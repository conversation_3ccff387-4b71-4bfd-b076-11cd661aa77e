# AIGC Server 编译指南

本文档描述了如何在 Linux 环境下编译 AIGC Server。

## 环境要求

- Linux 操作系统（推荐 Ubuntu 18.04 或更高版本）
- Go 1.19 或更高版本
- GCC/G++ 编译器（支持 C++14）
- Make 工具

## 编译步骤

### 1. 准备 SDK

确保 ByteRTC SDK 已正确放置在项目的 `sdk` 目录下：

```bash
sdk/
└── VolcEngineRTC_Linux_3.60.1.29857238_x86_64_Release/
    ├── include/    # SDK 头文件
    └── lib/        # SDK 动态库文件
```

### 2. 准备动态库

确保所有必需的动态库文件已经复制到项目根目录的 `lib` 目录下：

```bash
lib/
├── libVolcEngineRTC.so
├── libVolcEngineRTCWrapper.so
├── libRTCFFmpeg.so
├── libatomic.so
├── libbytertc_fdk-aac_extension.so
├── libbytertc_ffmpeg_audio_extension.so
├── libbytertc_nico_extension.so
├── libbytertc_vp8codec_extension.so
└── libbytenn.so
```

### 3. 编译项目

在项目根目录下执行以下命令：

```bash
# 清理之前的构建产物
make clean

# 编译项目
make build
```

编译完成后，可执行文件 `aigc_server` 将生成在项目根目录下。

### 4. 运行程序

编译完成后，可以通过以下命令运行程序：

```bash
# 使用默认配置运行
make run

# 使用指定环境配置运行
make run ENV=test
```

## 注意事项

1. 编译脚本已经配置了正确的环境变量，包括：
   - CGO 相关设置
   - 头文件路径
   - 动态库链接选项
   - rpath 设置（确保运行时能找到动态库）

2. 编译生成的可执行文件依赖于 `lib` 目录中的动态库，部署时需要确保 `lib` 目录与可执行文件位于同一目录。

3. 如果遇到编译错误，请检查：
   - SDK 文件是否完整
   - 动态库是否已正确复制到 `lib` 目录
   - 系统是否安装了所需的编译工具和依赖
