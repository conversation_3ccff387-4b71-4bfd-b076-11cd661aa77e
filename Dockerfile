# 构建阶段
FROM golang:1.24.2-alpine AS builder

# 安装基本工具
RUN apk add --no-cache gcc musl-dev

# 设置工作目录
WORKDIR /app

# 复制 go.mod 和 go.sum
COPY go.mod ./
COPY go.sum ./

# 复制 SDK 目录 (bytedance/bytertc 依赖)
COPY sdk/ ./sdk/

# 复制 proto 目录
COPY proto/ ./proto/

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 设置构建参数
ARG VERSION=dev
ARG BUILD_TIME=unknown
ARG GIT_COMMIT=unknown

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -ldflags="-X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'main.GitCommit=${GIT_COMMIT}'" -o aigc_server .

# 运行阶段
FROM alpine:latest

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 创建非 root 用户
RUN adduser -D -g '' aigcuser

# 创建工作目录和日志目录
WORKDIR /app
RUN mkdir -p /app/logs && chown -R aigcuser:aigcuser /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/aigc_server /app/
COPY --from=builder /app/configs /app/configs

# 容器内需要运行的SDK库
COPY --from=builder /app/sdk /app/sdk

# 设置用户
USER aigcuser

# 设置环境变量
ENV AIGC_SERVER_ENV=prod

# 暴露端口 (HTTP和gRPC)
EXPOSE 8080 50051

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --spider -q http://localhost:8080/ping || exit 1

# 启动应用
ENTRYPOINT ["/app/aigc_server"]
CMD ["--env", "prod"]
