.PHONY: all build clean test run help

# 变量定义
MAIN_APP_NAME := aigc-server-main
DOLL_APP_NAME := aigc-server-doll
SDK_PATH := $(shell pwd)/sdk/VolcEngineRTC_Linux_3.60.1.29857238_x86_64_Release

# 默认目标
all: build

# 构建应用
build:
	@echo "构建应用..."
	@chmod +x scripts/build.sh
	@./scripts/build.sh

# 清理构建产物
clean:
	@echo "清理构建产物..."
	@rm -f $(MAIN_APP_NAME) $(DOLL_APP_NAME)
	@rm -f coverage.out coverage.html

# 运行测试
test:
	@echo "运行测试..."
	@chmod +x scripts/test.sh
	@./scripts/test.sh

# 运行主进程
run:
	@echo "运行主进程..."
	@./$(MAIN_APP_NAME) --env=$(ENV)

# 运行doll服务进程（用于测试）
run-doll:
	@echo "运行doll服务进程..."
	@./$(DOLL_APP_NAME) --env=$(ENV) --uid=test_uid --room_id=test_room

# 帮助信息
help:
	@echo "可用命令:"
	@echo "  make build    - 构建应用"
	@echo "  make clean    - 清理构建产物"
	@echo "  make test     - 运行测试"
	@echo "  make run      - 运行应用 (默认dev环境)"
	@echo "  make run ENV=test - 在test环境运行应用"
	@echo "  make help     - 显示帮助信息"
