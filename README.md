# AIGC Server

基于Go语言的gRPC服务器，用于AIGC（AI生成内容）服务。

## 功能特性

- 基于gRPC的服务架构
- 支持多环境配置（开发、测试、生产）
- 高性能日志系统（基于zap）
- 优雅的启动和关闭
- 完整的构建和测试脚本

## 目录结构

```
aigc_server/
├── cmd/                    # 命令行入口
│   └── server/             # 服务器启动命令
├── configs/                # 配置文件
│   ├── dev.yaml            # 开发环境配置
│   ├── test.yaml           # 测试环境配置
│   └── prod.yaml           # 生产环境配置
├── internal/               # 内部代码，不对外暴露
│   ├── config/             # 配置加载
│   ├── server/             # 服务器实现
│   └── service/            # 业务逻辑实现
├── pkg/                    # 可重用的公共包
│   ├── logger/             # 日志工具
│   └── utils/              # 通用工具函数
├── proto/                  # 协议定义
│   └── aigc/               # AIGC服务协议
│       └── v1/             # 版本1
├── scripts/                # 脚本文件
│   ├── build.sh            # 构建脚本
│   ├── test.sh             # 测试脚本
│   └── proto_gen.sh        # 协议生成脚本
├── .gitignore              # Git忽略文件
├── go.mod                  # Go模块定义
├── go.sum                  # Go依赖校验
├── Makefile                # 构建工具
└── README.md               # 项目说明
```

## 快速开始

### 前置条件

- Go 1.16+
- Protocol Buffers 编译器 (protoc)
- Git

### 安装依赖

```bash
# 安装Go依赖
go mod tidy

# 安装protoc Go插件
go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
```

### 生成Proto代码

```bash
make proto
```

### 构建应用

```bash
make build
```

### 运行应用

```bash
# 开发环境
make run

# 测试环境
make run ENV=test

# 生产环境
make run ENV=prod
```

### 运行测试

```bash
make test
```

## 配置说明

配置文件位于`configs/`目录下，按环境分为`dev.yaml`、`test.yaml`和`prod.yaml`。

可以通过环境变量覆盖配置，例如：

```bash
AIGC_GRPC_PORT=50052 make run
```

## 许可证

[MIT](LICENSE)
