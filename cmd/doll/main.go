package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	workerIPC "aigc_server/internal/worker/ipc"
	"aigc_server/pkg/logger"

	"go.uber.org/zap"
)

var (
	// 命令行参数
	env    = flag.String("env", "prod", "运行环境: prod")
	uid    = flag.String("uid", "", "用户ID")
	roomID = flag.String("room_id", "", "房间ID")

	// 版本信息（通过编译时注入）
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"

	// IPC管理器
	ipcManager *ipc.IPCManager
)

func main() {
	flag.Parse()

	if *uid == "" {
		fmt.Println("请提供用户ID: --uid")
		os.Exit(1)
	}

	if *roomID == "" {
		fmt.Println("请提供房间ID: --room_id")
		os.Exit(1)
	}

	cfg, err := config.Load(*env)
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	if err := logger.Init(&logger.Config{
		Level:    cfg.Log.Level,
		Format:   cfg.Log.Format,
		Output:   cfg.Log.Output,
		FilePath: cfg.Log.FilePath,
	}); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	// 初始化Redis和IPC
	if err := ipc.InitRedisAndIPC(cfg); err != nil {
		logger.Fatal("初始化IPC失败", zap.Error(err))
	}

	// 创建worker进程IPC管理器
	workerProcessID := ipc.GetWorkerProcessID(*uid)
	workerHandler := &workerIPC.WorkerMessageHandler{}
	ipcManager = ipc.NewIPCManager(workerProcessID, workerHandler)

	// 启动上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动IPC消息循环
	ipcManager.Start(ctx)
	defer ipcManager.Stop()

	// 向主进程发送启动状态
	startData := map[string]interface{}{
		"state":       "starting",
		"uid":         *uid,
		"room_id":     *roomID,
		"description": "Worker进程正在启动",
	}
	if err := ipcManager.SendMessage(ctx, ipc.ProcessIDMain, "status", startData); err != nil {
		logger.Error("发送启动状态失败", zap.Error(err))
	}

	logger.Info("worker进程启动",
		zap.String("uid", *uid),
		zap.String("room_id", *roomID),
		zap.String("process_id", workerProcessID),
	)

	// 设置信号处理
	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		sig := <-sigCh
		logger.Info("worker进程接收到信号", zap.String("signal", sig.String()))

		// 向主进程发送停止状态
		stopData := map[string]interface{}{
			"state":       "stopped",
			"uid":         *uid,
			"description": "Worker进程正在停止",
		}
		if err := ipcManager.SendMessage(context.Background(), ipc.ProcessIDMain, "status", stopData); err != nil {
			logger.Error("发送停止状态失败", zap.Error(err))
		}

		cancel()
	}()

	// 这里可以添加worker进程的主要业务逻辑
	runWorkerLogic(ctx)

	<-ctx.Done()
	logger.Info("worker进程已停止")
}

func runWorkerLogic(ctx context.Context) {
	// 发送运行状态
	runningData := map[string]interface{}{
		"state":       "running",
		"uid":         *uid,
		"description": "Worker进程正在运行",
	}
	if err := ipcManager.SendMessage(ctx, ipc.ProcessIDMain, "status", runningData); err != nil {
		logger.Error("发送运行状态失败", zap.Error(err))
	}

	// 这里添加具体的worker业务逻辑
	logger.Info("worker进程业务逻辑开始执行")

	// 示例：定期发送心跳
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				heartbeatData := map[string]interface{}{
					"event":       "heartbeat",
					"uid":         *uid,
					"timestamp":   time.Now().Unix(),
					"description": "Worker进程心跳",
				}
				if err := ipcManager.SendMessage(ctx, ipc.ProcessIDMain, "event", heartbeatData); err != nil {
					logger.Error("发送心跳失败", zap.Error(err))
				}
			}
		}
	}()
}
