package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	mainIPC "aigc_server/internal/main/ipc"
	"aigc_server/internal/main/tcp"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

var (
	// 命令行参数
	env = flag.String("env", "prod", "运行环境: prod")

	// 版本信息（通过编译时注入）
	Version   = "dev"
	BuildTime = "unknown"
	GitCommit = "unknown"

	// 子进程引用
	childProcesses = make(map[string]*os.Process)
	childMutex     = &sync.Mutex{}

	// IPC管理器
	ipcManager *ipc.IPCManager
)

func main() {
	flag.Parse()

	cfg, err := config.Load(*env)
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	if err := logger.Init(&logger.Config{
		Level:    cfg.Log.Level,
		Format:   cfg.Log.Format,
		Output:   cfg.Log.Output,
		FilePath: cfg.Log.FilePath,
	}); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	// 初始化Redis和IPC
	if err := ipc.InitRedisAndIPC(cfg); err != nil {
		logger.Fatal("初始化IPC失败", zap.Error(err))
	}

	// 创建TCP服务器（需要在IPC管理器之前创建）
	tcpServer := tcp.NewTCPServer(cfg, nil, startDollProcess)

	// 创建主进程IPC管理器
	mainHandler := mainIPC.NewMainMessageHandler(tcpServer)
	ipcManager = ipc.NewIPCManager(ipc.ProcessIDMain, mainHandler)

	// 设置TCP服务器的IPC管理器
	tcpServer = tcp.NewTCPServer(cfg, ipcManager, startDollProcess)

	// 启动上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动IPC消息循环
	ipcManager.Start(ctx)
	defer ipcManager.Stop()

	logger.Info("主进程IPC初始化完成")

	// 运行主进程
	runMainProcess(cfg, ctx, cancel, tcpServer)
}

// runMainProcess 运行主进程
func runMainProcess(cfg *config.Config, ctx context.Context, cancel context.CancelFunc, tcpServer *tcp.TCPServer) {

	// 启动worker进程
	workerUID := "default"
	workerProcessID := ipc.GetWorkerProcessID(workerUID)

	// 向worker发送启动命令
	startData := map[string]interface{}{
		"uid":    workerUID,
		"config": cfg,
	}

	if err := ipcManager.SendMessage(ctx, workerProcessID, "command", startData); err != nil {
		logger.Error("发送启动命令失败", zap.Error(err))
	}

	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		sig := <-sigCh
		logger.Info("主进程接收到信号", zap.String("signal", sig.String()))
		cancel()
	}()

	go func() {
		logger.Info("启动TCP服务器（主进程）",
			zap.String("host", cfg.TCP.Host),
			zap.Int("port", cfg.TCP.Port),
		)
		if err := tcpServer.Start(ctx); err != nil {
			logger.Error("TCP服务器启动失败", zap.Error(err))
			cancel()
		}
	}()

	defer closeAllChildProcesses()

	<-ctx.Done()

	if err := tcpServer.Stop(); err != nil {
		logger.Error("TCP服务器关闭失败", zap.Error(err))
	}
	logger.Info("TCP服务器已关闭（主进程）")
}

// startDollProcess 启动doll服务子进程的回调函数
func startDollProcess(uid, roomID string) error {
	processKey := utils.GetProcessKey(uid)

	childMutex.Lock()
	if _, exists := childProcesses[processKey]; exists {
		logger.Info("子进程已存在，关闭",
			zap.String("processKey", processKey),
			zap.String("uid", uid))

		// 通过IPC发送停止命令给worker进程
		workerProcessID := ipc.GetWorkerProcessID(uid)
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		stopData := map[string]interface{}{"reason": "process_restart"}
		if err := ipcManager.SendMessage(ctx, workerProcessID, "stop", stopData); err != nil {
			logger.Error("发送停止命令失败", zap.Error(err))
		}
		cancel()

		err := utils.CloseProcess(childProcesses[processKey].Pid, 5*time.Second)
		if err != nil {
			logger.Error("关闭子进程Error", zap.Error(err))
		}
		delete(childProcesses, processKey)
	}
	childMutex.Unlock()

	dollPath, err := getDollExecutablePath()
	if err != nil {
		logger.Error("获取doll可执行文件路径失败", zap.Error(err))
		return err
	}

	cmd := exec.Command(
		dollPath,
		"--env", *env,
		"--uid", uid,
		"--room_id", roomID,
	)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	logger.Info("正在启动doll服务子进程...",
		zap.String("path", dollPath),
		zap.String("processKey", processKey),
		zap.String("uid", uid),
		zap.String("room_id", roomID),
	)
	if err := cmd.Start(); err != nil {
		logger.Error("启动doll服务子进程失败", zap.Error(err))
		return err
	}

	logger.Info("doll服务子进程已启动",
		zap.Int("pid", cmd.Process.Pid),
		zap.String("processKey", processKey),
	)

	childMutex.Lock()
	childProcesses[processKey] = cmd.Process
	childMutex.Unlock()

	go func() {
		if err := cmd.Wait(); err != nil {
			logger.Error("doll服务子进程异常退出",
				zap.Error(err),
				zap.String("processKey", processKey),
			)
		}

		childMutex.Lock()
		delete(childProcesses, processKey)
		childMutex.Unlock()
	}()

	return nil
}

// getDollExecutablePath 获取doll进程可执行文件路径
func getDollExecutablePath() (string, error) {
	// 首先尝试在同一目录下查找doll可执行文件
	execPath, err := os.Executable()
	if err != nil {
		return "", err
	}

	// 假设doll可执行文件与main在同一目录
	dollPath := execPath + "-doll"
	if _, err := os.Stat(dollPath); err == nil {
		return dollPath, nil
	}

	// 如果找不到，尝试使用相对路径
	dollPath = "./aigc-server-doll"
	if _, err := os.Stat(dollPath); err == nil {
		return dollPath, nil
	}

	// 最后尝试在PATH中查找
	dollPath, err = exec.LookPath("aigc-server-doll")
	if err != nil {
		return "", fmt.Errorf("找不到doll可执行文件")
	}

	return dollPath, nil
}

// closeAllChildProcesses 关闭所有子进程
func closeAllChildProcesses() {
	logger.Info("正在关闭所有子进程...")

	childMutex.Lock()
	processes := make(map[string]*os.Process)
	for id, process := range childProcesses {
		processes[id] = process
	}
	childMutex.Unlock()

	var wg sync.WaitGroup
	for id, process := range processes {
		wg.Add(1)
		go func(id string, process *os.Process) {
			defer wg.Done()

			logger.Info("正在关闭子进程",
				zap.String("id", id),
				zap.Int("pid", process.Pid),
			)

			if err := utils.CloseProcess(process.Pid, 5*time.Second); err != nil {
				logger.Error("关闭子进程失败",
					zap.String("id", id),
					zap.Int("pid", process.Pid),
					zap.Error(err),
				)
			} else {
				logger.Info("子进程已关闭",
					zap.String("id", id),
					zap.Int("pid", process.Pid),
				)
			}

			childMutex.Lock()
			delete(childProcesses, id)
			childMutex.Unlock()
		}(id, process)
	}

	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		logger.Info("所有子进程已关闭")
	case <-time.After(10 * time.Second):
		logger.Error("部分子进程可能未正常关闭")
	}
}
