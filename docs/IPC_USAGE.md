# 进程间通信(IPC)使用指南

本项目集成了基于 Redis 的进程间通信模块，实现主进程和 worker 进程之间的可靠消息传递。

## 架构概述

```
主进程 (main)
    ↕ Redis队列
Worker进程 (worker:uid)
```

- 主进程 ID: `main`
- Worker 进程 ID: `worker:{uid}` (例如: `worker:user123`)
- 队列名称: `aigc:ipc:{process_id}`

## 功能特性

1. **可靠消息传递**: 基于 Redis 队列，支持持久化
2. **多种消息类型**: 命令、状态、事件、错误、日志等
3. **自动重连**: Redis 连接异常时自动重试
4. **消息路由**: 支持注册自定义消息处理器
5. **队列管理**: 支持查看队列长度、清空队列等操作

## 快速开始

### 1. 配置 Redis

在配置文件中添加 Redis 配置:

```yaml
redis:
  host: "127.0.0.1"
  port: 6379
  password: ""
  database: 0
  pool_size: 10
  min_idle_conns: 2
  max_retries: 3
```

### 2. 初始化 IPC

```go
import (
    "aigc_server/internal/config"
    "aigc_server/internal/ipc"
)

// 加载配置
cfg, err := config.Load("prod")
if err != nil {
    log.Fatal(err)
}

// 初始化Redis和IPC
mainIPC, err := ipc.InitRedisAndIPC(cfg, ipc.ProcessIDMain)
if err != nil {
    log.Fatal(err)
}
defer redis.Close()
```

### 3. 启动消息循环

```go
ctx, cancel := context.WithCancel(context.Background())
defer cancel()

// 启动消息循环
go mainIPC.StartMessageLoop(ctx)
```

## 消息类型

### 1. 命令消息 (Command)

主进程向 worker 进程发送控制命令。

```go
// 发送停止命令
err := mainIPC.SendCommand(ctx, workerProcessID, "stop", nil)

// 发送带参数的命令
params := map[string]interface{}{
    "volume": 0.8,
    "speed": 1.2,
}
err := mainIPC.SendCommand(ctx, workerProcessID, "adjust_audio", params)
```

### 2. 状态消息 (Status)

Worker 进程向主进程报告运行状态。

```go
// 发送状态更新
metrics := map[string]interface{}{
    "cpu_usage": 45.6,
    "memory_mb": 128.5,
}
err := workerIPC.SendStatus(ctx, ipc.ProcessIDMain, "running", "正在处理音频", 0.6, metrics)
```

### 3. 事件消息 (Event)

Worker 进程向主进程发送业务事件。

```go
// 发送事件通知
eventData := map[string]interface{}{
    "audio_duration": 1500,
    "format": "wav",
}
err := workerIPC.SendEvent(ctx, ipc.ProcessIDMain, "audio_processed", "音频处理完成", eventData)
```

### 4. 错误消息 (Error)

Worker 进程向主进程报告错误。

```go
// 发送错误报告
err := workerIPC.SendError(ctx, ipc.ProcessIDMain, "AUDIO_ERROR", "音频解码失败", "不支持的音频格式", true)
```

## 自定义消息处理器

### 1. 实现 MessageHandler 接口

```go
type CustomHandler struct{}

func (h *CustomHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
    // 处理消息逻辑
    logger.Info("收到自定义消息", zap.Any("data", msg.Data))
    return nil
}

func (h *CustomHandler) GetHandlerType() string {
    return "CustomHandler"
}
```

### 2. 注册处理器

```go
// 注册自定义处理器
mainIPC.RegisterHandler(ipc.MessageTypeStatus, &CustomHandler{})
```

## 内置处理器

### 主进程处理器

- `MainStatusHandler`: 处理 worker 状态更新
- `MainEventHandler`: 处理 worker 事件通知
- `MainErrorHandler`: 处理 worker 错误报告

### Worker 进程处理器

- `WorkerCommandHandler`: 处理主进程命令
- `WorkerConfigHandler`: 处理配置更新
- `WorkerStopHandler`: 处理停止信号

## 队列管理

```go
// 获取队列长度
length, err := mainIPC.GetQueueLength(ctx, workerProcessID)

// 清空队列
err := mainIPC.ClearQueue(ctx, workerProcessID)
```

## 最佳实践

### 1. 错误处理

```go
// 设置超时
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

if err := mainIPC.SendCommand(ctx, workerProcessID, "action", params); err != nil {
    logger.Error("发送命令失败", zap.Error(err))
    // 处理发送失败的情况
}
```

### 2. 优雅关闭

```go
// 发送停止命令
err := mainIPC.SendCommand(ctx, workerProcessID, "stop", nil)
if err != nil {
    logger.Error("发送停止命令失败", zap.Error(err))
}

// 等待进程正常退出
time.Sleep(5 * time.Second)

// 强制结束进程
utils.CloseProcess(pid, 5*time.Second)
```

### 3. 消息去重

每个消息都有唯一 ID，可以用于去重：

```go
func (h *CustomHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
    // 检查消息是否已处理
    if h.isProcessed(msg.ID) {
        return nil
    }

    // 标记消息已处理
    h.markProcessed(msg.ID)

    // 处理消息
    return h.processMessage(msg)
}
```

## 测试

运行 IPC 测试示例：

```bash
go run examples/ipc_test/main.go
```

确保 Redis 服务正在运行，然后运行测试程序查看消息传递效果。

## 故障排除

### 1. Redis 连接失败

- 检查 Redis 服务是否运行
- 检查连接配置是否正确
- 检查网络连接和防火墙设置

### 2. 消息发送失败

- 检查 Redis 连接状态
- 检查目标进程 ID 是否正确
- 检查消息格式是否正确

### 3. 消息处理失败

- 检查是否注册了对应的消息处理器
- 检查处理器逻辑是否有错误
- 查看错误日志获取详细信息

## 监控和调试

建议在生产环境中监控以下指标：

1. **队列长度**: 避免消息积压
2. **消息处理延迟**: 监控消息处理性能
3. **错误率**: 监控消息发送和处理的错误率
4. **Redis 连接状态**: 监控 Redis 连接健康状况

通过日志可以跟踪消息的发送和处理情况：

```
2024-01-01 10:00:01 INFO 消息发送成功 from=main to=worker:user123 type=command id=20240101100001-abc123
2024-01-01 10:00:02 INFO 消息接收成功 from=main to=worker:user123 type=command id=20240101100001-abc123
2024-01-01 10:00:02 INFO 开始处理消息 type=command id=20240101100001-abc123 handler=WorkerCommandHandler
2024-01-01 10:00:02 INFO 消息处理成功 type=command id=20240101100001-abc123 duration=100ms
```
