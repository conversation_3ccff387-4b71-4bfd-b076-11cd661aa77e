# 简化版 IPC 系统使用指南

## 概述

本项目的 IPC（进程间通信）系统已经简化，现在只有一种基本的消息类型。所有的 send/recv/handler 都统一处理这种消息，您可以根据 messageType 字段自行实现不同的业务逻辑。

## 核心组件

### 1. Message 消息结构

```go
type Message struct {
    ID        string                 `json:"id"`        // 消息ID
    Type      string                 `json:"type"`      // 消息类型
    From      string                 `json:"from"`      // 发送者标识
    To        string                 `json:"to"`        // 接收者标识
    Timestamp int64                  `json:"timestamp"` // 时间戳
    Data      map[string]interface{} `json:"data"`      // 消息数据
}
```

### 2. MessageHandler 接口

```go
type MessageHandler interface {
    HandleMessage(ctx context.Context, msg *Message) error
}
```

### 3. IPCManager 管理器

```go
type IPCManager struct {
    QueueManager *QueueManager
    Router       *MessageRouter
    ProcessID    string
}
```

## 基本用法

### 1. 初始化 IPC 系统

```go
// 初始化Redis和IPC
if err := ipc.InitRedisAndIPC(cfg); err != nil {
    logger.Fatal("初始化IPC失败", zap.Error(err))
}
```

### 2. 创建消息处理器

```go
type MyHandler struct {
    processID string
}

func (h *MyHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
    // 根据消息类型处理不同的业务逻辑
    switch msg.Type {
    case "command":
        return h.handleCommand(ctx, msg)
    case "status":
        return h.handleStatus(ctx, msg)
    case "event":
        return h.handleEvent(ctx, msg)
    default:
        logger.Warn("未知的消息类型", zap.String("type", msg.Type))
    }
    return nil
}

func (h *MyHandler) handleCommand(ctx context.Context, msg *ipc.Message) error {
    // 处理命令消息
    action, ok := msg.Data["action"].(string)
    if !ok {
        return fmt.Errorf("无效的命令格式")
    }

    switch action {
    case "start":
        // 启动逻辑
    case "stop":
        // 停止逻辑
    }
    return nil
}
```

### 3. 创建 IPC 管理器

```go
// 创建处理器
handler := &MyHandler{processID: "main"}

// 创建IPC管理器
ipcManager := ipc.NewIPCManager("main", handler)

// 启动消息循环
ctx, cancel := context.WithCancel(context.Background())
defer cancel()

ipcManager.Start(ctx)
defer ipcManager.Stop()
```

### 4. 发送消息

```go
// 发送命令消息
commandData := map[string]interface{}{
    "action": "start",
    "timeout": 30,
}

err := ipcManager.SendMessage(ctx, "worker:uid123", "command", commandData)
if err != nil {
    logger.Error("发送消息失败", zap.Error(err))
}

// 发送状态消息
statusData := map[string]interface{}{
    "state": "running",
    "progress": 0.8,
    "message": "任务执行中",
}

err = ipcManager.SendMessage(ctx, "main", "status", statusData)
```

## 常用消息类型

虽然系统只有一种 Message 结构，但建议使用以下约定的消息类型：

### 1. 命令消息 (command)

```go
data := map[string]interface{}{
    "action": "start|stop|pause|resume",
    "params": map[string]interface{}{...},
}
```

### 2. 状态消息 (status)

```go
data := map[string]interface{}{
    "state": "starting|running|stopped|error",
    "progress": 0.5,
    "message": "状态描述",
}
```

### 3. 事件消息 (event)

```go
data := map[string]interface{}{
    "event": "task_completed",
    "timestamp": time.Now().Unix(),
    "description": "事件描述",
    "metadata": map[string]interface{}{...},
}
```

### 4. 错误消息 (error)

```go
data := map[string]interface{}{
    "code": "ERROR_CODE",
    "message": "错误消息",
    "details": "详细信息",
    "recoverable": true,
}
```

### 5. 日志消息 (log)

```go
data := map[string]interface{}{
    "level": "info|warn|error",
    "message": "日志消息",
    "fields": map[string]interface{}{...},
}
```

## 进程 ID 约定

```go
// 主进程
const ProcessIDMain = "main"

// Worker进程
func GetWorkerProcessID(uid string) string {
    return fmt.Sprintf("worker:%s", uid)
}
```

## 完整示例

参考 `examples/simple_ipc/main.go` 文件，这个示例展示了：

1. 如何初始化 IPC 系统
2. 如何创建自定义消息处理器
3. 如何发送不同类型的消息
4. 如何在处理器中区分消息类型

## 主要优势

1. **简化的 API**: 只有一种消息类型，减少了复杂性
2. **灵活的数据结构**: 使用 `map[string]interface{}` 支持任意数据
3. **统一的处理方式**: 所有消息通过同一个接口处理
4. **易于扩展**: 通过 messageType 字段轻松添加新的消息类型
5. **向后兼容**: 可以平滑迁移现有代码

## 迁移指南

如果您正在从旧的多类型消息系统迁移，请：

1. 将所有具体的消息处理器合并为一个通用处理器
2. 在处理器中使用 switch 语句根据 msg.Type 分发逻辑
3. 将原来的强类型数据结构改为 map 形式
4. 更新发送消息的代码使用新的 SendMessage 方法

## 注意事项

1. 消息数据使用 JSON 序列化，确保 Data 字段中的值可序列化
2. 处理器中要做好错误处理和类型断言
3. 建议为不同的消息类型定义常量，避免硬编码字符串
4. 在生产环境中要做好消息的监控和日志记录
