# TCP服务器集成指南

## 概述

本项目已将TCP服务器集成到主进程中，替代了原有的HTTP服务器。TCP服务器负责监听客户端连接，处理娃娃进入房间请求，并通过IPC与子进程通信。

## 架构设计

### 目录结构
```
internal/main/tcp/
├── server.go      # TCP服务器主要实现
├── handlers.go    # TCP消息处理器
└── protocol.go    # TCP协议相关（序列化/反序列化）
```

### 工作流程

1. **主进程启动**：
   - 启动TCP服务器监听指定端口（默认8970）
   - 初始化IPC管理器用于与子进程通信

2. **客户端连接**：
   - TCP服务器接受客户端连接
   - 为每个连接创建ConnectionHandler处理器

3. **消息处理**：
   - 解析TCP协议数据包
   - 根据消息类型分发到相应的处理函数
   - 支持娃娃进入房间、设置、打断等请求

4. **子进程管理**：
   - 收到进入房间请求后启动对应的子进程
   - 通过IPC转发后续的TCP请求到子进程
   - 子进程可通过IPC发送响应回主进程，再转发给TCP客户端

## TCP协议格式

### 数据包结构
```
+--------+--------+--------+--------+--------+--------+--------+--------+
| Header | Header | Header |  XOR   |      Body Length (Little Endian)     |
|  0xAB  |  0xAB  |  0xAB  | Check  |   Byte0  Byte1  Byte2  Byte3        |
+--------+--------+--------+--------+--------+--------+--------+--------+
|                           Body Data                                   |
+-----------------------------------------------------------------------+
```

### 音频数据包
音频数据使用特殊的头部标识：
```
+--------+--------+--------+--------+--------+--------+--------+--------+
|  0xBB  |  0xBB  |  0xBB  |  0xBB  |      Body Length (Little Endian)     |
+--------+--------+--------+--------+--------+--------+--------+--------+
|                           Audio Data                                  |
+-----------------------------------------------------------------------+
```

## 支持的消息类型

### 1. 娃娃进入房间请求
```json
{
  "dollId": "doll_001"
}
```

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "dollId": "doll_001",
    "roomId": "room_xxx",
    "roomToken": "token_xxx"
  }
}
```

### 2. 娃娃设置请求
```json
{
  "dollId": "doll_001",
  "battery": "80",
  "volume": "50"
}
```

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "volume": "60"
  }
}
```

### 3. 娃娃打断请求
```json
{
  "dollId": "doll_001",
  "interrupt": true
}
```

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "interrupt": true
  }
}
```

## 配置

### TCP服务器配置
在 `configs/prod.yaml` 中配置：
```yaml
tcp:
  port: 8970
  host: "0.0.0.0"
  max_connections: 1000
  read_timeout: 30s
  write_timeout: 30s
```

## IPC通信

### 消息类型
- `tcp_request`: 主进程转发TCP请求到子进程
- `tcp_response`: 子进程响应转发到TCP客户端

### 消息格式
```json
{
  "type": "tcp_request",
  "doll_id": "doll_001",
  "request_type": "audio_data|doll_settings|doll_interrupt",
  "data": {...}
}
```

## 测试

### 编译和运行
```bash
# 编译主进程
go build -o bin/aigc-server-main cmd/main/main.go

# 编译子进程
go build -o bin/aigc-server-doll cmd/doll/main.go

# 运行主进程
./bin/aigc-server-main --env prod
```

### 测试客户端
```bash
# 编译测试客户端
go build -o bin/tcp-client-test test/tcp_client_test.go

# 运行测试
./bin/tcp-client-test
```

## 与原HTTP服务器的差异

1. **协议**：从HTTP改为自定义TCP协议
2. **连接**：支持长连接，可以持续发送音频数据
3. **性能**：TCP协议开销更小，适合实时音频传输
4. **功能**：保持了原有的房间进入功能，增加了音频数据传输

## 注意事项

1. **连接管理**：TCP服务器会限制最大连接数，超过限制会拒绝新连接
2. **超时处理**：支持读写超时配置，防止连接阻塞
3. **错误处理**：包含完整的错误处理和日志记录
4. **优雅关闭**：支持优雅关闭，会等待所有连接处理完成

## 后续扩展

1. **加密支持**：可在protocol.go中添加消息加密/解密功能
2. **认证机制**：可添加客户端认证逻辑
3. **负载均衡**：可支持多个TCP服务器实例
4. **监控指标**：可添加连接数、消息量等监控指标
