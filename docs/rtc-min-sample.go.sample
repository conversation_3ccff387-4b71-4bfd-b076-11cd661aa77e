package main

import (
        "bytedance/bytertc/rtcengine"
        "fmt"
)

// callback handler（如果你的callback 中embeding了默认回调实现，则只需要关注实现自己需要的回调； 否则，应该实现全部回调）
type rtcEngineEventHandler struct {
        rtcengine.IRTCEngineEventHandlerDefaultImpl
}

type rtcRoomEventHandler struct {
        rtcengine.IRTCRoomEventHandlerDefaultImpl
}

const (
        appID     = TODO:      // 填写申请的应用 appID
        userID    = "linuxgo"  // 填写 SDK 客户端进入房间的用户 ID
        roomToken = TODO:      // 填写 SDK 客户端加入房间的鉴权 token
        roomID    = "gotest"   // 填写 SDK 客户端进入房间的房间 ID
)

func main() {
        // config
        engineParameters := make(map[string]interface{})
        engineConfig := &rtcengine.EngineConfig{AppID: appID, Parameters: engineParameters} // 字符串形式
        engineEventHandler := &rtcEngineEventHandler{}

        // create engine
        engine := rtcengine.CreateRTCEngine(engineConfig, engineEventHandler)
        fmt.Println("RTC engine creating. app ID:", appID, ", parameters:", engineParameters, ", handler:", engineEventHandler)

        // join room
        room := engine.CreateRTCRoom(roomID)
        roomEventHandler := &rtcRoomEventHandler{}
        room.SetRTCRoomEventHandler(roomEventHandler)
        userInfo := &rtcengine.UserInfo{UID: userID, ExtraInfo: ""}
        roomConfig := &rtcengine.RTCRoomConfig{IsPublishAudio: true, IsPublishVideo: true, IsAutoSubscribeAudio: true, IsAutoSubscribeVideo: true}
        room.JoinRoom(roomToken, userInfo, roomConfig)
  
        room.Destroy()
        rtcengine.DestroyRTCEngine()
}
