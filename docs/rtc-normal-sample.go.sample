package main

import (
	"bytedance/bytertc/rtcengine"
	"encoding/json"
	"fmt"
	"math"
	"os"
	"strconv"
	"sync"
	"sync/atomic"
)

const configFilepath = "config.json"

var (
	appID            = ""
	userID           = ""
	roomToken        = ""
	roomID           = ""
	engineParameters = make(map[string]interface{})
)

type rtcEngineEventHandler struct {
	rtcengine.IRTCEngineEventHandlerDefaultImp
}

type rtcRoomEventHandler struct {
	rtcengine.IRTCRoomEventHandlerDefaultImpl
}

type audioFrameObserver struct {
	rtcengine.IAudioFrameObserverDefaultImp
	audioFrameChan            chan rtcengine.IAudioFrame
	oncePrintRemoteAudioFrame sync.Once
}

func (h *rtcRoomEventHandler) OnRoomStateChanged(roomId string, uid string, state int, extraInfo string) {
	fmt.Println("OnRoomStateChanged. room ID:", roomId, ", user ID:", uid, ", state:", state, ", extra info:", extraInfo)
	if state == 0 {
		fmt.Println("OnRoomStateChanged join room success")
	} else {
		fmt.Println("OnRoomStateChanged join room failed, error code:", state)
	}
}

func (h *rtcRoomEventHandler) OnUserJoined(userInfo rtcengine.UserInfo) {
	fmt.Println("OnUserJoined. user ID:", userInfo.UID, ", extra info:", userInfo.ExtraInfo)
}

func (h *audioFrameObserver) OnRemoteUserAudioFrame(streamInfo rtcengine.RemoteStreamKey, audioFrame rtcengine.IAudioFrame) {
	h.oncePrintRemoteAudioFrame.Do(func() {
		fmt.Println("OnRemoteUserAudioFrame. user ID:", streamInfo.UserID, ", room ID:", streamInfo.RoomID,
			", stream index:", streamInfo.StreamIndex, "audio frame timestamp:", audioFrame.TimestampUs(),
			",audio frame sample rate:", audioFrame.SampleRate(), ",audio frame channel:", audioFrame.Channel(),
			"first 10 bytes is: ", audioFrame.Data()[:10])
	})

	// TODO: audioFrame 只在回调函数体内有效, 需要拷贝转发到另一个协程异步处理
	audioFrameBuilder := rtcengine.AudioFrameBuilder{
		SampleRate: audioFrame.SampleRate(),
		Channel:    audioFrame.Channel(),
		Data:       audioFrame.Data(),
	}
	copiedAudioFrame := rtcengine.BuildAudioFrame(audioFrameBuilder)
	for {
		select {
		case h.audioFrameChan <- copiedAudioFrame:
			return
		default:
			fmt.Println("OnRemoteUserAudioFrame. audio frame channel is full, drop audio frame")
			return
		}
	}
}

func pushFixedFreqExternalAudioFrameWorker(engine rtcengine.IRTCEngine, isPushing *atomic.Bool) {
	audioFrameBuilder := rtcengine.AudioFrameBuilder{
		SampleRate: rtcengine.AudioSampleRate16000,
		Channel:    rtcengine.AudioChannelMono,
		Data:       generate10MsAudioFrameData(),
	}
	audioFrame := rtcengine.BuildAudioFrame(audioFrameBuilder)

	for isPushing.Load() {
		ok := engine.PushExternalAudioFrame(audioFrame)
		if ok != 0 {
			panic("push external audio frame api call error, error code" + strconv.Itoa(ok))
		}
		fmt.Println("pushing external audio frame..")
	}
}

func pushExternalAudioFrameWorker(engine rtcengine.IRTCEngine, audioFrameChan <-chan rtcengine.IAudioFrame) {
	for audioFrame := range audioFrameChan {
		if audioFrame == nil {
			break
		}
		// 业务处理逻辑
		fmt.Println(audioFrame.Channel(), audioFrame.SampleRate())
		ok := engine.PushExternalAudioFrame(audioFrame)
		if ok != 0 {
			panic("push external audio frame api call error, error code" + strconv.Itoa(ok))
		}
		fmt.Println("pushing external audio frame..")
	}
}

func generate10MsAudioFrameData() []byte {
	const sampleRate = 16000
	const durationMs = 10
	const frequencyHz = 440
	const amplitude = 32767
	numSamples := sampleRate * durationMs / 1000
	audioData := make([]byte, numSamples)
	for i := 0; i < numSamples; i++ {
		t := float64(i) / float64(sampleRate)
		value := amplitude * math.Sin(2*math.Pi*frequencyHz*t)
		audioData[i] = byte(value)
	}
	return audioData
}

func initConfig() {
	configValue, _ := os.ReadFile(configFilepath)
	var runningConfig map[string]interface{}
	err := json.Unmarshal(configValue, &runningConfig)
	if err != nil {
		panic("parse running config failed, " + err.Error())
	}
	fmt.Println(runningConfig)

	var ok bool
	appID = runningConfig["app_id"].(string)
	userID = runningConfig["user_id"].(string)
	roomToken = runningConfig["room_token"].(string)
	roomID = runningConfig["room_id"].(string)
	engineParameters, ok = runningConfig["engine_parameters"].(map[string]interface{})
	if !ok {
		engineParameters = make(map[string]interface{})
	}
}

var scaned string

func main() {
	// config
	initConfig()
	engineConfig := &rtcengine.EngineConfig{AppID: appID, Parameters: engineParameters}
	engineEventHandler := &rtcEngineEventHandler{}

	// create engine
	engine := rtcengine.CreateRTCEngine(engineConfig, engineEventHandler)
	fmt.Println("RTC engine creating. app ID:", appID, ", parameters:", engineParameters, ", handler:", engineEventHandler)

	// join room
	room := engine.CreateRTCRoom(roomID)
	// set room handler
	roomEventHandler := &rtcRoomEventHandler{}
	ok := room.SetRTCRoomEventHandler(roomEventHandler)
	userInfo := &rtcengine.UserInfo{UID: userID, ExtraInfo: ""}
	roomConfig := &rtcengine.RTCRoomConfig{IsPublishAudio: true, IsPublishVideo: true, IsAutoSubscribeAudio: true, IsAutoSubscribeVideo: true}
	ok = room.JoinRoom(roomToken, userInfo, roomConfig)
	if ok != 0 {
		panic("join room api call error")
	}
	fmt.Println("RTC room joined. room ID:", roomID, ", user ID:", userID, ", token:", roomToken)

	// audio frame chan
	audioFrameChan := make(chan rtcengine.IAudioFrame)
	audioFrameObserver := &audioFrameObserver{audioFrameChan: audioFrameChan}

	// register audio frame observer
	ok = engine.RegisterAudioFrameObserver(audioFrameObserver)
	audioFormat := &rtcengine.AudioFormat{
		SampleRate:     rtcengine.AudioSampleRate48000,
		Channel:        rtcengine.AudioChannelMono,
		SamplesPerCall: 0}

	ok = engine.EnableAudioFrameCallback(rtcengine.AudioFrameCallbackMethodRemoteUser, audioFormat)
	if ok != 0 {
		panic("register audio frame observer api call error, error code" + strconv.Itoa(ok))
	}

	// push external audio frame
	engine.SetAudioSourceType(rtcengine.AudioSourceTypeExternal)
	isPushing := &atomic.Bool{}
	isPushing.Store(true)
	if true { // test push copied remote user audio frame
		go pushExternalAudioFrameWorker(engine, audioFrameObserver.audioFrameChan)
	} else { // test push fixed frequency audio frame
		go pushFixedFreqExternalAudioFrameWorker(engine, isPushing)
	}
	fmt.Println("Audio frame pushing.")
	fmt.Println("Enter to continue for stop pushing and leaving room...")
	fmt.Scanln(&scaned)
	isPushing.Store(false)
	audioFrameChan <- nil

	// leave room
	ok = room.LeaveRoom()
	if ok != 0 {
		panic("leave room api call error")
	}
	room.Destroy()
	close(audioFrameChan)

	fmt.Println("RTC room leaved. Enter to continue destroy engine...")
	fmt.Scanln(&scaned)

	// destroy engine
	rtcengine.DestroyRTCEngine()
	fmt.Println("RTC engine destroyed. Ending test.")
}
