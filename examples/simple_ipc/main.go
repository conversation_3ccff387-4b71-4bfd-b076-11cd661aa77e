package main

import (
	"context"
	"fmt"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	"aigc_server/pkg/logger"

	"go.uber.org/zap"
)

// SimpleHandler 简单的消息处理器
type SimpleHandler struct {
	processID string
}

func (h *SimpleHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
	fmt.Printf("[%s] 收到消息: ID=%s, Type=%s, From=%s, Data=%v\n",
		h.processID, msg.ID, msg.Type, msg.From, msg.Data)

	logger.Info("处理消息",
		zap.String("process", h.processID),
		zap.String("id", msg.ID),
		zap.Any("type", msg.Type),
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)

	return nil
}

func main() {
	// 初始化日志
	if err := logger.Init(&logger.Config{
		Level:  "info",
		Format: "console",
		Output: "stdout",
	}); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		return
	}
	defer logger.Sync()

	// 加载配置
	cfg, err := config.Load("prod")
	if err != nil {
		logger.Fatal("加载配置失败", zap.Error(err))
	}

	// 初始化Redis和IPC
	if err := ipc.InitRedisAndIPC(cfg); err != nil {
		logger.Fatal("初始化IPC失败", zap.Error(err))
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 创建主进程IPC管理器
	mainHandler := &SimpleHandler{processID: "main"}
	mainIPC := ipc.NewIPCManager("main", mainHandler)
	mainIPC.Start(ctx)
	defer mainIPC.Stop()

	// 创建worker进程IPC管理器
	workerHandler := &SimpleHandler{processID: "worker:test"}
	workerIPC := ipc.NewIPCManager("worker:test", workerHandler)
	workerIPC.Start(ctx)
	defer workerIPC.Stop()

	logger.Info("IPC示例开始运行...")

	// 主进程向worker发送命令
	commandData := map[string]interface{}{
		"action": "start",
		"params": map[string]interface{}{
			"timeout": 10,
			"retry":   3,
		},
	}

	if err := mainIPC.SendMessage(ctx, "worker:test", "command", commandData); err != nil {
		logger.Error("发送命令失败", zap.Error(err))
	} else {
		fmt.Println("✓ 主进程发送命令到worker")
	}

	// worker向主进程发送状态
	statusData := map[string]interface{}{
		"state":    "running",
		"progress": 0.5,
		"message":  "任务执行中",
	}

	if err := workerIPC.SendMessage(ctx, "main", "status", statusData); err != nil {
		logger.Error("发送状态失败", zap.Error(err))
	} else {
		fmt.Println("✓ worker发送状态到主进程")
	}

	// worker向主进程发送事件
	eventData := map[string]interface{}{
		"event":       "task_completed",
		"timestamp":   time.Now().Unix(),
		"description": "任务执行完成",
		"result": map[string]interface{}{
			"success": true,
			"output":  "处理完成",
		},
	}

	if err := workerIPC.SendMessage(ctx, "main", "event", eventData); err != nil {
		logger.Error("发送事件失败", zap.Error(err))
	} else {
		fmt.Println("✓ worker发送事件到主进程")
	}

	// 主进程向worker发送停止命令
	stopData := map[string]interface{}{
		"reason": "shutdown",
		"force":  false,
	}

	if err := mainIPC.SendMessage(ctx, "worker:test", "stop", stopData); err != nil {
		logger.Error("发送停止命令失败", zap.Error(err))
	} else {
		fmt.Println("✓ 主进程发送停止命令到worker")
	}

	// 等待消息处理
	time.Sleep(2 * time.Second)

	logger.Info("IPC示例运行完成")
	fmt.Println("\n=== IPC示例完成 ===")
	fmt.Println("✓ 消息类型简化为统一的Message结构")
	fmt.Println("✓ 发送/接收/处理器只处理一种消息类型")
	fmt.Println("✓ 根据messageType字段区分不同业务逻辑")
	fmt.Println("✓ 代码更加简洁和易于扩展")
}
