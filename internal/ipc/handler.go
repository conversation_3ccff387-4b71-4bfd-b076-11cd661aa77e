package ipc

import (
	"context"
	"time"

	"go.uber.org/zap"

	"aigc_server/pkg/logger"
)

// MessageHandler 消息处理器接口
type MessageHandler interface {
	HandleMessage(ctx context.Context, msg *Message) error
}

// MessageRouter 消息路由器
type MessageRouter struct {
	queueManager *QueueManager
	handler      MessageHandler
	stopChan     chan struct{}
	running      bool
}

// NewMessageRouter 创建消息路由器
func NewMessageRouter(queueManager *QueueManager, handler MessageHandler) *MessageRouter {
	return &MessageRouter{
		queueManager: queueManager,
		handler:      handler,
		stopChan:     make(chan struct{}),
	}
}

// Start 启动消息循环
func (mr *MessageRouter) Start(ctx context.Context) {
	if mr.running {
		return
	}

	mr.running = true
	logger.Info("启动消息路由器")

	go func() {
		defer func() {
			mr.running = false
			logger.Info("消息路由器已停止")
		}()

		for {
			select {
			case <-mr.stopChan:
				return
			case <-ctx.Done():
				return
			default:
				// 接收消息，超时时间1秒
				msg, err := mr.queueManager.ReceiveMessage(ctx, 1*time.Second)
				if err != nil {
					logger.Error("接收消息失败", zap.Error(err))
					continue
				}

				if msg == nil {
					// 超时无消息，继续循环
					continue
				}

				// 处理消息
				if mr.handler != nil {
					if err := mr.handler.HandleMessage(ctx, msg); err != nil {
						logger.Error("消息处理失败",
							zap.String("message_id", msg.ID),
							zap.Any("type", msg.Type),
							zap.String("from", msg.From),
							zap.Error(err),
						)
					}
				}
			}
		}
	}()
}

// Stop 停止消息循环
func (mr *MessageRouter) Stop() {
	if !mr.running {
		return
	}

	close(mr.stopChan)
	logger.Info("正在停止消息路由器...")
}

// IsRunning 检查是否正在运行
func (mr *MessageRouter) IsRunning() bool {
	return mr.running
}
