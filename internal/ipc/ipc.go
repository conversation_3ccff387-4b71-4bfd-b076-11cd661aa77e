package ipc

import (
	"context"
	"fmt"

	"aigc_server/internal/config"
	redisClient "aigc_server/pkg/redis"
)

// 进程ID常量
const (
	ProcessIDMain = "main"
)

// GetWorkerProcessID 获取worker进程ID
func GetWorkerProcessID(uid string) string {
	return fmt.Sprintf("worker:%s", uid)
}

// IPCManager IPC管理器
type IPCManager struct {
	QueueManager *QueueManager
	Router       *MessageRouter
	ProcessID    string
}

// NewIPCManager 创建IPC管理器
func NewIPCManager(processID string, handler MessageHandler) *IPCManager {
	qm := NewQueueManager(processID)
	router := NewMessageRouter(qm, handler)

	return &IPCManager{
		QueueManager: qm,
		Router:       router,
		ProcessID:    processID,
	}
}

// Start 启动IPC服务
func (ipc *IPCManager) Start(ctx context.Context) {
	ipc.Router.Start(ctx)
}

// Stop 停止IPC服务
func (ipc *IPCManager) Stop() {
	ipc.Router.Stop()
}

// SendMessage 发送消息
func (ipc *IPCManager) SendMessage(ctx context.Context, targetProcessID string, msgType MessageType, data map[string]interface{}) error {
	msg := NewMessage(msgType, ipc.ProcessID, targetProcessID, data)
	return ipc.QueueManager.SendMessage(ctx, targetProcessID, msg)
}

// InitRedisAndIPC 初始化Redis和IPC
func InitRedisAndIPC(cfg *config.Config) error {
	// 初始化Redis
	err := redisClient.Init(&cfg.Redis)
	if err != nil {
		return fmt.Errorf("初始化Redis失败: %v", err)
	}

	return nil
}
