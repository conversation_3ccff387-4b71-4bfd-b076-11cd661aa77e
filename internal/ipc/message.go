package ipc

import (
	"encoding/json"
	"time"
)

// Message 通用消息结构
type Message struct {
	ID        string                 `json:"id"`        // 消息ID
	Type      MessageType            `json:"type"`      // 消息类型
	From      string                 `json:"from"`      // 发送者标识 (main/worker:uid)
	To        string                 `json:"to"`        // 接收者标识 (main/worker:uid)
	Timestamp int64                  `json:"timestamp"` // 时间戳
	Data      map[string]interface{} `json:"data"`      // 消息数据
}

// NewMessage 创建新消息
func NewMessage(msgType MessageType, from, to string, data map[string]interface{}) *Message {
	return &Message{
		ID:        generateMessageID(),
		Type:      msgType,
		From:      from,
		To:        to,
		Timestamp: time.Now().Unix(),
		Data:      data,
	}
}

// ToJSON 转换为JSON字符串
func (m *Message) ToJSON() (string, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// FromJSON 从JSON字符串解析消息
func FromJSON(jsonStr string) (*Message, error) {
	var msg Message
	err := json.Unmarshal([]byte(jsonStr), &msg)
	if err != nil {
		return nil, err
	}
	return &msg, nil
}

// generateMessageID 生成消息ID
func generateMessageID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(result)
}
