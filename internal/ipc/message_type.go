package ipc

type MessageType string

const (
	// 原有消息类型
	MessageTypeDollTcpMessage MessageType = "doll_tcp_message"

	// TCP相关消息类型
	MessageTypeTcpRequest  MessageType = "tcp_request"  // TCP请求转发到子进程
	MessageTypeTcpResponse MessageType = "tcp_response" // 子进程响应转发到TCP客户端
	MessageTypeCommand     MessageType = "command"      // 命令消息
	MessageTypeStatus      MessageType = "status"       // 状态消息
	MessageTypeEvent       MessageType = "event"        // 事件消息
	MessageTypeError       MessageType = "error"        // 错误消息
	MessageTypeLog         MessageType = "log"          // 日志消息
)
