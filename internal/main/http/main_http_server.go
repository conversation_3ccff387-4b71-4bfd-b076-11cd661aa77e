package http

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"aigc_server/internal/config"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

// StartChildProcessFunc 启动子进程的函数类型
type StartChildProcessFunc func(uid, roomId string) error

// MainHTTPServer HTTP服务器
type MainHTTPServer struct {
	cfg               *config.Config
	server            *http.Server
	startChildProcess StartChildProcessFunc
}

// RoomEnterRequest 房间进入请求
type RoomEnterRequest struct {
	UID string `json:"uid"`
}

// RoomEnterResponse 房间进入响应
type RoomEnterResponse struct {
	RoomID     string `json:"roomId"`
	RoomToken  string `json:"roomToken"`
	ExpireTime string `json:"expireTime"`
}

// NewMainHTTPServer 创建HTTP服务器
func NewMainHTTPServer(cfg *config.Config, startChildProcess StartChildProcessFunc) *MainHTTPServer {
	return &MainHTTPServer{
		cfg:               cfg,
		startChildProcess: startChildProcess,
	}
}

// Start 启动服务器
func (s *MainHTTPServer) Start() error {
	mux := http.NewServeMux()

	// 注册路由
	mux.HandleFunc("/aigc/room-enter", s.handleRoomEnter)

	// 创建HTTP服务器
	addr := fmt.Sprintf("%s:%d", s.cfg.HTTP.Host, s.cfg.HTTP.Port)
	s.server = &http.Server{
		Addr:    addr,
		Handler: mux,
	}

	logger.Info("HTTP服务器启动中",
		zap.String("addr", addr),
		zap.String("env", s.cfg.Server.Env),
	)

	// 启动服务器
	return s.server.ListenAndServe()
}

// Stop 停止服务器
func (s *MainHTTPServer) Stop() error {
	logger.Info("HTTP服务器停止中")
	return s.server.Close()
}

// handleRoomEnter 处理房间进入请求
func (s *MainHTTPServer) handleRoomEnter(w http.ResponseWriter, r *http.Request) {
	logger.Info("收到进入房间请求", zap.String("method", r.Method))
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	logger.Info("收到进入房间请求体", zap.String("body", string(body)))
	if err != nil {
		http.Error(w, "读取请求体失败", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// 解析JSON请求
	var req RoomEnterRequest
	if err := json.Unmarshal(body, &req); err != nil {
		http.Error(w, "解析JSON请求失败", http.StatusBadRequest)
		return
	}

	// 验证uid参数
	if req.UID == "" {
		http.Error(w, "缺少uid参数", http.StatusBadRequest)
		return
	}

	logger.Info("收到进入房间请求", zap.String("uid", req.UID))

	// 启动子进程
	if s.startChildProcess == nil {
		logger.Error("StartChildProcess为空")
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
		return
	}

	roomID := utils.GenerateRoomId(req.UID)
	roomToken, expireTime, err := utils.GenerateRoomToken(s.cfg.Rtc.AppID, s.cfg.Rtc.AppKey, roomID, req.UID)

	logger.Info("正在为用户启动子进程",
		zap.String("uid", req.UID), zap.String("roomId", roomID), zap.String("expireTime", expireTime),
	)

	if err != nil {
		logger.Error("生成房间Token失败", zap.Error(err))
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
		return
	}

	if err := s.startChildProcess(req.UID, roomID); err != nil {
		logger.Error("启动子进程失败", zap.Error(err))
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
		return
	}

	logger.Info("进入房间成功",
		zap.String("uid", req.UID),
		zap.String("roomId", roomID),
		zap.String("roomToken", roomToken),
	)
	// 构造响应
	response := RoomEnterResponse{
		RoomID:     roomID,
		RoomToken:  roomToken,
		ExpireTime: expireTime,
	}
	// TODO:
	// 延迟2s返回响应
	time.Sleep(2 * time.Second)

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	logger.Info("进入房间响应", zap.String("uid", req.UID), zap.String("roomId", roomID), zap.String("roomToken", roomToken))
	// 返回JSON响应
	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Error("编码响应失败", zap.Error(err))
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
		return
	}
}
