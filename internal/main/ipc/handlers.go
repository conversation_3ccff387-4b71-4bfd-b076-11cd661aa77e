package ipc

import (
	"context"

	"go.uber.org/zap"

	"aigc_server/internal/ipc"
	"aigc_server/pkg/logger"
)

// MainMessageHandler 主进程消息处理器
type MainMessageHandler struct{}

// HandleMessage 处理消息
func (h *MainMessageHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
	logger.Info("主进程收到消息",
		zap.String("id", msg.ID),
		zap.Any("type", msg.Type),
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)

	// 根据消息类型处理业务逻辑
	switch msg.Type {
	case "status":
		return h.handleStatus(ctx, msg)
	case "event":
		return h.handleEvent(ctx, msg)
	case "error":
		return h.handleError(ctx, msg)
	case "log":
		return h.handleLog(ctx, msg)
	default:
		logger.Warn("未知的消息类型", zap.Any("type", msg.Type))
	}

	return nil
}

// handleStatus 处理状态消息
func (h *MainMessageHandler) handleStatus(ctx context.Context, msg *ipc.Message) error {
	logger.Info("处理状态消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加状态处理逻辑
	return nil
}

// handleEvent 处理事件消息
func (h *MainMessageHandler) handleEvent(ctx context.Context, msg *ipc.Message) error {
	logger.Info("处理事件消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加事件处理逻辑
	return nil
}

// handleError 处理错误消息
func (h *MainMessageHandler) handleError(ctx context.Context, msg *ipc.Message) error {
	logger.Error("收到错误消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加错误处理逻辑
	return nil
}

// handleLog 处理日志消息
func (h *MainMessageHandler) handleLog(ctx context.Context, msg *ipc.Message) error {
	logger.Info("收到日志消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加日志处理逻辑
	return nil
}
