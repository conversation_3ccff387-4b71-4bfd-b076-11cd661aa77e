package ipc

import (
	"context"

	"go.uber.org/zap"

	"aigc_server/internal/ipc"
	"aigc_server/pkg/logger"
)

// TCPServerInterface TCP服务器接口
type TCPServerInterface interface {
	SendToClient(dollId string, data interface{}) error
}

// MainMessageHandler 主进程消息处理器
type MainMessageHandler struct {
	tcpServer TCPServerInterface
}

// NewMainMessageHandler 创建主进程消息处理器
func NewMainMessageHandler(tcpServer TCPServerInterface) *MainMessageHandler {
	return &MainMessageHandler{
		tcpServer: tcpServer,
	}
}

// HandleMessage 处理消息
func (h *MainMessageHandler) HandleMessage(ctx context.Context, msg *ipc.Message) error {
	logger.Info("主进程收到消息",
		zap.String("id", msg.ID),
		zap.Any("type", msg.Type),
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)

	// 根据消息类型处理业务逻辑
	switch msg.Type {
	case ipc.MessageTypeTcpResponse:
		return h.handleTcpResponse(ctx, msg)
	case ipc.MessageTypeStatus:
		return h.handleStatus(ctx, msg)
	case ipc.MessageTypeEvent:
		return h.handleEvent(ctx, msg)
	case ipc.MessageTypeError:
		return h.handleError(ctx, msg)
	case ipc.MessageTypeLog:
		return h.handleLog(ctx, msg)
	default:
		logger.Warn("未知的消息类型", zap.Any("type", msg.Type))
	}

	return nil
}

// handleTcpResponse 处理TCP响应消息
func (h *MainMessageHandler) handleTcpResponse(ctx context.Context, msg *ipc.Message) error {
	logger.Info("处理TCP响应消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)

	// 从消息数据中提取娃娃ID和响应数据
	dollId, ok := msg.Data["doll_id"].(string)
	if !ok {
		logger.Error("TCP响应消息缺少doll_id")
		return nil
	}

	responseData := msg.Data["response_data"]
	if responseData == nil {
		logger.Error("TCP响应消息缺少response_data")
		return nil
	}

	// 通过TCP服务器发送响应到客户端
	if h.tcpServer != nil {
		if err := h.tcpServer.SendToClient(dollId, responseData); err != nil {
			logger.Error("发送TCP响应到客户端失败",
				zap.String("doll_id", dollId),
				zap.Error(err))
		}
	}

	return nil
}

// handleStatus 处理状态消息
func (h *MainMessageHandler) handleStatus(ctx context.Context, msg *ipc.Message) error {
	logger.Info("处理状态消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加状态处理逻辑
	return nil
}

// handleEvent 处理事件消息
func (h *MainMessageHandler) handleEvent(ctx context.Context, msg *ipc.Message) error {
	logger.Info("处理事件消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加事件处理逻辑
	return nil
}

// handleError 处理错误消息
func (h *MainMessageHandler) handleError(ctx context.Context, msg *ipc.Message) error {
	logger.Error("收到错误消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加错误处理逻辑
	return nil
}

// handleLog 处理日志消息
func (h *MainMessageHandler) handleLog(ctx context.Context, msg *ipc.Message) error {
	logger.Info("收到日志消息",
		zap.String("from", msg.From),
		zap.Any("data", msg.Data),
	)
	// 这里可以添加日志处理逻辑
	return nil
}
