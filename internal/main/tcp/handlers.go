package tcp

import (
	"context"
	"net"
	"strconv"
	"time"

	"aigc_server/internal/config"
	"aigc_server/internal/ipc"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"

	"go.uber.org/zap"
)

// ConnectionHandler TCP连接处理器
type ConnectionHandler struct {
	cfg               *config.Config
	ipcManager        *ipc.IPCManager
	startChildProcess func(uid, roomID string) error
	conn              net.Conn
	dollId            string // 当前连接的娃娃ID
	processKey        string // 对应的子进程key
}

// NewConnectionHandler 创建连接处理器
func NewConnectionHandler(cfg *config.Config, ipcManager *ipc.IPCManager, startChildProcess func(uid, roomID string) error, conn net.Conn) *ConnectionHandler {
	return &ConnectionHandler{
		cfg:               cfg,
		ipcManager:        ipcManager,
		startChildProcess: startChildProcess,
		conn:              conn,
	}
}

// Handle 处理连接
func (h *ConnectionHandler) Handle(ctx context.Context) {
	defer h.conn.Close()

	remoteAddr := h.conn.RemoteAddr().String()
	logger.Info("新的TCP连接建立", zap.String("remote_addr", remoteAddr))

	packetReader := NewPacketReader()

	for {
		select {
		case <-ctx.Done():
			logger.Info("连接处理被取消", zap.String("remote_addr", remoteAddr))
			return
		default:
		}

		packet, err, isAudio := packetReader.ReadPacket(h.conn)
		if err != nil {
			if err.Error() == "EOF" {
				logger.Info("客户端主动断开连接", zap.String("remote_addr", remoteAddr))
				return
			}
			if nerr, ok := err.(net.Error); ok && nerr.Timeout() {
				logger.Warn("客户端连接超时", zap.String("remote_addr", remoteAddr))
			} else {
				logger.Error("读取数据错误", zap.String("remote_addr", remoteAddr), zap.Error(err))
			}
			continue
		}

		if isAudio {
			h.handleAudioData(ctx, packet)
			continue
		}

		// 解析消息
		msg, err := ParseMessage(packet)
		if err != nil {
			logger.Error("解析消息失败", zap.Error(err))
			SendJSON(h.conn, map[string]interface{}{"code": 1, "message": "invalid request"})
			continue
		}

		// 处理不同类型的消息
		switch req := msg.(type) {
		case DollEnterRoomRequest:
			h.handleDollEnterRoom(ctx, req)
		case DollSettingsRequest:
			h.handleDollSettings(ctx, req)
		case DollInterruptRequest:
			h.handleDollInterrupt(ctx, req)
		default:
			logger.Warn("未知的消息类型", zap.Any("message", msg))
			SendJSON(h.conn, map[string]interface{}{"code": 1, "message": "unknown message type"})
		}
	}
}

// handleDollEnterRoom 处理娃娃进入房间请求
func (h *ConnectionHandler) handleDollEnterRoom(ctx context.Context, req DollEnterRoomRequest) {
	logger.Info("处理娃娃进入房间请求", zap.String("doll_id", req.DollId))

	// 生成房间ID和Token（简化版本，不依赖外部服务）
	roomID := utils.GenerateRoomId(req.DollId)
	roomToken, _, err := utils.GenerateRoomToken(h.cfg.Rtc.AppID, h.cfg.Rtc.AppKey, roomID, req.DollId)
	if err != nil {
		logger.Error("生成房间Token失败", zap.Error(err))
		SendJSON(h.conn, map[string]interface{}{"code": 1, "message": "生成房间信息失败"})
		return
	}

	logger.Info("生成房间信息",
		zap.String("doll_id", req.DollId),
		zap.String("room_id", roomID),
		zap.String("room_token", roomToken))

	// 启动子进程
	if h.startChildProcess != nil {
		if err := h.startChildProcess(req.DollId, roomID); err != nil {
			logger.Error("启动子进程失败", zap.Error(err))
			SendJSON(h.conn, map[string]interface{}{"code": 1, "message": "启动子进程失败"})
			return
		}
	}

	// 记录当前连接的娃娃ID和进程key
	h.dollId = req.DollId
	h.processKey = utils.GetProcessKey(req.DollId)

	// 构造响应
	resp := DollEnterRoomResponse{
		Code:    0,
		Message: "success",
	}
	resp.Data.DollId = req.DollId
	resp.Data.RoomId = roomID
	resp.Data.RoomToken = roomToken

	if err := SendJSON(h.conn, resp); err != nil {
		logger.Error("发送响应失败", zap.Error(err))
	}
}

// handleDollSettings 处理娃娃设置请求
func (h *ConnectionHandler) handleDollSettings(ctx context.Context, req DollSettingsRequest) {
	logger.Info("处理娃娃设置请求",
		zap.String("doll_id", req.DollId),
		zap.String("battery", req.Battery),
		zap.String("volume", req.Volume))

	// 如果有子进程，通过IPC转发请求
	if h.processKey != "" && h.ipcManager != nil {
		workerProcessID := ipc.GetWorkerProcessID(req.DollId)
		data := map[string]interface{}{
			"type":    "doll_settings",
			"doll_id": req.DollId,
			"battery": req.Battery,
			"volume":  req.Volume,
		}

		if err := h.ipcManager.SendMessage(ctx, workerProcessID, "tcp_request", data); err != nil {
			logger.Error("通过IPC转发设置请求失败", zap.Error(err))
		}
	}

	// 简单处理：返回调整后的音量
	volume, _ := strconv.Atoi(req.Volume)
	resp := DollSettingsResponse{
		Code:    0,
		Message: "success",
	}
	resp.Data.Volume = strconv.Itoa((volume + 10) % 100)

	if err := SendJSON(h.conn, resp); err != nil {
		logger.Error("发送设置响应失败", zap.Error(err))
	}
}

// handleDollInterrupt 处理娃娃打断请求
func (h *ConnectionHandler) handleDollInterrupt(ctx context.Context, req DollInterruptRequest) {
	logger.Info("处理娃娃打断请求",
		zap.String("doll_id", req.DollId),
		zap.Bool("interrupt", req.Interrupt))

	// 如果有子进程，通过IPC转发请求
	if h.processKey != "" && h.ipcManager != nil {
		workerProcessID := ipc.GetWorkerProcessID(req.DollId)
		data := map[string]interface{}{
			"type":      "doll_interrupt",
			"doll_id":   req.DollId,
			"interrupt": req.Interrupt,
		}

		if err := h.ipcManager.SendMessage(ctx, workerProcessID, "tcp_request", data); err != nil {
			logger.Error("通过IPC转发打断请求失败", zap.Error(err))
		}
	}

	resp := DollInterruptResponse{
		Code:    0,
		Message: "success",
	}
	resp.Data.Interrupt = req.Interrupt

	if err := SendJSON(h.conn, resp); err != nil {
		logger.Error("发送打断响应失败", zap.Error(err))
	}
}

// handleAudioData 处理音频数据
func (h *ConnectionHandler) handleAudioData(ctx context.Context, audioData []byte) {
	logger.Debug("收到音频数据",
		zap.String("doll_id", h.dollId),
		zap.Int("data_length", len(audioData)))

	// 如果有子进程，通过IPC转发音频数据
	if h.processKey != "" && h.ipcManager != nil {
		workerProcessID := ipc.GetWorkerProcessID(h.dollId)
		data := map[string]interface{}{
			"type":       "audio_data",
			"doll_id":    h.dollId,
			"audio_data": audioData,
			"timestamp":  time.Now().Unix(),
		}

		if err := h.ipcManager.SendMessage(ctx, workerProcessID, "tcp_request", data); err != nil {
			logger.Error("通过IPC转发音频数据失败", zap.Error(err))
		}
	}
}

// SendToClient 向客户端发送数据（供IPC回调使用）
func (h *ConnectionHandler) SendToClient(data interface{}) error {
	return SendJSON(h.conn, data)
}
