package tcp

import (
	"encoding/binary"
	"encoding/json"
	"errors"
	"net"
)

// 协议常量
const (
	HeaderSize      = 8
	AudioHeaderMark = 0xBB
	DataHeaderMark  = 0xAB
)

// 消息类型定义
type DollEnterRoomRequest struct {
	DollId string `json:"dollId"`
}

type DollSettingsRequest struct {
	DollId  string `json:"dollId"`
	Battery string `json:"battery"`
	Volume  string `json:"volume"`
}

type DollEnterRoomResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		DollId    string `json:"dollId"`
		RoomId    string `json:"roomId"`
		RoomToken string `json:"roomToken"`
	} `json:"data"`
}

type DollSettingsResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Volume string `json:"volume"`
	} `json:"data"`
}

type DollInterruptRequest struct {
	DollId    string `json:"dollId"`
	Interrupt bool   `json:"interrupt"`
}

type DollInterruptResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Interrupt bool `json:"interrupt"`
	} `json:"data"`
}

// PacketReader 数据包读取器
type PacketReader struct {
	remainingData []byte
}

// NewPacketReader 创建新的数据包读取器
func NewPacketReader() *PacketReader {
	return &PacketReader{
		remainingData: make([]byte, 0),
	}
}

// ReadPacket 读取数据包
func (pr *PacketReader) ReadPacket(conn net.Conn) ([]byte, error, bool) {
	buf := make([]byte, 0, 4096)
	tmp := make([]byte, 4096)

	for {
		// 如果有剩余数据，先使用剩余数据
		if len(pr.remainingData) > 0 {
			buf = append(buf, pr.remainingData...)
			pr.remainingData = []byte{}
		}

		// 先保证有8字节头
		for len(buf) < HeaderSize {
			n, err := conn.Read(tmp)
			if err != nil {
				return nil, err, false
			}
			buf = append(buf, tmp[:n]...)
		}

		// 检查是否是音频数据
		isAudio := buf[0] == AudioHeaderMark && buf[1] == AudioHeaderMark &&
			buf[2] == AudioHeaderMark && buf[3] == AudioHeaderMark

		// 检查头部
		if (buf[0] != DataHeaderMark || buf[1] != DataHeaderMark || buf[2] != DataHeaderMark) && !isAudio {
			return nil, errors.New("包头错误"), false
		}

		xorVal := buf[3]
		bodyLen := int(binary.LittleEndian.Uint32(buf[4:8]))

		// 继续读body
		for len(buf) < HeaderSize+bodyLen {
			n, err := conn.Read(tmp)
			if err != nil {
				return nil, err, false
			}
			buf = append(buf, tmp[:n]...)
		}

		body := buf[HeaderSize : HeaderSize+bodyLen]

		// 校验异或（音频数据不校验）
		if !isAudio {
			var xor byte
			for _, b := range body {
				xor ^= b
			}
			if xor != xorVal {
				return nil, errors.New("异或校验失败"), false
			}
		}

		// 拆出本包
		packet := make([]byte, bodyLen)
		copy(packet, body)

		// 保存剩余数据
		if len(buf) > HeaderSize+bodyLen {
			pr.remainingData = make([]byte, len(buf)-HeaderSize-bodyLen)
			copy(pr.remainingData, buf[HeaderSize+bodyLen:])
		}

		return packet, nil, isAudio
	}
}

// Pack 打包数据
func Pack(body []byte) []byte {
	header := make([]byte, HeaderSize)
	header[0] = DataHeaderMark
	header[1] = DataHeaderMark
	header[2] = DataHeaderMark

	// 计算异或校验
	var xor byte
	for _, b := range body {
		xor ^= b
	}
	header[3] = xor

	// 写入长度，小端
	bodyLen := len(body)
	header[4] = byte(bodyLen & 0xFF)
	header[5] = byte((bodyLen >> 8) & 0xFF)
	header[6] = byte((bodyLen >> 16) & 0xFF)
	header[7] = byte((bodyLen >> 24) & 0xFF)

	return append(header, body...)
}

// SendJSON 发送JSON数据
func SendJSON(conn net.Conn, v interface{}) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}
	packet := Pack(b)
	_, err = conn.Write(packet)
	return err
}

// ParseMessage 解析消息
func ParseMessage(data []byte) (interface{}, error) {
	// 先尝试解析为 DollSettingsRequest
	var setReq DollSettingsRequest
	if err := json.Unmarshal(data, &setReq); err == nil && setReq.DollId != "" && setReq.Battery != "" && setReq.Volume != "" {
		return setReq, nil
	}

	// 语音打断
	var interruptReq DollInterruptRequest
	if err := json.Unmarshal(data, &interruptReq); err == nil && interruptReq.DollId != "" {
		return interruptReq, nil
	}

	// DollEnterRoomRequest
	var enterRoomReq DollEnterRoomRequest
	if err := json.Unmarshal(data, &enterRoomReq); err == nil && enterRoomReq.DollId != "" {
		return enterRoomReq, nil
	}

	return nil, errors.New("无法解析消息")
}
