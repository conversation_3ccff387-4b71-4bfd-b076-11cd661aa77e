package service

import (
	"aigc_server/pkg/utils"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type HttpRequest struct {
	Method  string
	Url     string
	Body    interface{}
	Headers map[string]string
	Timeout int
}

func NewHttpRequest(method, url string, body interface{}, headers map[string]string, timeout int) *HttpRequest {
	return &HttpRequest{
		Method:  method,
		Url:     url,
		Body:    body,
		Headers: headers,
		Timeout: timeout,
	}
}
func (h *HttpRequest) DoRequest(ctx context.Context) ([]byte, error) {
	return DoRequest(ctx, h.Method, h.Url, h.<PERSON>, h.<PERSON>, h.Timeout)
}

func DoRequestJson(ctx context.Context, method, url string, body interface{}, headers map[string]string, timeout int) ([]byte, error) {
	if headers == nil {
		headers = make(map[string]string)
	}
	if _, ok := headers["Content-Type"]; !ok {
		headers["Content-Type"] = "application/json"
	}
	return DoRequest(ctx, method, url, body, headers, timeout)
}

func DoRequest(ctx context.Context, method, url string, body interface{}, headers map[string]string, timeout int) ([]byte, error) {
	client := &http.Client{
		Timeout: time.Duration(timeout) * time.Second,
	}
	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(jsonBody)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置自定义headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("request failed with status %d", resp.StatusCode)
	}
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return respBody, nil
}

// DownloadFile 下载文件，可选择性保存到本地
// url: 文件下载地址
// savePath: 本地保存路径，如为空则不保存到本地，仅返回文件内容
// headers: 请求头
// timeout: 超时时间(秒)
// 返回值:
// - 如果savePath为空，返回文件内容和错误
// - 如果savePath不为空，返回nil和错误
func DownloadFile(ctx context.Context, url string, headers map[string]string, timeout int, savePath string) ([]byte, error) {
	body, err := DoRequest(ctx, http.MethodGet, url, nil, headers, timeout)
	if err != nil {
		return nil, fmt.Errorf("下载文件失败: %w", err)
	}
	// 如果指定了保存路径，则保存文件到本地
	if savePath != "" {
		err = utils.WriteToFile(savePath, body)
		if err != nil {
			return nil, fmt.Errorf("保存文件失败: %w", err)
		}
	}

	return body, nil
}
