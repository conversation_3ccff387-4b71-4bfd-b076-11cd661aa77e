package doll

import (
	"aigc_server/internal/config"
	"aigc_server/internal/constant"
	"aigc_server/internal/service"
	"aigc_server/internal/worker/llm"
	"aigc_server/internal/worker/rtc"
	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils"
	proto "aigc_server/proto/go"
	"fmt"
	"sync"
	"time"

	"bytedance/bytertc/rtcengine"

	"go.uber.org/zap"
	"golang.org/x/net/context"
)

func (s *DollState) OnRoomStateChanged(roomId string, uid string, state int, extraInfo string) {

}

func (s *DollState) OnUserJoined(userInfo rtcengine.UserInfo) {
	s.LlmState.SendLLMRequestWelcome()
}
func (s *DollState) OnUserLeave(uid string, reason rtcengine.UserOfflineReason) {
	s.CtxCancel()
}
func (s *DollState) OnRemoteUserAudioFrame(_ rtcengine.RemoteStreamKey, audioFrame rtcengine.IAudioFrame) {
	for {
		select {
		case s.audioFrameChan <- audioFrame:
			return
		default:
			logger.Info("OnRemoteUserAudioFrame. 音频帧通道已满，丢弃音频帧")
			return
		}
	}
}

type DollState struct {
	Cfg *config.Config

	RtcService *rtc.RTCService
	GrpcClient *service.GRPCClient
	netHandler *GrpcNetHandler

	audioFrameChan chan rtcengine.IAudioFrame

	ctx       context.Context
	CtxCancel context.CancelFunc

	voiceFrameSourceQueue []*VoiceFrameSource
	mutexFrameSourceQueue sync.Mutex
	LlmState              *llm.LLMState

	audioInterruptIndex int32
	interruptPauseChan  chan struct{}
}

func NewDollStateAndStart(ctx context.Context, cfg *config.Config, uid, roomID string) error {
	dollState := &DollState{Cfg: cfg}
	dollState.ctx, dollState.CtxCancel = context.WithCancel(ctx)
	return dollState.RoomLooping(uid, roomID)
}

func (s *DollState) roomStop() error {
	logger.Info("RTCService 停止中", zap.String("roomID", s.RtcService.RoomID), zap.String("uid", s.RtcService.Uid), zap.String("serverUid", s.RtcService.ServerUid))
	if s.RtcService != nil {
		s.RtcService.Stop()
		s.RtcService = nil
	}
	if s.audioFrameChan != nil {
		close(s.audioFrameChan)
		s.audioFrameChan = nil
	}
	if s.GrpcClient != nil {
		s.GrpcClient.SetHandler(nil)
		s.GrpcClient.Close()
		s.GrpcClient = nil
	}
	s.netHandler = nil
	return nil
}

func (s *DollState) RoomLooping(uid, roomID string) error {
	defer utils.TraceRecover()
	defer s.roomStop()

	var err error
	s.RtcService, err = rtc.NewRTCService(s.ctx, s.Cfg, uid, roomID, s)
	if err != nil {
		logger.Error("启动RTC服务失败", zap.Error(err))
		return err
	}

	timeout, cancelTimeout := context.WithTimeout(s.ctx, 60*time.Second)
	defer cancelTimeout()
	go func() {
		<-timeout.Done()
		if s.RtcService.UserJoinedStatus == rtc.UserJoinedStatusNotJoined {
			logger.Error("RTCService RoomLooping超时，用户未加入房间", zap.String("roomID", s.RtcService.RoomID), zap.String("uid", uid), zap.String("serverUid", s.RtcService.ServerUid))
			s.CtxCancel()
		}
	}()

	// 创建GRPC客户端
	s.GrpcClient, err = service.NewGRPCClient(s.Cfg, uid)
	if err != nil {
		logger.Error("创建GRPC客户端失败", zap.Error(err))
		return err
	}
	s.netHandler = NewGrpcNetHandler(s)
	s.GrpcClient.SetHandler(s.netHandler)

	s.LlmState = llm.NewLLMState(s.ctx, s.GrpcClient)
	s.audioFrameChan = make(chan rtcengine.IAudioFrame, 100)
	s.voiceFrameSourceQueue = make([]*VoiceFrameSource, 0)

	go s.loopReceiveAudioFrame()
	go s.loopDispatchAudioFrame()

	<-s.ctx.Done()

	logger.Info("RTCService ctx已取消", zap.String("roomID", s.RtcService.RoomID), zap.String("uid", s.RtcService.Uid), zap.String("serverUid", s.RtcService.ServerUid))
	return nil
}

func (s *DollState) loopReceiveAudioFrame() error {
	defer utils.TraceRecover()
	var saveFileChan chan []byte
	if s.Cfg.Debug.SaveAudio {
		saveFileChan = utils.ContinueWriteAppendData(s.ctx, utils.GetRtcRecvSaveFilePath(s.RtcService.Uid))
	}
	for {
		select {
		case <-s.ctx.Done():
			logger.Info("RTCService ctx已取消，停止接收音频帧", zap.String("roomID", s.RtcService.RoomID), zap.String("uid", s.RtcService.Uid), zap.String("serverUid", s.RtcService.ServerUid))
			return nil
		case frame := <-s.audioFrameChan:
			if s.Cfg.Debug.SaveAudio {
				select {
				case saveFileChan <- frame.Data():
				default:
					logger.Warn("RTCService ContinueWriteAppendData saveFileChan已满，丢弃音频帧")
				}
			}
			if err := s.netHandler.OnAudioFrame(&frame); err != nil {
				logger.Error("RTCService RoomLooping 音频帧接收器错误", zap.Error(err))
			}
		}
	}
}

func (s *DollState) loopDispatchAudioFrame() error {
	defer utils.TraceRecover()
	sendAudioChan := make(chan rtcengine.IAudioFrame)
	defer close(sendAudioChan)

	logger.Info("启动协程推送外部音频帧", zap.Int("frame", constant.RtcFrameRate))

	go s.frameProducer(sendAudioChan)
	go s.frameConsumer(sendAudioChan)

	<-s.ctx.Done()
	return nil
}
func (s *DollState) frameProducer(sendAudioChan chan rtcengine.IAudioFrame) error {
	defer utils.TraceRecover()
	frameCount := 0
	for {
		select {
		case <-s.ctx.Done():
			return nil
		default:
			if s.interruptPauseChan != nil {
				logger.Info("RTCService RoomLooping 音频帧生产者 等待 interruptPauseChan")
				select {
				case <-s.interruptPauseChan:
				case <-time.After(10 * time.Second):
					logger.Error("RTCService 音频帧生产者 interruptPauseChan 超时")
					close(s.interruptPauseChan)
					s.interruptPauseChan = nil
				}
				logger.Info("RTCService RoomLooping 音频帧生产者 恢复，继续发送音频帧")
			}
			source := s.GetFirstVoiceFrameSource()
			var audioFrame rtcengine.IAudioFrame
			if source != nil {
				frameCount = 0
				var err error
				audioFrame, _, err = source.NextFrame()
				if err != nil {
					logger.Error("RTCService RoomLooping 音频帧生产者错误", zap.Error(err))
					continue
				}
			} else {
				if frameCount < constant.RtcFrameRate*2 {
					audioFrame = VoiceFrameSourceEmptyFrame()
					frameCount++
				} else {
					time.Sleep(time.Millisecond * 10)
					continue
				}
			}
			select {
			case <-s.ctx.Done():
				return nil
			case sendAudioChan <- audioFrame:
			}
		}
	}
}

func (s *DollState) frameConsumer(sendAudioChan chan rtcengine.IAudioFrame) error {
	defer utils.TraceRecover()

	var debugSaveAudioChan chan []byte
	if s.Cfg.Debug.SaveAudio {
		debugSaveAudioChan = utils.ContinueWriteAppendData(s.ctx, utils.GetRtcSendSaveFilePath(s.RtcService.Uid))
	}

	ticker := time.Tick(time.Millisecond * time.Duration(1000/constant.RtcFrameRate))
	for range ticker {
		select {
		case <-s.ctx.Done():
			logger.Info("RTCService ctx已取消，停止发送音频帧", zap.String("roomID", s.RtcService.RoomID), zap.String("uid", s.RtcService.Uid), zap.String("serverUid", s.RtcService.ServerUid))
			return nil
		case audioFrame := <-sendAudioChan:
			go func() {
				defer func() {
					if r := recover(); r != nil {
						logger.Warn("RTCService 推送外部音频帧协程错误", zap.Any("recover", r))
					}
				}()
				select {
				case <-s.ctx.Done():
					return
				default:
					ret := s.RtcService.PushExternalAudioFrame(audioFrame)
					if ret != 0 {
						logger.Error("RTCService 推送外部音频帧API调用错误，错误码 %d", zap.Int("error code", ret))
					}
					if s.Cfg.Debug.SaveAudio {
						select {
						case debugSaveAudioChan <- audioFrame.Data():
						default:
							logger.Warn("RTCService ContinueWriteAppendData debugSaveAudioChan已满，丢弃音频帧")
						}
					}
				}
			}()
		default:
			// logger.Info("RTCService audio consumer chan is empty", zap.String("roomID", s.roomID), zap.String("uid", s.uid), zap.String("serverUid", s.serverUid), zap.Time("time", t))
		}
	}
	return nil
}
func (s *DollState) OnTTSResult(audioDatas []byte, index int32, segment_index int32, isFinal bool) error {
	audio8k, err := utils.ResamplePCM(audioDatas, utils.AudioPCMFormat{
		SampleRate: constant.TtsRecordSampleRate,
		Channels:   1,
		BitDepth:   16,
	}, utils.AudioPCMFormat{
		SampleRate: constant.RtcPushAudioSampleRate,
		Channels:   constant.AudioChannel,
		BitDepth:   constant.SampleBitDepth,
	})
	if err != nil {
		logger.Error("ResamplePCM错误", zap.Error(err))
		return err
	}
	source := NewVoiceFrameSource(audio8k, llm.LLMToolStateType_NO_TOOL, index, segment_index)
	s.PutVoiceFrameSource(source)
	return nil
}

func (s *DollState) CheckLlmCmd(ctx context.Context, response *proto.LLMResponse) error {

	if response.Type == proto.LLMRespType_RESET {
		s.SetAudioInterruptIndex(response.MsgIndex, response.MsgSegmentIndex)
		s.SetAsrResume()

	} else if response.Type == proto.LLMRespType_RESUME {
		s.SetAsrResume()
	} else if response.Type == proto.LLMRespType_NORMAL {
		go s.UpdateLLMToolState(response)
	}
	return nil
}
func (s *DollState) UpdateLLMToolState(response *proto.LLMResponse) error {
	defer utils.TraceRecover()
	llmToolState := s.LlmState.BuildLLMToolStateFromLLMResp(response, s.Cfg.HTTPClient.MediaBaseURL)
	if llmToolState == nil || llmToolState.IsNoTool() {
		return nil
	}
	logger.Info("UpdateLLMToolState 开始", zap.Any("LLMResponse", response))
	if err := llmToolState.Update(); err != nil {
		logger.Error("UpdateLLMToolState 错误", zap.Error(err))
		return err
	}
	if llmToolState.RtcPCM == nil {
		logger.Error("RtcStateUpdateLLMToolState错误, AudioPCM为空", zap.Any("llmToolState", llmToolState))
		return fmt.Errorf("AudioPCM为空")
	}
	logger.Info("RtcState UpdateLLMToolState. 新的工具调用源", zap.Any("llmToolState.State", llmToolState.State),
		zap.Int("llmToolState.Index", int(llmToolState.Index)),
		zap.Any("MediaInfo", llmToolState.MediaInfo))
	source := NewVoiceFrameSource(llmToolState.RtcPCM, llmToolState.State, llmToolState.Index, llmToolState.SegmentIndex)
	s.PutVoiceFrameSource(source)
	return nil
}

func (s *DollState) GetFirstVoiceFrameSource() *VoiceFrameSource {
	s.mutexFrameSourceQueue.Lock()
	defer s.mutexFrameSourceQueue.Unlock()

	for len(s.voiceFrameSourceQueue) > 0 {
		source := s.voiceFrameSourceQueue[0]
		if source == nil || source.IsDone() || source.TestInterrupt(s.audioInterruptIndex) {
			s.voiceFrameSourceQueue = s.voiceFrameSourceQueue[1:]
			continue
		}
		return source
	}
	return nil
}
func (s *DollState) PutVoiceFrameSource(source *VoiceFrameSource) {
	s.mutexFrameSourceQueue.Lock()
	defer s.mutexFrameSourceQueue.Unlock()
	s.voiceFrameSourceQueue = append(s.voiceFrameSourceQueue, source)
}

func (s *DollState) SetAudioInterruptIndex(index int32, seqment_index int32) {
	s.audioInterruptIndex = index
	logger.Info("RtcState LLM 重置 SetAudioInterruptIndex",
		zap.Int("index", int(index)),
		zap.Int("seqment_index", int(seqment_index)))
}

func (s *DollState) SetAsrInterrupt() error {
	if source := s.GetFirstVoiceFrameSource(); source != nil {
		if !source.TestAsrInterrupt() {
			logger.Info("RtcState SetAsrInterrupt 跳过. source.LLMVoiceType:", zap.Any("llmVoiceType", source.LLMVoiceType))
			return nil
		}
		logger.Info("RtcState SetAsrInterrupt. 设置ASR中断", zap.Any("llm", map[string]interface{}{"LLMIndex": source.LLMIndex, "LLMSegmentIndex": source.LLMSegmentIndex, "LLMVoiceType": source.LLMVoiceType, "FrameIndex": source.FrameIndex, "FrameSize": source.FrameSize, "len": source.len}))
	}
	if s.interruptPauseChan == nil {
		logger.Info("RtcState interruptPauseChan为空，创建它")
		s.interruptPauseChan = make(chan struct{})
	}
	return nil
}

func (s *DollState) SetAsrResume() error {
	if s.interruptPauseChan != nil {
		logger.Info("RtcState SetAsrResume. interruptPauseChan非空，关闭它")
		close(s.interruptPauseChan)
		s.interruptPauseChan = nil
	}
	return nil
}
