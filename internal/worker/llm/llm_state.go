package llm

import (
	"aigc_server/internal/service"
	"aigc_server/pkg/logger"
	proto "aigc_server/proto/go"
	"context"

	"go.uber.org/zap"
)

type LLMState struct {
	ctx                     context.Context
	grpcClient              *service.GRPCClient
	LLMToolStateGlobalIndex int32
}

func NewLLMState(ctx context.Context, grpcClient *service.GRPCClient) *LLMState {
	return &LLMState{
		ctx:                     ctx,
		grpcClient:              grpcClient,
		LLMToolStateGlobalIndex: -2,
	}
}

func (s *LLMState) IsLLMRespToolStateIndexValid(resp *proto.LLMResponse) bool {
	return resp.MsgIndex > s.LLMToolStateGlobalIndex
}

func (s *LLMState) BuildLLMToolStateFromLLMResp(resp *proto.LLMResponse, baseUrl string) *LLMToolState {

	if !s.IsLLMRespToolStateIndexValid(resp) {
		return nil
	}

	llmState := &LLMToolState{
		ctx:           s.ctx,
		baseUrl:       baseUrl,
		Index:         resp.MsgIndex,
		SegmentIndex:  resp.MsgSegmentIndex,
		State:         LLMToolStateType_NO_TOOL,
		Content:       "",
		MediaInfo:     nil,
		mediaResBytes: nil,
	}

	llmState.State = LLMToolStateType_NO_TOOL
	if v, ok := resp.ToolCall["name"]; ok {
		if v == "TellStory" {
			llmState.State = LLMToolStateType_TELL_STORY
			llmState.Content = resp.ToolCall["content"]
		} else if v == "PlayMusic" {
			llmState.State = LLMToolStateType_PLAY_MUSIC
			llmState.Content = resp.ToolCall["content"]
		} else if v == "PlayAudio" {
			llmState.State = LLMToolStateType_PLAY_AUDIO
			llmState.Content = resp.ToolCall["content"]
		}
	}

	return llmState
}

func (s *LLMState) SendLLMRequestWelcome() error {
	logger.Info("发送LLM欢迎请求")
	err := s.grpcClient.SendLLMMessage("", true, &map[string]string{
		"name":    "startup_word",
		"content": "",
	})
	if err != nil {
		logger.Error("发送LLM请求 startup_word 失败", zap.Error(err))
		return err
	}
	return nil
}
