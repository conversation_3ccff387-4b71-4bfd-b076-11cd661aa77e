# Audio Processing Utility

A Go package for audio file processing, format detection, and conversion.

## Features

- Audio file format detection (MP3, WAV, AAC, FLAC)
- Audio metadata extraction (sample rate, channels, bit depth, duration)
- Conversion to PCM format
- Conversion from PCM to WAV format
- PCM resampling (change sample rate, bit depth, and channel count)
- Pure Go implementation where possible

## Supported Formats

| Format | Detection | Metadata | Decode to PCM | Encode from PCM |
| ------ | --------- | -------- | ------------- | --------------- |
| WAV    | ✓         | ✓        | ✓             | ✓               |
| MP3    | ✓         | ✓        | ✓             | ✗               |
| FLAC   | ✓         | ✓        | ✓             | ✗               |
| AAC    | ✓         | ✓        | ✗             | ✗               |

## Usage

### Basic Usage

```go
import (
    "fmt"
    "os"
    "aigc_server/pkg/utils"
)

func main() {
    // Read audio file
    data, err := os.ReadFile("audio.mp3")
    if err != nil {
        fmt.Printf("Error reading file: %v\n", err)
        return
    }

    // Create audio processor
    ap, err := utils.NewAudioProcessor(data)
    if err != nil {
        fmt.Printf("Error processing audio: %v\n", err)
        return
    }

    // Get audio info
    info := ap.GetInfo()
    fmt.Printf("Format: %s\n", info.Format)
    fmt.Printf("Sample Rate: %d Hz\n", info.SampleRate)
    fmt.Printf("Channels: %d\n", info.Channels)
    fmt.Printf("Duration: %.2f seconds\n", info.Duration)

    // Convert to PCM
    pcmData, sampleRate, channels, err := ap.DecodeToPCM()
    if err != nil {
        fmt.Printf("Error decoding to PCM: %v\n", err)
        return
    }

    // Convert PCM to WAV
    wavData, err := ap.EncodePCMTo(pcmData, sampleRate, channels, utils.WAVFormat)
    if err != nil {
        fmt.Printf("Error encoding to WAV: %v\n", err)
        return
    }

    // Save WAV file
    err = os.WriteFile("output.wav", wavData, 0644)
    if err != nil {
        fmt.Printf("Error saving WAV file: %v\n", err)
        return
    }
}
```

### PCM Resampling

The package provides PCM resampling functionality to change sample rate, bit depth, and channel count:

```go
import (
    "fmt"
    "os"
    "aigc_server/pkg/utils"
)

func main() {
    // Read PCM data from somewhere (file, network, etc.)
    pcmData, err := os.ReadFile("input.pcm")
    if err != nil {
        fmt.Printf("Error reading PCM file: %v\n", err)
        return
    }

    // Create audio processor
    ap := &utils.AudioProcessor{}

    // Define source PCM format
    srcFormat := utils.AudioPCMFormat{
        SampleRate: 44100,  // 44.1kHz
        Channels:   2,      // Stereo
        BitDepth:   16,     // 16-bit
    }

    // Define target PCM format
    dstFormat := utils.AudioPCMFormat{
        SampleRate: 48000,  // 48kHz
        Channels:   1,      // Mono
        BitDepth:   24,     // 24-bit
    }

    // Resample PCM
    resampledPCM, err := ap.ResamplePCM(pcmData, srcFormat, dstFormat)
    if err != nil {
        fmt.Printf("Error resampling PCM: %v\n", err)
        return
    }

    // Save resampled PCM
    err = os.WriteFile("resampled.pcm", resampledPCM, 0644)
    if err != nil {
        fmt.Printf("Error saving resampled PCM: %v\n", err)
        return
    }

    fmt.Println("PCM resampled successfully!")
}
```

### Command-line Tool

The package also includes a command-line tool for audio processing:

```
$ go run cmd/audio/main.go -input sample.mp3 -output converted.wav -format wav
```

Options:

- `-input`: Input audio file (required)
- `-output`: Output file (optional, defaults to input filename with new extension)
- `-format`: Output format: wav or pcm (default: wav)
- `-info`: Only display audio information without conversion

## Dependencies

- [github.com/hajimehoshi/go-mp3](https://github.com/hajimehoshi/go-mp3) - MP3 decoder in pure Go
- [github.com/youpy/go-wav](https://github.com/youpy/go-wav) - WAV file handling
- [github.com/mewkiz/flac](https://github.com/mewkiz/flac) - FLAC decoder in Go

## Limitations

- AAC decoding is not fully implemented in pure Go. For production use, consider using a CGO wrapper around libraries like FAAD2.
- MP3/FLAC encoding is not implemented. For production use, consider using CGO wrappers around libraries like LAME or libflac.
- PCM resampling uses simple linear interpolation which is fast but may not provide the highest audio quality. For production use where quality is critical, consider using more sophisticated resampling algorithms.

## License

This project is licensed under the terms of the MIT license.
