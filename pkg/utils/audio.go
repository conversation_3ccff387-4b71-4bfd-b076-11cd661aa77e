package utils

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"io"

	"github.com/hajimehoshi/go-mp3"
	"github.com/mewkiz/flac"
	"github.com/youpy/go-wav"
)

// Audio constants
const (
	MP3Format  = "mp3"
	WAVFormat  = "wav"
	AACFormat  = "aac"
	FLACFormat = "flac"
	PCMFormat  = "pcm"
)

// AudioInfo represents information about an audio file
type AudioInfo struct {
	Format       string
	SampleRate   int
	Channels     int
	BitDepth     int
	Duration     float64 // in seconds
	FileSize     int     // in bytes
	TotalSamples int64
}

// AudioPCMFormat represents PCM audio format parameters
type AudioPCMFormat struct {
	SampleRate int
	BitDepth   int
	Channels   int
}

// AudioProcessor handles audio processing operations
type AudioProcessor struct {
	data []byte
	info AudioInfo
}

// NewAudioProcessor creates a new AudioProcessor from byte slice
func NewAudioProcessor(data []byte) (*AudioProcessor, error) {
	if len(data) == 0 {
		return nil, errors.New("音频数据为空")
	}

	ap := &AudioProcessor{
		data: data,
	}

	// Detect format and fill info
	if err := ap.detectFormat(); err != nil {
		return nil, err
	}

	return ap, nil
}

// GetInfo returns audio information
func (ap *AudioProcessor) GetInfo() AudioInfo {
	return ap.info
}

// detectFormat analyzes the data to determine audio format and extract metadata
func (ap *AudioProcessor) detectFormat() error {
	// Check for WAV signature (RIFF)
	if len(ap.data) > 12 && string(ap.data[0:4]) == "RIFF" && string(ap.data[8:12]) == "WAVE" {
		return ap.parseWavInfo()
	}

	// Check for MP3 header (ID3 or sync word)
	if len(ap.data) > 3 && string(ap.data[0:3]) == "ID3" || ap.isMP3SyncWord(ap.data) {
		return ap.parseMP3Info()
	}

	// Check for AAC ADTS marker
	if len(ap.data) > 2 && ap.data[0] == 0xFF && (ap.data[1]&0xF0) == 0xF0 {
		return ap.parseAACInfo()
	}

	// Check for FLAC signature "fLaC"
	if len(ap.data) > 4 && string(ap.data[0:4]) == "fLaC" {
		return ap.parseFLACInfo()
	}

	return fmt.Errorf("未知或不支持的音频格式")
}

// isMP3SyncWord checks if the byte slice starts with an MP3 sync word
func (ap *AudioProcessor) isMP3SyncWord(data []byte) bool {
	// Search for sync word (11 bits set)
	for i := 0; i < len(data)-1; i++ {
		if data[i] == 0xFF && (data[i+1]&0xE0) == 0xE0 {
			return true
		}
		// Only check first 4K to avoid false positives
		if i > 4096 {
			break
		}
	}
	return false
}

// parseWavInfo extracts WAV metadata
func (ap *AudioProcessor) parseWavInfo() error {
	reader := bytes.NewReader(ap.data)
	wavReader := wav.NewReader(reader)

	format, err := wavReader.Format()
	if err != nil {
		return fmt.Errorf("获取WAV格式错误: %v", err)
	}

	ap.info.Format = WAVFormat
	ap.info.SampleRate = int(format.SampleRate)
	ap.info.Channels = int(format.NumChannels)
	ap.info.BitDepth = int(format.BitsPerSample)
	ap.info.FileSize = len(ap.data)

	// Calculate duration
	bytesPerSample := int(format.BitsPerSample) / 8
	dataSize := len(ap.data) - 44 // Typical WAV header size
	totalSamples := dataSize / bytesPerSample / int(format.NumChannels)
	ap.info.TotalSamples = int64(totalSamples)
	ap.info.Duration = float64(totalSamples) / float64(format.SampleRate)

	return nil
}

// parseMP3Info extracts MP3 metadata
func (ap *AudioProcessor) parseMP3Info() error {
	reader := bytes.NewReader(ap.data)
	decoder, err := mp3.NewDecoder(reader)
	if err != nil {
		return fmt.Errorf("解析MP3错误: %v", err)
	}

	ap.info.Format = MP3Format
	ap.info.SampleRate = decoder.SampleRate()
	ap.info.Channels = 2  // MP3 typically has 2 channels
	ap.info.BitDepth = 16 // MP3 typically decodes to 16-bit
	ap.info.FileSize = len(ap.data)

	// Calculate duration
	samples := decoder.Length() / 4 // 4 bytes per sample (16-bit stereo)
	ap.info.TotalSamples = samples
	ap.info.Duration = float64(samples) / float64(decoder.SampleRate())

	return nil
}

// parseFLACInfo extracts FLAC metadata
func (ap *AudioProcessor) parseFLACInfo() error {
	reader := bytes.NewReader(ap.data)
	stream, err := flac.New(reader)
	if err != nil {
		return fmt.Errorf("解析FLAC错误: %v", err)
	}

	ap.info.Format = FLACFormat
	ap.info.SampleRate = int(stream.Info.SampleRate)
	ap.info.Channels = int(stream.Info.NChannels)
	ap.info.BitDepth = int(stream.Info.BitsPerSample)
	ap.info.FileSize = len(ap.data)
	ap.info.TotalSamples = int64(stream.Info.NSamples)
	ap.info.Duration = float64(stream.Info.NSamples) / float64(stream.Info.SampleRate)

	return nil
}

// parseAACInfo extracts AAC metadata
func (ap *AudioProcessor) parseAACInfo() error {
	ap.info.Format = AACFormat
	ap.info.FileSize = len(ap.data)

	// Extract sample rate and channels from AAC header
	if len(ap.data) > 2 {
		// AAC ADTS header parsing
		// See: https://wiki.multimedia.cx/index.php/ADTS

		// Sample rate index is in the 3rd byte
		sampleRateIndex := (ap.data[2] & 0x3C) >> 2

		// Map sample rate index to actual sample rate
		sampleRates := []int{96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350}
		if int(sampleRateIndex) < len(sampleRates) {
			ap.info.SampleRate = sampleRates[sampleRateIndex]
		} else {
			ap.info.SampleRate = 44100 // Default
		}

		// Channel configuration is stored across 3rd and 4th bytes
		channelConfig := ((ap.data[2] & 0x01) << 2) | ((ap.data[3] & 0xC0) >> 6)
		if channelConfig > 0 && channelConfig <= 7 {
			ap.info.Channels = int(channelConfig)
		} else {
			ap.info.Channels = 2 // Default to stereo
		}
	}

	ap.info.BitDepth = 16 // AAC typically decodes to 16-bit

	// Duration estimation is not accurate without parsing the entire file
	// This would require a full AAC parser
	ap.info.Duration = 0

	return nil
}

// DecodeToPCM converts audio data to raw PCM data
func (ap *AudioProcessor) DecodeToPCM() (pcmData []byte, audioPCMFormat AudioPCMFormat, err error) {
	switch ap.info.Format {
	case WAVFormat:
		return ap.wavToPCM()
	case MP3Format:
		return ap.mp3ToPCM()
	case AACFormat:
		return ap.aacToPCM()
	case FLACFormat:
		return ap.flacToPCM()
	default:
		return nil, AudioPCMFormat{}, fmt.Errorf("不支持的PCM转换格式: %s", ap.info.Format)
	}
}

// wavToPCM extracts PCM data from WAV
func (ap *AudioProcessor) wavToPCM() ([]byte, AudioPCMFormat, error) {
	reader := bytes.NewReader(ap.data)
	wavReader := wav.NewReader(reader)

	format, err := wavReader.Format()
	if err != nil {
		return nil, AudioPCMFormat{}, fmt.Errorf("获取WAV格式错误: %v", err)
	}

	// Skip the header and get the PCM data
	pcmData, err := io.ReadAll(wavReader)
	if err != nil {
		return nil, AudioPCMFormat{}, fmt.Errorf("读取WAV数据错误: %v", err)
	}

	return pcmData, AudioPCMFormat{
		SampleRate: int(format.SampleRate),
		Channels:   int(format.NumChannels),
		BitDepth:   int(format.BitsPerSample),
	}, nil
}

// mp3ToPCM decodes MP3 to PCM
func (ap *AudioProcessor) mp3ToPCM() ([]byte, AudioPCMFormat, error) {
	reader := bytes.NewReader(ap.data)
	decoder, err := mp3.NewDecoder(reader)
	if err != nil {
		return nil, AudioPCMFormat{}, fmt.Errorf("解析MP3错误: %v", err)
	}

	var pcmBuffer bytes.Buffer
	buf := make([]byte, 8192)
	for {
		n, err := decoder.Read(buf)
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, AudioPCMFormat{}, fmt.Errorf("解码MP3错误: %v", err)
		}
		pcmBuffer.Write(buf[:n])
	}

	return pcmBuffer.Bytes(), AudioPCMFormat{
		SampleRate: decoder.SampleRate(),
		Channels:   2,
		BitDepth:   16,
	}, nil // MP3 decodes to stereo
}

// flacToPCM decodes FLAC to PCM
func (ap *AudioProcessor) flacToPCM() ([]byte, AudioPCMFormat, error) {
	reader := bytes.NewReader(ap.data)
	stream, err := flac.New(reader)
	if err != nil {
		return nil, AudioPCMFormat{}, fmt.Errorf("解析FLAC错误: %v", err)
	}

	var pcmBuffer bytes.Buffer

	for {
		frame, err := stream.ParseNext()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, AudioPCMFormat{}, fmt.Errorf("解码FLAC帧错误: %v", err)
		}

		// For each channel
		for i := 0; i < int(stream.Info.NChannels); i++ {
			// For each sample in this frame
			for j := 0; j < len(frame.Subframes[i].Samples); j++ {
				// Convert sample to bytes and write to buffer
				sample := int16(frame.Subframes[i].Samples[j])
				binary.Write(&pcmBuffer, binary.LittleEndian, sample)
			}
		}
	}

	return pcmBuffer.Bytes(), AudioPCMFormat{
		SampleRate: int(stream.Info.SampleRate),
		Channels:   int(stream.Info.NChannels),
		BitDepth:   int(stream.Info.BitsPerSample),
	}, nil
}

// aacToPCM decodes AAC to PCM
func (ap *AudioProcessor) aacToPCM() ([]byte, AudioPCMFormat, error) {
	// For AAC decoding, we'd typically use a C library like FAAD2 with CGO
	// For pure Go, there are limited options
	// This is a placeholder that would need a proper implementation with a CGO wrapper
	return nil, AudioPCMFormat{}, fmt.Errorf("纯Go环境未完全实现AAC到PCM的转换")
}

// ConvertFormat converts audio from one format to another
func (ap *AudioProcessor) ConvertFormat(targetFormat string) ([]byte, error) {
	// First decode to PCM as an intermediate format
	pcmData, audioPCMFormat, err := ap.DecodeToPCM()
	if err != nil {
		return nil, err
	}

	// Then encode PCM to target format
	return EncodePCMTo(pcmData, audioPCMFormat, targetFormat)
}

// EncodePCMTo converts PCM data to a specific format
func EncodePCMTo(pcmData []byte, audioPCMFormat AudioPCMFormat, format string) ([]byte, error) {
	switch format {
	case WAVFormat:
		return PCMToWAV(pcmData, audioPCMFormat)
	case MP3Format:
		return nil, fmt.Errorf("纯Go环境未实现MP3编码 - 需要CGO和LAME")
	case AACFormat:
		return nil, fmt.Errorf("纯Go环境未实现AAC编码 - 需要CGO和FAAC")
	case FLACFormat:
		return nil, fmt.Errorf("未实现FLAC编码")
	default:
		return nil, fmt.Errorf("不支持的目标格式: %s", format)
	}
}

// PCMToWAV converts PCM data to WAV
func PCMToWAV(pcmData []byte, audioPCMFormat AudioPCMFormat) ([]byte, error) {
	var wavBuffer bytes.Buffer

	// Create WAV writer
	wavWriter := wav.NewWriter(&wavBuffer,
		uint32(len(pcmData)/2/audioPCMFormat.Channels),
		uint16(audioPCMFormat.Channels),
		uint32(audioPCMFormat.SampleRate),
		uint16(16),
		// uint16(audioPCMFormat.BitDepth),
	)

	// Convert PCM data to samples
	numSamples := len(pcmData) / 2 // 16-bit samples
	samples := make([]wav.Sample, numSamples)

	for i := 0; i < numSamples; i++ {
		var values [2]int
		// First channel
		values[0] = int(int16(binary.LittleEndian.Uint16(pcmData[i*2*audioPCMFormat.Channels : i*2*audioPCMFormat.Channels+2])))
		// Second channel (if stereo)
		if audioPCMFormat.Channels > 1 {
			values[1] = int(int16(binary.LittleEndian.Uint16(pcmData[i*2*audioPCMFormat.Channels+2 : i*2*audioPCMFormat.Channels+4])))
		} else {
			// If mono, duplicate the value to both channels
			values[1] = values[0]
		}
		samples[i].Values = values
	}

	if err := wavWriter.WriteSamples(samples); err != nil {
		return nil, fmt.Errorf("写入WAV采样数据错误: %v", err)
	}

	return wavBuffer.Bytes(), nil
}

// ResamplePCM resamples PCM data to a new sample rate, bit depth, and channel count
// pcmData: input PCM data (16-bit, little-endian)
// srcFormat: source PCM format
// dstFormat: destination PCM format
// Returns resampled PCM data
func ResamplePCM(pcmData []byte, srcFormat, dstFormat AudioPCMFormat) ([]byte, error) {
	if len(pcmData) == 0 {
		return nil, errors.New("PCM数据为空")
	}

	// Validate source format
	bytesPerSample := srcFormat.BitDepth / 8
	if bytesPerSample < 1 || bytesPerSample > 4 {
		return nil, fmt.Errorf("不支持的源比特深度: %d", srcFormat.BitDepth)
	}

	if srcFormat.Channels < 1 || srcFormat.Channels > 8 {
		return nil, fmt.Errorf("不支持的源声道数: %d", srcFormat.Channels)
	}

	if srcFormat.SampleRate < 1 {
		return nil, fmt.Errorf("无效的源采样率: %d", srcFormat.SampleRate)
	}

	// Validate destination format
	dstBytesPerSample := dstFormat.BitDepth / 8
	if dstBytesPerSample < 1 || dstBytesPerSample > 4 {
		return nil, fmt.Errorf("不支持的目标比特深度: %d", dstFormat.BitDepth)
	}

	if dstFormat.Channels < 1 || dstFormat.Channels > 8 {
		return nil, fmt.Errorf("不支持的目标声道数: %d", dstFormat.Channels)
	}

	if dstFormat.SampleRate < 1 {
		return nil, fmt.Errorf("无效的目标采样率: %d", dstFormat.SampleRate)
	}

	// Convert bit depth and extract samples
	samples, err := extractSamples(pcmData, srcFormat)
	if err != nil {
		return nil, err
	}

	// Process channel conversion
	samples = convertChannels(samples, srcFormat.Channels, dstFormat.Channels)

	// Process sample rate conversion
	samples = convertSampleRate(samples, srcFormat.SampleRate, dstFormat.SampleRate, dstFormat.Channels)

	// Pack samples back to PCM
	return packSamples(samples, dstFormat)
}

// extractSamples reads PCM data and extracts sample values as float64
func extractSamples(pcmData []byte, srcFormat AudioPCMFormat) ([]float64, error) {
	bytesPerFrame := srcFormat.BitDepth / 8 * srcFormat.Channels
	numFrames := len(pcmData) / bytesPerFrame

	// Extract samples from all channels into a single array
	samples := make([]float64, numFrames*srcFormat.Channels)

	for i := 0; i < numFrames; i++ {
		for ch := 0; ch < srcFormat.Channels; ch++ {
			sampleIndex := i*srcFormat.Channels + ch
			byteIndex := i*bytesPerFrame + ch*(srcFormat.BitDepth/8)

			// Extract sample based on bit depth
			var rawSample int64

			switch srcFormat.BitDepth {
			case 8:
				// 8-bit audio is typically unsigned
				rawSample = int64(pcmData[byteIndex]) - 128
				samples[sampleIndex] = float64(rawSample) / 128.0 // Normalize to [-1.0, 1.0]

			case 16:
				if byteIndex+1 >= len(pcmData) {
					return nil, fmt.Errorf("PCM数据对于16位采样过短")
				}
				// 16-bit audio is typically signed, little-endian
				rawSample = int64(int16(binary.LittleEndian.Uint16(pcmData[byteIndex : byteIndex+2])))
				samples[sampleIndex] = float64(rawSample) / 32768.0 // Normalize to [-1.0, 1.0]

			case 24:
				if byteIndex+2 >= len(pcmData) {
					return nil, fmt.Errorf("PCM数据对于24位采样过短")
				}
				// 24-bit audio is typically signed, little-endian
				rawSample = int64(pcmData[byteIndex]) | int64(pcmData[byteIndex+1])<<8 | int64(pcmData[byteIndex+2])<<16
				// Sign extension for negative values
				if (rawSample & 0x800000) != 0 {
					rawSample |= int64(-1) << 24
				}
				samples[sampleIndex] = float64(rawSample) / 8388608.0 // Normalize to [-1.0, 1.0]

			case 32:
				if byteIndex+3 >= len(pcmData) {
					return nil, fmt.Errorf("PCM数据对于32位采样过短")
				}
				// 32-bit audio is typically signed, little-endian
				rawSample = int64(binary.LittleEndian.Uint32(pcmData[byteIndex : byteIndex+4]))
				if (rawSample & 0x80000000) != 0 {
					rawSample = int64(int32(rawSample))
				}
				samples[sampleIndex] = float64(rawSample) / 2147483648.0 // Normalize to [-1.0, 1.0]

			default:
				return nil, fmt.Errorf("不支持的比特深度: %d", srcFormat.BitDepth)
			}
		}
	}

	return samples, nil
}

// convertChannels changes the number of channels in the audio
func convertChannels(samples []float64, srcChannels, dstChannels int) []float64 {
	// If no change in channels, return the original samples
	if srcChannels == dstChannels {
		return samples
	}

	numFrames := len(samples) / srcChannels
	result := make([]float64, numFrames*dstChannels)

	// Convert channels
	for i := 0; i < numFrames; i++ {
		srcOffset := i * srcChannels
		dstOffset := i * dstChannels

		if srcChannels == 1 && dstChannels > 1 {
			// Mono to multi-channel (duplicate mono to all channels)
			monoSample := samples[srcOffset]
			for ch := 0; ch < dstChannels; ch++ {
				result[dstOffset+ch] = monoSample
			}
		} else if srcChannels > 1 && dstChannels == 1 {
			// Multi-channel to mono (average all channels)
			var sum float64
			for ch := 0; ch < srcChannels; ch++ {
				sum += samples[srcOffset+ch]
			}
			result[dstOffset] = sum / float64(srcChannels)
		} else {
			// General case: mix or drop channels
			for ch := 0; ch < dstChannels; ch++ {
				if ch < srcChannels {
					// Copy existing channel
					result[dstOffset+ch] = samples[srcOffset+ch]
				} else {
					// For additional channels, duplicate the last channel
					result[dstOffset+ch] = samples[srcOffset+srcChannels-1]
				}
			}
		}
	}

	return result
}

// convertSampleRate changes the sample rate of the audio using linear interpolation
func convertSampleRate(samples []float64, srcRate, dstRate, channels int) []float64 {
	// If no change in sample rate, return the original samples
	if srcRate == dstRate {
		return samples
	}

	// Calculate new length
	srcFrames := len(samples) / channels
	dstFrames := int(float64(srcFrames) * float64(dstRate) / float64(srcRate))

	result := make([]float64, dstFrames*channels)

	// Resample using linear interpolation
	for i := 0; i < dstFrames; i++ {
		// Calculate the exact position in the source
		srcPos := float64(i) * float64(srcRate) / float64(dstRate)
		srcIndex := int(srcPos)
		frac := srcPos - float64(srcIndex) // Fractional part for interpolation

		// Linear interpolation between adjacent samples for each channel
		for ch := 0; ch < channels; ch++ {
			if srcIndex < srcFrames-1 {
				// Normal case - interpolate between two adjacent samples
				sample1 := samples[srcIndex*channels+ch]
				sample2 := samples[(srcIndex+1)*channels+ch]
				result[i*channels+ch] = sample1 + frac*(sample2-sample1)
			} else if srcIndex < srcFrames {
				// Last frame, no next sample to interpolate with
				result[i*channels+ch] = samples[srcIndex*channels+ch]
			} else {
				// Beyond source data (shouldn't happen with our calculation)
				result[i*channels+ch] = 0
			}
		}
	}

	return result
}

// packSamples converts normalized float64 samples back to PCM bytes
func packSamples(samples []float64, dstFormat AudioPCMFormat) ([]byte, error) {
	numFrames := len(samples) / dstFormat.Channels
	bytesPerFrame := dstFormat.Channels * (dstFormat.BitDepth / 8)
	result := make([]byte, numFrames*bytesPerFrame)

	for i := 0; i < numFrames; i++ {
		for ch := 0; ch < dstFormat.Channels; ch++ {
			sampleIndex := i*dstFormat.Channels + ch
			byteIndex := i*bytesPerFrame + ch*(dstFormat.BitDepth/8)

			// Clamp the sample value to [-1.0, 1.0]
			sample := samples[sampleIndex]
			if sample < -1.0 {
				sample = -1.0
			} else if sample > 1.0 {
				sample = 1.0
			}

			// Convert to appropriate bit depth
			switch dstFormat.BitDepth {
			case 8:
				// 8-bit PCM is typically unsigned [0, 255]
				value := byte(sample*127.0 + 128.0)
				result[byteIndex] = value

			case 16:
				// 16-bit PCM is typically signed [-32768, 32767]
				value := int16(sample * 32767.0)
				binary.LittleEndian.PutUint16(result[byteIndex:byteIndex+2], uint16(value))

			case 24:
				// 24-bit PCM is typically signed [-8388608, 8388607]
				value := int32(sample * 8388607.0)
				result[byteIndex] = byte(value)
				result[byteIndex+1] = byte(value >> 8)
				result[byteIndex+2] = byte(value >> 16)

			case 32:
				// 32-bit PCM is typically signed [-2147483648, 2147483647]
				value := int32(sample * 2147483647.0)
				binary.LittleEndian.PutUint32(result[byteIndex:byteIndex+4], uint32(value))

			default:
				return nil, fmt.Errorf("不支持的输出比特深度: %d", dstFormat.BitDepth)
			}
		}
	}

	return result, nil
}
