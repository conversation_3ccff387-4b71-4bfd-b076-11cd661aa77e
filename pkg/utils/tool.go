package utils

import (
	"encoding/binary"
	"fmt"
	"os"
	"runtime"
	"time"
)

const (
	// WAV文件头常量
	RIFF_HEADER_SIZE = 44
	FORMAT_PCM       = 1
	NUM_CHANNELS     = 1
	BITS_PER_SAMPLE  = 16
)

// WAV文件头结构
type WavHeader struct {
	ChunkID       [4]byte // "RIFF"
	ChunkSize     uint32  // 文件大小 - 8
	Format        [4]byte // "WAVE"
	Subchunk1ID   [4]byte // "fmt "
	Subchunk1Size uint32  // 16 for PCM
	AudioFormat   uint16  // 1 for PCM
	NumChannels   uint16  // 1 for mono
	SampleRate    uint32  // 16000
	ByteRate      uint32  // SampleRate * NumChannels * BitsPerSample/8
	BlockAlign    uint16  // NumChannels * BitsPerSample/8
	BitsPerSample uint16  // 16
	Subchunk2ID   [4]byte // "data"
	Subchunk2Size uint32  // data size
}

func getAudioDir() string {
	switch runtime.GOOS {
	case "darwin", "windows": // macOS 和 Windows
		return "/tmp/tts_audio"
	default: // Linux 和其他系统
		return "/mnt/disk0/zhaolu/tts_audio"
	}
}

func SaveAudio(audioData []byte) error {
	return SaveToWav(audioData)
}

// SaveToWav 保存音频数据为WAV文件
func SaveToWav(audioData []byte) error {
	// 确保 audio 目录存在
	savePath := fmt.Sprintf("%s/audio_doll99/%s", getAudioDir(), time.Now().Format("20060102"))
	if err := os.MkdirAll(savePath, 0755); err != nil {
		return fmt.Errorf("创建audio目录失败: %v", err)
	}

	// 生成文件名
	filename := fmt.Sprintf("%s/audio_%s.wav", savePath, time.Now().Format("20060102_150405"))

	// 创建WAV文件头
	header := WavHeader{
		ChunkID:       [4]byte{'R', 'I', 'F', 'F'},
		Format:        [4]byte{'W', 'A', 'V', 'E'},
		Subchunk1ID:   [4]byte{'f', 'm', 't', ' '},
		Subchunk1Size: 16,
		AudioFormat:   FORMAT_PCM,
		NumChannels:   NUM_CHANNELS,
		SampleRate:    16000,
		BitsPerSample: BITS_PER_SAMPLE,
		Subchunk2ID:   [4]byte{'d', 'a', 't', 'a'},
		Subchunk2Size: uint32(len(audioData)),
	}

	// 计算其他字段
	header.ByteRate = header.SampleRate * uint32(header.NumChannels) * uint32(header.BitsPerSample) / 8
	header.BlockAlign = header.NumChannels * header.BitsPerSample / 8
	header.ChunkSize = 36 + header.Subchunk2Size

	// 创建文件
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建WAV文件失败: %v", err)
	}
	defer file.Close()

	// 写入文件头
	if err := binary.Write(file, binary.LittleEndian, &header); err != nil {
		return fmt.Errorf("写入WAV文件头失败: %v", err)
	}

	// 写入音频数据
	if _, err := file.Write(audioData); err != nil {
		return fmt.Errorf("写入音频数据失败: %v", err)
	}

	return nil
}

func IsAudioGzip(audioData []byte) bool {
	return len(audioData) >= 3 && audioData[0] == 0x1F && audioData[1] == 0x8B && audioData[2] == 0x08
}
