package utils

import (
	"context"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"syscall"
	"time"

	"aigc_server/pkg/logger"
	"aigc_server/pkg/utils/RtcToken"
)

func GetRTCServerUserId(uid string) string {
	return fmt.Sprintf("rtc_server_%s", uid)
}

// GetProcessKey generates a process ID from user ID
func GetProcessKey(uid string) string {
	return fmt.Sprintf("rtc_%s", uid)
}
func GenerateRoomId(uid string) string {
	ts := time.Now().Unix()
	return fmt.Sprintf("G711A_doll_%s_%d", uid, ts)
}

func GenerateRoomToken(appID, appKey, roomID, uid string) (string, string, error) {
	t := RtcToken.New(appID, appKey, roomID, uid)
	// 添加此 Token 的有效时间，24小时过期。过期后，你无法使用此 Token 进房。并且踢出token过期的人
	expireTime := time.Now().Add(time.Hour * 24)
	t.ExpireTime(expireTime)
	// 添加订阅流权限
	t.AddPrivilege(RtcToken.PrivSubscribeStream, expireTime)
	// 添加发布流权限
	t.AddPrivilege(RtcToken.PrivPublishStream, expireTime)
	// 获取最终生成的 token
	token, err := t.Serialize()

	return token, expireTime.Format(time.RFC3339), err
}

func CalculateRMS(buffer []float32) float64 {
	var sum float64
	for _, sample := range buffer {
		sum += float64(sample) * float64(sample)
	}
	meanSquare := sum / float64(len(buffer))
	return math.Sqrt(meanSquare)
}

// CloseProcess 优雅关闭子进程
func CloseProcess(pid int, timeout time.Duration) error {
	// 获取进程引用
	process, err := os.FindProcess(pid)
	if err != nil {
		return fmt.Errorf("查找进程失败(pid=%d): %w", pid, err)
	}

	// 首先发送SIGTERM信号
	if err := process.Signal(syscall.SIGTERM); err != nil {
		if err.Error() == "os: process already finished" {
			return nil
		}
		return fmt.Errorf("发送SIGTERM信号失败(pid=%d): %w", pid, err)
	}

	// 创建一个通道用于等待进程退出
	done := make(chan error, 1)
	go func() {
		_, err := process.Wait()
		done <- err
	}()

	// 等待进程退出或超时
	select {
	case err := <-done:
		if err != nil {
			return fmt.Errorf("等待进程退出完成(pid=%d): %w", pid, err)
		}
		return nil
	case <-time.After(timeout):
		// 超时后强制结束进程
		if err := process.Kill(); err != nil {
			if err.Error() == "os: process already finished" {
				return nil
			}
			return fmt.Errorf("强制终止进程失败(pid=%d): %w", pid, err)
		}
		// 等待进程被kill
		select {
		case err := <-done:
			if err != nil {
				return fmt.Errorf("等待进程强制终止失败(pid=%d): %w", pid, err)
			}
			return nil
		case <-time.After(2 * time.Second):
			return fmt.Errorf("进程强制终止超时(pid=%d)", pid)
		}
	}
}

// IsProcessExist 检查进程是否存在
func IsProcessExist(pid int) bool {
	process, err := os.FindProcess(pid)
	if err != nil {
		return false
	}

	// 在Unix系统中，FindProcess总是成功的，需要发送信号0来检查进程是否真实存在
	err = process.Signal(syscall.Signal(0))
	return err == nil
}

func Filter[T any](slice []T, filter func(T) bool) []T {
	var result []T = make([]T, 0, len(slice))
	for _, item := range slice {
		if filter(item) {
			result = append(result, item)
		}
	}
	return result
}

func WriteToFile(filePath string, data []byte) error {
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}
	return os.WriteFile(filePath, data, 0644)
}

// func GetWriteAudioSwitcher() bool {
// 	return true
// }

func GetRtcRecvSaveFilePath(uid string) string {
	return fmt.Sprintf("audio/%s_%s_rtc_recv.pcm", time.Now().Format("20060102_150405"), uid)
}
func GetTtsRecvSaveFilePath(uid string) string {
	return fmt.Sprintf("audio/%s_%s_tts_recv.pcm", time.Now().Format("20060102_150405"), uid)
}
func GetRtcSendSaveFilePath(uid string) string {
	return fmt.Sprintf("audio/%s_%s_rtc_send.pcm", time.Now().Format("20060102_150405"), uid)
}
func GetWriteablePath() string {
	writeablePath, err := os.Executable()
	if err != nil {
		logger.Error(fmt.Errorf("获取可执行文件路径失败: %w", err).Error())
		return ""
	}
	return filepath.Join(filepath.Dir(writeablePath), "run")
}

func ContinueWriteAppendData(ctx context.Context, writePath string) chan []byte {
	dataChan := make(chan []byte, 500)
	go func() {
		writeablePath := GetWriteablePath()
		writePath = filepath.Join(writeablePath, writePath)
		dir := filepath.Dir(writePath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			logger.Error(fmt.Errorf("创建目录失败: %w", err).Error())
			return
		}
		file, err := os.OpenFile(writePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			logger.Error(fmt.Errorf("打开文件失败: %w", err).Error())
		}
		defer file.Close()

		for {
			select {
			case <-ctx.Done():
				close(dataChan)
				return
			case data := <-dataChan:
				if _, err := file.Write(data); err != nil {
					logger.Error(fmt.Errorf("写入文件失败: %w", err).Error())
					return
				}
			}
		}
	}()

	return dataChan
}

func TraceRecover() {
	if err := recover(); err != nil {
		logger.RecoverWithStack(err)
	}
}
