# 协议定义

## protos 目录下是所有 proto 

[protoc version: 29.0](https://github.com/protocolbuffers/protobuf/releases/tag/v29.0)

## 不同语言生成在自己的目录下

### go

1. 下载 29.0 的 protoc-29.0-linux-x86_64
2. 解压到本地，如 ~/lib/protoc-29.0-linux-x86_64
3. ~/.zshrc 下加入 export PATH=$PATH:$HOME/.local/bin 
4. go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
5. go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
6. ~/.zshrc 下加入 export PATH=$PATH:$(go env GOPATH)/bin
7. source ~/.zshrc
8. ./gen_go.sh
