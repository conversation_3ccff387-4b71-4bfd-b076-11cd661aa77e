syntax = "proto3";

package doll.llm;

option go_package = "aigc.proto;proto";

enum LLMRespType {
  NORMAL = 0;
  RESUME = 1;
  RESET = 2;
}

message LLMRequest {
  bool is_final = 1; // asr 识别是否一句话结束
  string content = 2;
  map<string, string> tool_call = 3; // fn -> fn_msg
}

message LLMResponse {
  LLMRespType type = 1;
  string content = 2;
  int32  msg_index = 3;
  int32  msg_segment_index = 4;
  bool   is_final = 5;
  map<string, string> tool_call = 6; // fn -> fn_msg
}

service LLMChat {
  rpc Chat(stream LLMRequest) returns (stream LLMResponse);
}