syntax = "proto3";

package doll.risk_control;

option go_package = "aigc.proto;proto";

enum RiskTextType {
    RISK_PROMPT = 0;
    RISK_RESPONSE = 1;
}

message RiskControlRequest {
  string text = 1;
  string user_id = 2;
  RiskTextType risk_text_type = 3;
}   

message RiskControlResponse {
  string request_id = 1;
  bool is_risk = 2;
}

service RiskControl {
  rpc RiskControl(stream RiskControlRequest) returns (stream RiskControlResponse);
}
