syntax = "proto3";

package doll.tts;

option go_package = "aigc.proto;proto";

message TTSRequest {
  string text = 1;
  string voice_type = 2;
  int32 msg_index = 3;
  int32 msg_segment_index = 4;
  bool is_custom_tts = 5;
  bool is_reset = 6;
  bool is_final = 7;
}   

message TTSResponse {
  bytes audio = 1; // 16k sample rate
  int32 msg_index = 2;
  int32 msg_segment_index = 3;
  bool is_final = 4;
}

service TTS {
  rpc TTS(stream TTSRequest) returns (stream TTSResponse);
}
