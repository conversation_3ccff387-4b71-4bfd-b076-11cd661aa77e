#!/bin/bash
# 发布Docker镜像到阿里云容器镜像服务的脚本

set -e

# 脚本参数说明
if [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
  echo "使用方法: $0 [版本标签] [区域]"
  echo "参数说明:"
  echo "  版本标签: Docker镜像的版本标签，默认为'latest'"
  echo "  区域: 阿里云区域，默认为'cn-hangzhou'"
  echo "示例: $0 v1.0.0 cn-beijing"
  exit 0
fi

# 获取脚本参数
VERSION_TAG=${1:-latest}
REGION=${2:-cn-hangzhou}

# 阿里云容器仓库的相关信息 (需要替换)
ACR_NAMESPACE="替换为您的命名空间"
ACR_REPOSITORY="aigc-server"
ACR_REGISTRY="${REGION}.aliyuncs.com"
ACR_URL="${ACR_NAMESPACE}.${ACR_REGISTRY}"

# 获取Git信息
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

echo "======================================================"
echo "构建并推送 AIGC Server 到阿里云容器镜像服务"
echo "======================================================"
echo "镜像标签: ${VERSION_TAG}"
echo "Git Commit: ${GIT_COMMIT}"
echo "构建时间: ${BUILD_TIME}"
echo "阿里云区域: ${REGION}"
echo "目标仓库: ${ACR_URL}/${ACR_REPOSITORY}:${VERSION_TAG}"
echo "======================================================"

# 确认继续
read -p "是否继续? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo "操作已取消"
  exit 1
fi

# 登录到阿里云容器镜像服务
echo "登录到阿里云容器镜像服务..."
docker login --username=${ACR_USERNAME} ${ACR_URL}

# 构建Docker镜像
echo "构建Docker镜像..."
docker build \
  --build-arg VERSION=${VERSION_TAG} \
  --build-arg BUILD_TIME="${BUILD_TIME}" \
  --build-arg GIT_COMMIT=${GIT_COMMIT} \
  -t ${ACR_URL}/${ACR_REPOSITORY}:${VERSION_TAG} .

# 推送镜像到阿里云容器镜像服务
echo "推送镜像到阿里云容器镜像服务..."
docker push ${ACR_URL}/${ACR_REPOSITORY}:${VERSION_TAG}

echo "======================================================"
echo "镜像已成功推送到阿里云容器镜像服务!"
echo "镜像地址: ${ACR_URL}/${ACR_REPOSITORY}:${VERSION_TAG}"
echo "======================================================"
