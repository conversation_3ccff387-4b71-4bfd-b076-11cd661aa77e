package rtcengine

import (
	"bytedance/bytertc/native"
	"runtime"
)

type audioFrameProxyImp struct {
	nativeAudioFrame native.IAudioFrame
	noCopy
}

func deleteAudioFrameProxyImp(f *audioFrameProxyImp) {
	f.release()
}

func newAudioFrameProxyImp(nativeAudioFrame native.IAudioFrame, needRelease bool) *audioFrameProxyImp {
	tmp := &audioFrameProxyImp{
		nativeAudioFrame: nativeAudioFrame,
	}
	if needRelease {
		runtime.SetFinalizer(tmp, deleteAudioFrameProxyImp)
	}
	return tmp
}

func (f *audioFrameProxyImp) TimestampUs() int64 {
	return f.nativeAudioFrame.TimestampUs()
}
func (f *audioFrameProxyImp) SampleRate() AudioSampleRate {
	return AudioSampleRate(f.nativeAudioFrame.SampleRate())
}
func (f *audioFrameProxyImp) Channel() AudioChannel {
	return AudioChannel(f.nativeAudioFrame.Channel())
}
func (f *audioFrameProxyImp) Data() []byte {
	// explicitly copy the slice to decouple with the underlying AudioFrame lifetime
	originalData := native.GetDataChunkFromIAudioFrame(f.nativeAudioFrame)
	data := make([]byte, len(originalData))
	copy(data, originalData)
	return data

}
func (f *audioFrameProxyImp) DataSize() int {
	return f.nativeAudioFrame.DataSize()
}
func (f *audioFrameProxyImp) FrameType() AudioFrameType {
	return AudioFrameType(f.nativeAudioFrame.FrameType())
}
func (f *audioFrameProxyImp) IsMutedData() bool {
	return f.nativeAudioFrame.IsMutedData()
}

func (f *audioFrameProxyImp) toNativeAudioFrame() native.IAudioFrame {
	return f.nativeAudioFrame
}

func (f *audioFrameProxyImp) release() {
	f.nativeAudioFrame.Release()
}
