package rtcengine

import (
	"bytedance/bytertc/native"
)

type IAudioFrameObserver interface {
	OnRecordAudioFrameOriginal(audioFrame IAudioFrame)
	OnRecordAudioFrame(audioFrame IAudioFrame)
	OnPlaybackAudioFrame(audioFrame IAudioFrame)
	OnRemoteUserAudioFrame(streamInfo RemoteStreamKey, audioFrame IAudioFrame)
	OnMixedAudioFrame(audioFrame IAudioFrame)
	OnRecordScreenAudioFrame(audioFrame IAudioFrame)
	OnCaptureMixedAudioFrame(audioFrame IAudioFrame)
}

type IAudioFrameObserverDefaultImp struct {
}

func (h *IAudioFrameObserverDefaultImp) OnRecordAudioFrameOriginal(audioFrame IAudioFrame) {}
func (h *IAudioFrameObserverDefaultImp) OnRecordAudioFrame(audioFrame IAudioFrame)         {}
func (h *IAudioFrameObserverDefaultImp) OnPlaybackAudioFrame(audioFrame IAudioFrame)       {}
func (h *IAudioFrameObserverDefaultImp) OnRemoteUserAudioFrame(streamInfo RemoteStreamKey, audioFrame IAudioFrame) {
}
func (h *IAudioFrameObserverDefaultImp) OnMixedAudioFrame(audioFrame IAudioFrame)        {}
func (h *IAudioFrameObserverDefaultImp) OnRecordScreenAudioFrame(audioFrame IAudioFrame) {}
func (h *IAudioFrameObserverDefaultImp) OnCaptureMixedAudioFrame(audioFrame IAudioFrame) {}

type nativeIAudioFrameObserverImp struct {
	GoHandler IAudioFrameObserver
}

func (h *nativeIAudioFrameObserverImp) OnRecordAudioFrameOriginal(arg2 native.IAudioFrame) {
	h.GoHandler.OnRecordAudioFrameOriginal(newAudioFrameProxyImp(arg2, false))
}
func (h *nativeIAudioFrameObserverImp) OnRecordAudioFrame(arg2 native.IAudioFrame) {
	h.GoHandler.OnRecordAudioFrame(newAudioFrameProxyImp(arg2, false))
}
func (h *nativeIAudioFrameObserverImp) OnPlaybackAudioFrame(arg2 native.IAudioFrame) {
	h.GoHandler.OnPlaybackAudioFrame(newAudioFrameProxyImp(arg2, false))
}
func (h *nativeIAudioFrameObserverImp) OnRemoteUserAudioFrame(arg2 native.RemoteStreamKey, arg3 native.IAudioFrame) {
	h.GoHandler.OnRemoteUserAudioFrame(toRemoteStreamKey(arg2), newAudioFrameProxyImp(arg3, false))
}
func (h *nativeIAudioFrameObserverImp) OnMixedAudioFrame(arg2 native.IAudioFrame) {
	h.GoHandler.OnMixedAudioFrame(newAudioFrameProxyImp(arg2, false))
}
func (h *nativeIAudioFrameObserverImp) OnRecordScreenAudioFrame(arg2 native.IAudioFrame) {
	h.GoHandler.OnRecordScreenAudioFrame(newAudioFrameProxyImp(arg2, false))
}
func (h *nativeIAudioFrameObserverImp) OnCaptureMixedAudioFrame(arg2 native.IAudioFrame) {
	h.GoHandler.OnCaptureMixedAudioFrame(newAudioFrameProxyImp(arg2, false))
}
