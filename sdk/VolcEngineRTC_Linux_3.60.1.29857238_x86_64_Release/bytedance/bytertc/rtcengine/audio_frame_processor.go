package rtcengine

import "bytedance/bytertc/native"

type IAudioFrameProcessor interface {
	OnProcessRecordAudioFrame(audioFrame IAudioFrame) int
	OnProcessPlayBackAudioFrame(audioFrame IAudioFrame) int
	OnProcessRemoteUserAudioFrame(streamInfo RemoteStreamKey, audioFrame IAudioFrame) int
	OnProcessEarMonitorAudioFrame(audioFrame IAudioFrame) int
	OnProcessScreenAudioFrame(audioFrame IAudioFrame) int
}

type IAudioFrameProcessorDefaultImp struct{}

func (p IAudioFrameProcessorDefaultImp) OnProcessRecordAudioFrame(audioFrame IAudioFrame) int {
	return 0
}
func (p IAudioFrameProcessorDefaultImp) OnProcessPlayBackAudioFrame(audioFrame IAudioFrame) int {
	return 0
}
func (p IAudioFrameProcessorDefaultImp) OnProcessRemoteUserAudioFrame(streamInfo RemoteStreamKey, audioFrame IAudioFrame) int {
	return 0
}
func (p IAudioFrameProcessorDefaultImp) OnProcessEarMonitorAudioFrame(audioFrame IAudioFrame) int {
	return 0
}
func (p IAudioFrameProcessorDefaultImp) OnProcessScreenAudioFrame(audioFrame IAudioFrame) int {
	return 0
}

type nativeIAudioFrameProcessorImp struct {
	GoHandler IAudioFrameProcessor
}

func (h *nativeIAudioFrameProcessorImp) OnProcessRecordAudioFrame(arg2 native.IAudioFrame) (_swig_ret int) {
	return h.GoHandler.OnProcessRecordAudioFrame(toIAudioFrame(arg2))
}
func (h *nativeIAudioFrameProcessorImp) OnProcessPlayBackAudioFrame(arg2 native.IAudioFrame) (_swig_ret int) {
	return h.GoHandler.OnProcessPlayBackAudioFrame(toIAudioFrame(arg2))
}
func (h *nativeIAudioFrameProcessorImp) OnProcessRemoteUserAudioFrame(arg2 native.RemoteStreamKey, arg3 native.IAudioFrame) (_swig_ret int) {
	return h.GoHandler.OnProcessRemoteUserAudioFrame(toRemoteStreamKey(arg2), toIAudioFrame(arg3))
}
func (h *nativeIAudioFrameProcessorImp) OnProcessEarMonitorAudioFrame(arg2 native.IAudioFrame) (_swig_ret int) {
	return h.GoHandler.OnProcessEarMonitorAudioFrame(toIAudioFrame(arg2))
}
func (h *nativeIAudioFrameProcessorImp) OnProcessScreenAudioFrame(arg2 native.IAudioFrame) (_swig_ret int) {
	return h.GoHandler.OnProcessScreenAudioFrame(toIAudioFrame(arg2))
}
