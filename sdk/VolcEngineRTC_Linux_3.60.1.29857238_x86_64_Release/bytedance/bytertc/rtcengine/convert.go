package rtcengine

/*
#include <stdlib.h>
#include <stdio.h>
*/
import "C"
import (
	"bytedance/bytertc/native"
	"encoding/json"
)

func toVideoFrameInfo(nativeEntity native.VideoFrameInfo) VideoFrameInfo {
	return VideoFrameInfo{Width: nativeEntity.GetWidth(), Height: nativeEntity.GetHeight(), Rotation: FallbackOrRecoverReason(nativeEntity.GetRotation())}
}

func toNativeVideoFrameInfo(entity *VideoFrameInfo) native.VideoFrameInfo {
	nativeEntity := native.NewVideoFrameInfo()
	nativeEntity.SetWidth(entity.Width)
	nativeEntity.SetHeight(entity.Height)
	nativeEntity.SetRotation(entity.Rotation)
	return nativeEntity
}

func deleteNativeVideoFrameInfo(entity native.VideoFrameInfo) {
	native.DeleteVideoFrameInfo(entity)
}

func toRemoteStreamSwitch(nativeEntity native.RemoteStreamSwitch) RemoteStreamSwitch {
	return RemoteStreamSwitch{UID: nativeEntity.GetUid(), IsScreen: nativeEntity.GetIs_screen(), BeforeVideoIndex: nativeEntity.GetBefore_video_index(), AfterVideoIndex: nativeEntity.GetAfter_video_index(), BeforeEnable: nativeEntity.GetBefore_enable(), AfterEnable: nativeEntity.GetAfter_enable(), Reason: nativeEntity.GetReason()}
}

func toNativeRemoteStreamSwitch(entity *RemoteStreamSwitch) native.RemoteStreamSwitch {
	nativeEntity := native.NewRemoteStreamSwitch()
	nativeEntity.SetUid(entity.UID)
	nativeEntity.SetIs_screen(entity.IsScreen)
	nativeEntity.SetBefore_video_index(entity.BeforeVideoIndex)
	nativeEntity.SetAfter_video_index(entity.AfterVideoIndex)
	nativeEntity.SetBefore_enable(entity.BeforeEnable)
	nativeEntity.SetAfter_enable(entity.AfterEnable)
	nativeEntity.SetReason(entity.Reason)
	return nativeEntity
}
func deleteNativeRemoteStreamSwitch(entity native.RemoteStreamSwitch) {
	native.DeleteRemoteStreamSwitchMembers(entity)
	native.DeleteRemoteStreamSwitch(entity)
}

func toAudioFormat(nativeEntity native.AudioFormat) AudioFormat {
	return AudioFormat{SampleRate: AudioSampleRate(nativeEntity.GetSample_rate()), Channel: AudioChannel(nativeEntity.GetChannel()), SamplesPerCall: nativeEntity.GetSamples_per_call()}
}

func toNativeAudioFormat(entity *AudioFormat) native.AudioFormat {
	nativeEntity := native.NewAudioFormat()
	nativeEntity.SetSample_rate(entity.SampleRate)
	nativeEntity.SetChannel(entity.Channel)
	nativeEntity.SetSamples_per_call(entity.SamplesPerCall)
	return nativeEntity
}
func deleteNativeAudioFormat(entity native.AudioFormat) {
	native.DeleteAudioFormat(entity)
}

func toAudioPropertiesInfo(nativeEntity native.AudioPropertiesInfo) AudioPropertiesInfo {
	return AudioPropertiesInfo{LinearVolume: nativeEntity.GetLinear_volume(), NonlinearVolume: nativeEntity.GetNonlinear_volume(), Spectrum: native.GetSpectrumFromAudioPropertiesInfo(nativeEntity), Vad: nativeEntity.GetVad(), VoicePitch: nativeEntity.GetVoice_pitch()}
}

func toLocalAudioPropertiesInfo(nativeEntity native.LocalAudioPropertiesInfo) LocalAudioPropertiesInfo {
	return LocalAudioPropertiesInfo{StreamIndex: StreamIndex(nativeEntity.GetStream_index()), AudioPropertiesInfo: toAudioPropertiesInfo(nativeEntity.GetAudio_properties_info())}
}

func toRemoteAudioPropertiesInfo(nativeEntity native.RemoteAudioPropertiesInfo) RemoteAudioPropertiesInfo {
	return RemoteAudioPropertiesInfo{StreamKey: toRemoteStreamKey(nativeEntity.GetStream_key()), AudioPropertiesInfo: toAudioPropertiesInfo(nativeEntity.GetAudio_properties_info())}
}

func toEngineConfig(nativeEntity native.EngineConfig) EngineConfig {
	engineParameters := make(map[string]interface{})
	err := json.Unmarshal([]byte(nativeEntity.GetParameters()), engineParameters)
	if err == nil {
		return EngineConfig{AppID: nativeEntity.GetApp_id(), Parameters: nil}
	}
	return EngineConfig{AppID: nativeEntity.GetApp_id(), Parameters: engineParameters}
}

func toNativeEngineConfig(entity *EngineConfig) native.EngineConfig {
	nativeEntity := native.NewEngineConfig()
	nativeEntity.SetApp_id(entity.AppID)
	engineParametersBytes, err := json.Marshal(entity.Parameters)
	var engineParametersString string
	if err != nil {
		engineParametersString = ""
	} else {
		engineParametersString = string(engineParametersBytes)
	}
	nativeEntity.SetParameters(engineParametersString)
	return nativeEntity
}
func deleteNativeEngineConfig(entity native.EngineConfig) {
	native.DeleteEngineConfigMembers(entity)
	native.DeleteEngineConfig(entity)
}

func toUserInfo(nativeEntity native.UserInfo) UserInfo {
	return UserInfo{UID: nativeEntity.GetUid(), ExtraInfo: nativeEntity.GetExtra_info()}
}

func toNativeUserInfo(entity *UserInfo) native.UserInfo {
	nativeEntity := native.NewUserInfo()
	nativeEntity.SetUid(entity.UID)
	nativeEntity.SetExtra_info(entity.ExtraInfo)
	return nativeEntity
}
func deleteNativeUserInfo(entity native.UserInfo) {
	native.DeleteUserInfoMembers(entity)
	native.DeleteUserInfo(entity)
}

func toDeadLockMsg(nativeEntity native.DeadLockMsg) DeadLockMsg {
	return DeadLockMsg{BlockSessionID: nativeEntity.GetBlock_session_id(), BlockingPaths: nativeEntity.GetBlocking_paths(), IsCritical: nativeEntity.GetIs_critical()}
}

func toNativeDeadLockMsg(entity *DeadLockMsg) native.DeadLockMsg {
	nativeEntity := native.NewDeadLockMsg()
	nativeEntity.SetBlock_session_id(entity.BlockSessionID)
	nativeEntity.SetBlocking_paths(entity.BlockingPaths)
	nativeEntity.SetIs_critical(entity.IsCritical)
	return nativeEntity
}

func deleteNativeDeadLockMsg(entity native.DeadLockMsg) {
	native.DeleteDeleteDeadLockMsgMembers(entity)
	native.DeleteDeadLockMsg(entity)
}

func toRTCRoomConfig(nativeEntity native.RTCRoomConfig) RTCRoomConfig {
	return RTCRoomConfig{RoomProfileType: RoomProfileType(nativeEntity.GetRoom_profile_type()), IsPublishAudio: nativeEntity.GetIs_publish_audio(), IsPublishVideo: nativeEntity.GetIs_publish_video(), IsAutoSubscribeAudio: nativeEntity.GetIs_auto_subscribe_audio(), IsAutoSubscribeVideo: nativeEntity.GetIs_auto_subscribe_video()}
}

func toNativeRTCRoomConfig(entity *RTCRoomConfig) native.RTCRoomConfig {
	nativeEntity := native.NewRTCRoomConfig()
	nativeEntity.SetRoom_profile_type(entity.RoomProfileType)
	nativeEntity.SetIs_publish_audio(entity.IsPublishAudio)
	nativeEntity.SetIs_publish_video(entity.IsPublishVideo)
	nativeEntity.SetIs_auto_subscribe_audio(entity.IsAutoSubscribeAudio)
	nativeEntity.SetIs_auto_subscribe_video(entity.IsAutoSubscribeVideo)
	return nativeEntity
}
func deleteNativeRTCRoomConfig(entity native.RTCRoomConfig) {
	native.DeleteRTCRoomConfig(entity)
}

func toSysStats(nativeEntity native.SysStats) SysStats {
	return SysStats{CpuCores: nativeEntity.GetCpu_cores(), CpuAppUsage: nativeEntity.GetCpu_app_usage(), CpuTotalUsage: nativeEntity.GetCpu_total_usage(), MemoryUsage: nativeEntity.GetMemory_usage(), FullMemory: float64(nativeEntity.GetFull_memory()), TotalMemoryUsage: nativeEntity.GetTotal_memory_usage(), FreeMemory: nativeEntity.GetFree_memory()}
}

func toNativeSysStats(entity *SysStats) native.SysStats {
	nativeEntity := native.NewSysStats()
	nativeEntity.SetCpu_cores(entity.CpuCores)
	nativeEntity.SetCpu_app_usage(entity.CpuAppUsage)
	nativeEntity.SetCpu_total_usage(entity.CpuTotalUsage)
	nativeEntity.SetMemory_usage(entity.MemoryUsage)
	nativeEntity.SetFull_memory(uint64(entity.FullMemory))
	nativeEntity.SetTotal_memory_usage(entity.TotalMemoryUsage)
	nativeEntity.SetFree_memory(entity.FreeMemory)
	nativeEntity.SetMemory_ratio(entity.MemoryRatio)
	nativeEntity.SetTotal_memory_ratio(entity.TotalMemoryRatio)
	return nativeEntity
}
func deleteNativeSysStats(entity native.SysStats) {
	native.DeleteSysStats(entity)
}

func toRecordingProgress(nativeEntity native.RecordingProgress) RecordingProgress {
	return RecordingProgress{Duration: nativeEntity.GetDuration(), FileSize: nativeEntity.GetFile_size()}
}

func toNativeRecordingProgress(entity *RecordingProgress) native.RecordingProgress {
	nativeEntity := native.NewRecordingProgress()
	nativeEntity.SetDuration(entity.Duration)
	nativeEntity.SetFile_size(entity.FileSize)
	return nativeEntity
}

func deleteNativeRecordingProgress(entity native.RecordingProgress) {
	native.DeleteRecordingProgress(entity)
}

func toRTCUser(nativeEntity native.RtcUser) RTCUser {
	return RTCUser{UserID: nativeEntity.GetUser_id(), MetaData: nativeEntity.GetMeta_data()}
}

func toNativeRTCUser(entity *RTCUser) native.RtcUser {
	nativeEntity := native.NewRtcUser()
	nativeEntity.SetUser_id(entity.UserID)
	nativeEntity.SetMeta_data(entity.MetaData)
	return nativeEntity
}

func deleteNativeRTCUser(entity native.RtcUser) {
	native.DeleteRtcUserMembers(entity)
	native.DeleteRtcUser(entity)
}

func toRemoteStreamKey(nativeEntity native.RemoteStreamKey) RemoteStreamKey {
	return RemoteStreamKey{RoomID: nativeEntity.GetRoom_id(), UserID: nativeEntity.GetUser_id(), StreamIndex: nativeEntity.GetStream_index()}
}

func toNativeRemoteStreamKey(entity *RemoteStreamKey) native.RemoteStreamKey {
	nativeEntity := native.NewRemoteStreamKey()
	nativeEntity.SetRoom_id(entity.RoomID)
	nativeEntity.SetUser_id(entity.UserID)
	nativeEntity.SetStream_index(entity.StreamIndex)
	return nativeEntity
}

func deleteNativeRemoteStreamKey(entity native.RemoteStreamKey) {
	native.DeleteRemoteStreamKeyMembers(entity)
	native.DeleteRemoteStreamKey(entity)
}

func toSourceWantedData(nativeEntity native.SourceWantedData) SourceWantedData {
	return SourceWantedData{Width: nativeEntity.GetWidth(), Height: nativeEntity.GetHeight(), FrameRate: nativeEntity.GetFrame_rate()}
}

func toNativeSourceWantedData(entity *SourceWantedData) native.SourceWantedData {
	nativeEntity := native.NewSourceWantedData()
	nativeEntity.SetWidth(entity.Width)
	nativeEntity.SetHeight(entity.Height)
	nativeEntity.SetFrame_rate(entity.FrameRate)
	return nativeEntity
}

func deleteNativeSourceWantedData(entity native.SourceWantedData) {
	native.DeleteSourceWantedData(entity)
}

func toServerACKMsg(nativeEntity native.ServerACKMsg) ServerACKMsg {
	return ServerACKMsg{Length: nativeEntity.GetLength(), AckMsg: nativeEntity.GetAck_msg()}
}

func toNativeServerACKMsg(entity *ServerACKMsg) native.ServerACKMsg {
	nativeEntity := native.NewServerACKMsg()
	nativeEntity.SetLength(entity.Length)
	nativeEntity.SetAck_msg(entity.AckMsg)
	return nativeEntity
}

func deleteNativeServerACKMsg(entity native.ServerACKMsg) {
	native.DeleteServerACKMsgMembers(entity)
	native.DeleteServerACKMsg(entity)
}

func toRecordingInfo(nativeEntity native.RecordingInfo) RecordingInfo {
	return RecordingInfo{FilePath: nativeEntity.GetFile_path(), VideoCodecType: nativeEntity.GetVideo_codec_type(), Width: nativeEntity.GetWidth(), Height: nativeEntity.GetHeight()}
}

func toNativeRecordingInfo(entity *RecordingInfo) native.RecordingInfo {
	nativeEntity := native.NewRecordingInfo()
	nativeEntity.SetFile_path(entity.FilePath)
	nativeEntity.SetVideo_codec_type(entity.VideoCodecType)
	nativeEntity.SetWidth(entity.Width)
	nativeEntity.SetHeight(entity.Height)
	return nativeEntity
}

func deleteNativeRecordingInfo(entity native.RecordingInfo) {
	native.DeleteRecordingInfo(entity)
}

func toNativeRoomEventInfo(entity *RoomEventInfo) native.RoomEventInfo {
	nativeEntity := native.NewRoomEventInfo()
	nativeEntity.SetForbidden_time(entity.ForbiddenTime)
	return nativeEntity
}
func toRoomEventInfo(nativeEntity native.RoomEventInfo) RoomEventInfo {
	return RoomEventInfo{ForbiddenTime: nativeEntity.GetForbidden_time()}
}

func deleteNativeRoomEventInfo(entity native.RoomEventInfo) {
	native.DeleteRoomEventInfo(entity)
}

func toNativeLocalAudioStats(entity *LocalAudioStats) native.LocalAudioStats {
	nativeEntity := native.NewLocalAudioStats()
	nativeEntity.SetAudio_loss_rate(entity.AudioLossRate)
	nativeEntity.SetSend_kbitrate(entity.SendKbitrate)
	nativeEntity.SetRecord_sample_rate(entity.RecordSampleRate)
	nativeEntity.SetStats_interval(entity.StatsInterval)
	nativeEntity.SetRtt(entity.RTT)
	nativeEntity.SetNum_channels(entity.NumChannels)
	nativeEntity.SetSent_sample_rate(entity.SentSampleRate)
	nativeEntity.SetJitter(entity.Jitter)
	nativeEntity.SetAudio_device_loop_delay(entity.AudioDeviceLoopDelay)
	return nativeEntity
}
func deleteNativeLocalAudioStats(entity native.LocalAudioStats) {
	native.DeleteLocalAudioStats(entity)
}

func toLocalAudioStats(nativeEntity native.LocalAudioStats) LocalAudioStats {
	return LocalAudioStats{
		AudioLossRate:        nativeEntity.GetAudio_loss_rate(),
		SendKbitrate:         nativeEntity.GetSend_kbitrate(),
		RecordSampleRate:     nativeEntity.GetRecord_sample_rate(),
		StatsInterval:        nativeEntity.GetStats_interval(),
		RTT:                  nativeEntity.GetRtt(),
		NumChannels:          nativeEntity.GetNum_channels(),
		SentSampleRate:       nativeEntity.GetSent_sample_rate(),
		Jitter:               nativeEntity.GetJitter(),
		AudioDeviceLoopDelay: nativeEntity.GetAudio_device_loop_delay(),
	}
}

func toNativeLocalVideoStats(entity *LocalVideoStats) native.LocalVideoStats {
	nativeEntity := native.NewLocalVideoStats()
	nativeEntity.SetSent_kbitrate(entity.SentKbitrate)
	nativeEntity.SetInput_frame_rate(entity.InputFrameRate)
	nativeEntity.SetSent_frame_rate(entity.SentFrameRate)
	nativeEntity.SetEncoder_output_frame_rate(entity.EncoderOutputFrameRate)
	nativeEntity.SetRenderer_output_frame_rate(entity.RendererOutputFrameRate)
	nativeEntity.SetStats_interval(entity.StatsInterval)
	nativeEntity.SetVideo_loss_rate(entity.VideoLossRate)
	nativeEntity.SetRtt(entity.RTT)
	nativeEntity.SetEncoded_bitrate(entity.EncodedBitrate)
	nativeEntity.SetEncoded_frame_width(entity.EncodedFrameWidth)
	nativeEntity.SetEncoded_frame_height(entity.EncodedFrameHeight)
	nativeEntity.SetEncoded_frame_count(entity.EncodedFrameCount)
	nativeEntity.SetCodec_type(entity.CodecType)
	nativeEntity.SetIs_screen(entity.IsScreen)
	nativeEntity.SetJitter(entity.Jitter)
	nativeEntity.SetVideo_denoise_mode(entity.VideoDenoiseMode)
	nativeEntity.SetCodec_elapse_per_frame(entity.CodecElapsePerFrame)
	return nativeEntity

}
func deleteNativeLocalVideoStats(entity native.LocalVideoStats) {
	native.DeleteLocalVideoStats(entity)
}
func toLocalVideoStats(nativeEntity native.LocalVideoStats) LocalVideoStats {
	return LocalVideoStats{
		SentKbitrate:            nativeEntity.GetSent_kbitrate(),
		InputFrameRate:          nativeEntity.GetInput_frame_rate(),
		SentFrameRate:           nativeEntity.GetSent_frame_rate(),
		EncoderOutputFrameRate:  nativeEntity.GetEncoder_output_frame_rate(),
		RendererOutputFrameRate: nativeEntity.GetRenderer_output_frame_rate(),
		StatsInterval:           nativeEntity.GetStats_interval(),
		VideoLossRate:           nativeEntity.GetVideo_loss_rate(),
		RTT:                     nativeEntity.GetRtt(),
		EncodedBitrate:          nativeEntity.GetEncoded_bitrate(),
		EncodedFrameWidth:       nativeEntity.GetEncoded_frame_width(),
		EncodedFrameHeight:      nativeEntity.GetEncoded_frame_height(),
		EncodedFrameCount:       nativeEntity.GetEncoded_frame_count(),
		CodecType:               nativeEntity.GetCodec_type(),
		IsScreen:                nativeEntity.GetIs_screen(),
		Jitter:                  nativeEntity.GetJitter(),
		VideoDenoiseMode:        nativeEntity.GetVideo_denoise_mode(),
		CodecElapsePerFrame:     nativeEntity.GetCodec_elapse_per_frame(),
	}
}

func toNativeLocalStreamStats(entity *LocalStreamStats) native.LocalStreamStats {
	nativeEntity := native.NewLocalStreamStats()
	nativeEntity.SetAudio_stats(toNativeLocalAudioStats(&entity.AudioStats))
	nativeEntity.SetVideo_stats(toNativeLocalVideoStats(&entity.VideoStats))
	nativeEntity.SetLocal_tx_quality(entity.LocalTXQuality)
	nativeEntity.SetLocal_rx_quality(entity.LocalRXQuality)
	nativeEntity.SetIs_screen(entity.IsScreen)
	return nativeEntity
}
func deleteNativeLocalStreamStats(entity native.LocalStreamStats) {
	deleteNativeLocalAudioStats(entity.GetAudio_stats())
	deleteNativeLocalVideoStats(entity.GetVideo_stats())
	native.DeleteLocalStreamStats(entity)
}
func toLocalStreamStats(nativeEntity native.LocalStreamStats) LocalStreamStats {
	return LocalStreamStats{
		AudioStats:     toLocalAudioStats(nativeEntity.GetAudio_stats()),
		VideoStats:     toLocalVideoStats(nativeEntity.GetVideo_stats()),
		LocalTXQuality: nativeEntity.GetLocal_tx_quality(),
		LocalRXQuality: nativeEntity.GetLocal_rx_quality(),
		IsScreen:       nativeEntity.GetIs_screen(),
	}
}

func toNativeRemoteAudioStats(entity *RemoteAudioStats) native.RemoteAudioStats {
	nativeEntity := native.NewRemoteAudioStats()
	nativeEntity.SetAudio_loss_rate(entity.AudioLossRate)
	nativeEntity.SetReceived_kbitrate(entity.ReceivedKbitrate)
	nativeEntity.SetStall_count(entity.StallCount)
	nativeEntity.SetStall_duration(entity.StallDuration)
	nativeEntity.SetE2e_delay(entity.E2eDelay)
	nativeEntity.SetPlayout_sample_rate(entity.PlayoutSampleRate)
	nativeEntity.SetStats_interval(entity.StatsInterval)
	nativeEntity.SetRtt(entity.RTT)
	nativeEntity.SetTotal_rtt(entity.TotalRtt)
	nativeEntity.SetQuality(entity.Quality)
	nativeEntity.SetJitter_buffer_delay(entity.JitterBufferDelay)
	nativeEntity.SetNum_channels(entity.NumChannels)
	nativeEntity.SetReceived_sample_rate(entity.ReceivedSampleRate)
	nativeEntity.SetFrozen_rate(entity.FrozenRate)
	nativeEntity.SetConcealed_samples(entity.ConcealedSamples)
	nativeEntity.SetConcealment_event(entity.ConcealmentEvent)
	nativeEntity.SetDec_sample_rate(entity.DecSampleRate)
	nativeEntity.SetDec_duration(entity.DecDuration)
	nativeEntity.SetJitter(entity.Jitter)
	return nativeEntity
}
func deleteNativeRemoteAudioStats(entity native.RemoteAudioStats) {
	native.DeleteRemoteAudioStats(entity)
}
func toRemoteAudioStats(nativeEntity native.RemoteAudioStats) RemoteAudioStats {
	return RemoteAudioStats{
		AudioLossRate:      nativeEntity.GetAudio_loss_rate(),
		ReceivedKbitrate:   nativeEntity.GetReceived_kbitrate(),
		StallCount:         nativeEntity.GetStall_count(),
		StallDuration:      nativeEntity.GetStall_duration(),
		E2eDelay:           nativeEntity.GetE2e_delay(),
		PlayoutSampleRate:  nativeEntity.GetPlayout_sample_rate(),
		StatsInterval:      nativeEntity.GetStats_interval(),
		RTT:                nativeEntity.GetRtt(),
		TotalRtt:           nativeEntity.GetTotal_rtt(),
		Quality:            nativeEntity.GetQuality(),
		JitterBufferDelay:  nativeEntity.GetJitter_buffer_delay(),
		NumChannels:        nativeEntity.GetNum_channels(),
		ReceivedSampleRate: nativeEntity.GetReceived_sample_rate(),
		FrozenRate:         nativeEntity.GetFrozen_rate(),
		ConcealedSamples:   nativeEntity.GetConcealed_samples(),
		ConcealmentEvent:   nativeEntity.GetConcealment_event(),
		DecSampleRate:      nativeEntity.GetDec_sample_rate(),
		DecDuration:        nativeEntity.GetDec_duration(),
		Jitter:             nativeEntity.GetJitter(),
	}
}

func toNativeRemoteVideoStats(entity *RemoteVideoStats) native.RemoteVideoStats {
	nativeEntity := native.NewRemoteVideoStats()
	nativeEntity.SetWidth(entity.Width)
	nativeEntity.SetHeight(entity.Height)
	nativeEntity.SetVideo_loss_rate(entity.VideoLossRate)
	nativeEntity.SetReceived_kbitrate(entity.ReceivedKbitrate)
	nativeEntity.SetDecoder_output_frame_rate(entity.DecoderOutputFrameRate)
	nativeEntity.SetRenderer_output_frame_rate(entity.RendererOutputFrameRate)
	nativeEntity.SetStall_count(entity.StallCount)
	nativeEntity.SetStall_duration(entity.StallDuration)
	nativeEntity.SetE2e_delay(entity.E2eDelay)
	nativeEntity.SetIs_screen(entity.IsScreen)
	nativeEntity.SetStats_interval(entity.StatsInterval)
	nativeEntity.SetRtt(entity.RTT)
	nativeEntity.SetFrozen_rate(entity.FrozenRate)
	nativeEntity.SetCodec_type(entity.CodecType)
	nativeEntity.SetVideo_index(entity.VideoIndex)
	nativeEntity.SetJitter(entity.Jitter)
	nativeEntity.SetSuper_resolution_mode(entity.SuperResolutionMode)
	nativeEntity.SetCodec_elapse_per_frame(entity.CodecElapsePerFrame)
	return nativeEntity
}
func deleteNativeRemoteVideoStats(entity native.RemoteVideoStats) {
	native.DeleteRemoteVideoStats(entity)
}
func toRemoteVideoStats(nativeEntity native.RemoteVideoStats) RemoteVideoStats {
	return RemoteVideoStats{
		Width:                   nativeEntity.GetWidth(),
		Height:                  nativeEntity.GetHeight(),
		VideoLossRate:           nativeEntity.GetVideo_loss_rate(),
		ReceivedKbitrate:        nativeEntity.GetReceived_kbitrate(),
		DecoderOutputFrameRate:  nativeEntity.GetDecoder_output_frame_rate(),
		RendererOutputFrameRate: nativeEntity.GetRenderer_output_frame_rate(),
		StallCount:              nativeEntity.GetStall_count(),
		StallDuration:           nativeEntity.GetStall_duration(),
		E2eDelay:                nativeEntity.GetE2e_delay(),
		IsScreen:                nativeEntity.GetIs_screen(),
		StatsInterval:           nativeEntity.GetStats_interval(),
		RTT:                     nativeEntity.GetRtt(),
		FrozenRate:              nativeEntity.GetFrozen_rate(),
		CodecType:               nativeEntity.GetCodec_type(),
		VideoIndex:              nativeEntity.GetVideo_index(),
		Jitter:                  nativeEntity.GetJitter(),
		SuperResolutionMode:     nativeEntity.GetSuper_resolution_mode(),
		CodecElapsePerFrame:     nativeEntity.GetCodec_elapse_per_frame(),
	}
}

func toNativeRemoteStreamStats(entity *RemoteStreamStats) native.RemoteStreamStats {
	nativeEntity := native.NewRemoteStreamStats()
	nativeEntity.SetUid(entity.UID)
	nativeEntity.SetAudio_stats(toNativeRemoteAudioStats(&entity.AudioStats))
	nativeEntity.SetVideo_stats(toNativeRemoteVideoStats(&entity.VideoStats))
	nativeEntity.SetRemote_tx_quality(entity.RemoteTXQuality)
	nativeEntity.SetRemote_rx_quality(entity.RemoteRXQuality)
	nativeEntity.SetIs_screen(entity.IsScreen)
	return nativeEntity

}
func deleteNativeRemoteStreamStats(entity native.RemoteStreamStats) {
	deleteNativeRemoteAudioStats(entity.GetAudio_stats())
	deleteNativeRemoteVideoStats(entity.GetVideo_stats())
	native.DeleteRemoteStreamStats(entity)
}
func toRemoteStreamStats(nativeEntity native.RemoteStreamStats) RemoteStreamStats {
	return RemoteStreamStats{
		UID:             nativeEntity.GetUid(),
		AudioStats:      toRemoteAudioStats(nativeEntity.GetAudio_stats()),
		VideoStats:      toRemoteVideoStats(nativeEntity.GetVideo_stats()),
		RemoteTXQuality: nativeEntity.GetRemote_tx_quality(),
		RemoteRXQuality: nativeEntity.GetRemote_rx_quality(),
		IsScreen:        nativeEntity.GetIs_screen(),
	}
}

func toNativeVideoSolutionDescription(entity *VideoSolutionDescription) native.VideoSolutionDescription {
	nativeEntity := native.NewVideoSolutionDescription()
	nativeEntity.SetWidth(entity.Width)
	nativeEntity.SetHeight(entity.Height)
	nativeEntity.SetFps(entity.Fps)
	nativeEntity.SetMax_send_kbps(entity.MaxSendKbps)
	nativeEntity.SetMin_send_kbps(entity.MinSendKbps)
	nativeEntity.SetScale_mode(entity.ScaleMode)
	nativeEntity.SetCodec_name(entity.CodecName)
	nativeEntity.SetCodec_mode(entity.CodecMode)
	nativeEntity.SetEncode_preference(entity.EncodePreference)
	return nativeEntity
}
func deleteNativeVideoSolutionDescription(entity native.VideoSolutionDescription) {
	native.DeleteVideoSolutionDescription(entity)
}

func toVideoSolutionDescription(nativeEntity native.VideoSolutionDescription) VideoSolutionDescription {
	return VideoSolutionDescription{
		Width:            nativeEntity.GetWidth(),
		Height:           nativeEntity.GetHeight(),
		Fps:              nativeEntity.GetFps(),
		MaxSendKbps:      nativeEntity.GetMax_send_kbps(),
		MinSendKbps:      nativeEntity.GetMin_send_kbps(),
		ScaleMode:        nativeEntity.GetScale_mode(),
		CodecName:        nativeEntity.GetCodec_name(),
		CodecMode:        nativeEntity.GetCodec_mode(),
		EncodePreference: nativeEntity.GetEncode_preference(),
	}
}

func toMediaStreamInfo(nativeEntity native.MediaStreamInfo) MediaStreamInfo {
	info := MediaStreamInfo{
		UserID:       nativeEntity.GetUser_id(),
		StreamIndex:  nativeEntity.GetStream_index(),
		IsScreen:     nativeEntity.GetIs_screen(),
		HasVideo:     nativeEntity.GetHas_video(),
		HasAudio:     nativeEntity.GetHas_audio(),
		ProfileCount: nativeEntity.GetProfile_count(),
		MaxProfile:   toVideoSolutionDescription(nativeEntity.GetMax_profile()),
	}
	info.Profiles = make([]VideoSolutionDescription, nativeEntity.GetProfile_count())
	head := nativeEntity.GetProfiles()
	for i := 0; i < nativeEntity.GetProfile_count(); i++ {
		info.Profiles[i] = toVideoSolutionDescription(native.GetElementOfVideoSolutionDescription(head, i))
	}
	return info
}

func toNativeForwardStreamStateInfo(entity *ForwardStreamStateInfo) native.ForwardStreamStateInfo {
	nativeEntity := native.NewForwardStreamStateInfo()
	nativeEntity.SetRoom_id(entity.RoomID)
	nativeEntity.SetState(entity.State)
	nativeEntity.SetError(entity.Error)
	return nativeEntity
}
func deleteNativeForwardStreamStateInfo(entity native.ForwardStreamStateInfo) {
	native.DeleteForwardStreamStateInfoMembers(entity)
	native.DeleteForwardStreamStateInfo(entity)
}
func toForwardStreamStateInfo(nativeEntity native.ForwardStreamStateInfo) ForwardStreamStateInfo {
	return ForwardStreamStateInfo{RoomID: nativeEntity.GetRoom_id(), State: nativeEntity.GetState(), Error: nativeEntity.GetError()}
}
func toNativeForwardStreamEventInfo(entity *ForwardStreamEventInfo) native.ForwardStreamEventInfo {
	nativeEntity := native.NewForwardStreamEventInfo()
	nativeEntity.SetRoom_id(entity.RoomID)
	nativeEntity.SetEvent(entity.Event)
	return nativeEntity
}
func deleteNativeForwardStreamEventInfo(entity native.ForwardStreamEventInfo) {
	native.DeleteForwardStreamEventInfoMembers(entity)
	native.DeleteForwardStreamEventInfo(entity)
}
func toForwardStreamEventInfo(nativeEntity native.ForwardStreamEventInfo) ForwardStreamEventInfo {
	return ForwardStreamEventInfo{RoomID: nativeEntity.GetRoom_id(), Event: nativeEntity.GetEvent()}
}

func toNativeNetworkQualityStats(entity *NetworkQualityStats) native.NetworkQualityStats {
	nativeEntity := native.NewNetworkQualityStats()
	nativeEntity.SetUid(entity.UID)
	nativeEntity.SetFraction_lost(entity.FractionLost)
	nativeEntity.SetRtt(entity.RTT)
	nativeEntity.SetTotal_bandwidth(entity.TotalBandwidth)
	nativeEntity.SetTx_quality(entity.TXQuality)
	nativeEntity.SetRx_quality(entity.RXQuality)
	return nativeEntity
}
func deleteNativeNetworkQualityStats(entity native.NetworkQualityStats) {
	native.DeleteNetworkQualityStats(entity)
}
func toNetworkQualityStats(nativeEntity native.NetworkQualityStats) NetworkQualityStats {
	return NetworkQualityStats{
		UID:            nativeEntity.GetUid(),
		FractionLost:   nativeEntity.GetFraction_lost(),
		RTT:            nativeEntity.GetRtt(),
		TotalBandwidth: nativeEntity.GetTotal_bandwidth(),
		TXQuality:      nativeEntity.GetTx_quality(),
		RXQuality:      nativeEntity.GetRx_quality(),
	}
}

func toNativeSubtitleMessage(entity *SubtitleMessage) native.SubtitleMessage {
	nativeEntity := native.NewSubtitleMessage()
	nativeEntity.SetUser_id(entity.UserID)
	nativeEntity.SetText(entity.Text)
	nativeEntity.SetLanguage(entity.Language)
	nativeEntity.SetMode(entity.Mode)
	nativeEntity.SetSequence(entity.Sequence)
	nativeEntity.SetDefinite(entity.Definite)
	return nativeEntity

}
func deleteNativeSubtitleMessage(entity native.SubtitleMessage) {
	native.DeleteSubtitleMessageMembers(entity)
	native.DeleteSubtitleMessage(entity)
}
func toSubtitleMessage(nativeEntity native.SubtitleMessage) SubtitleMessage {
	return SubtitleMessage{
		UserID:   nativeEntity.GetUser_id(),
		Text:     nativeEntity.GetText(),
		Language: nativeEntity.GetLanguage(),
		Mode:     nativeEntity.GetMode(),
		Sequence: nativeEntity.GetSequence(),
		Definite: nativeEntity.GetDefinite(),
	}
}

func toNativeRTCRoomStats(entity *RTCRoomStats) native.RtcRoomStats {
	nativeEntity := native.NewRtcRoomStats()
	nativeEntity.SetTx_lostrate(entity.TXLostrate)
	nativeEntity.SetRx_lostrate(entity.RXLostrate)
	nativeEntity.SetRtt(entity.RTT)
	nativeEntity.SetDuration(entity.Duration)
	nativeEntity.SetTx_bytes(entity.TXBytes)
	nativeEntity.SetRx_bytes(entity.RXBytes)
	nativeEntity.SetTx_kbitrate(entity.TXKbitrate)
	nativeEntity.SetRx_kbitrate(entity.RXKbitrate)
	nativeEntity.SetRx_audio_kbitrate(entity.RXAudioKbitrate)
	nativeEntity.SetTx_audio_kbitrate(entity.TXAudioKbitrate)
	nativeEntity.SetRx_video_kbitrate(entity.RXVideoKbitrate)
	nativeEntity.SetTx_video_kbitrate(entity.TXVideoKbitrate)
	nativeEntity.SetRx_screen_kbitrate(entity.RXScreenKbitrate)
	nativeEntity.SetTx_screen_kbitrate(entity.TXScreenKbitrate)
	nativeEntity.SetUser_count(entity.UserCount)
	nativeEntity.SetCpu_app_usage(entity.CpuAppUsage)
	nativeEntity.SetCpu_total_usage(entity.CpuTotalUsage)
	nativeEntity.SetTx_jitter(entity.TXJitter)
	nativeEntity.SetRx_jitter(entity.RXJitter)
	nativeEntity.SetTx_cellular_kbitrate(entity.TXCellularKbitrate)
	nativeEntity.SetRx_cellular_kbitrate(entity.RXCellularKbitrate)
	return nativeEntity
}
func deleteNativeRTCRoomStats(entity native.RtcRoomStats) {
	native.DeleteRtcRoomStats(entity)
}
func toRTCRoomStats(nativeEntity native.RtcRoomStats) RTCRoomStats {
	return RTCRoomStats{
		TXLostrate:         nativeEntity.GetTx_lostrate(),
		RXLostrate:         nativeEntity.GetRx_lostrate(),
		RTT:                nativeEntity.GetRtt(),
		Duration:           nativeEntity.GetDuration(),
		TXBytes:            nativeEntity.GetTx_bytes(),
		RXBytes:            nativeEntity.GetRx_bytes(),
		TXKbitrate:         nativeEntity.GetTx_kbitrate(),
		RXKbitrate:         nativeEntity.GetRx_kbitrate(),
		RXAudioKbitrate:    nativeEntity.GetRx_audio_kbitrate(),
		TXAudioKbitrate:    nativeEntity.GetTx_audio_kbitrate(),
		RXVideoKbitrate:    nativeEntity.GetRx_video_kbitrate(),
		TXVideoKbitrate:    nativeEntity.GetTx_video_kbitrate(),
		RXScreenKbitrate:   nativeEntity.GetRx_screen_kbitrate(),
		TXScreenKbitrate:   nativeEntity.GetTx_screen_kbitrate(),
		UserCount:          nativeEntity.GetUser_count(),
		CpuAppUsage:        nativeEntity.GetCpu_app_usage(),
		CpuTotalUsage:      nativeEntity.GetCpu_total_usage(),
		TXJitter:           nativeEntity.GetTx_jitter(),
		RXJitter:           nativeEntity.GetRx_jitter(),
		TXCellularKbitrate: nativeEntity.GetTx_cellular_kbitrate(),
		RXCellularKbitrate: nativeEntity.GetRx_cellular_kbitrate(),
	}
}

func toNativeProblemFeedbackInfo(entity *ProblemFeedbackInfo) native.ProblemFeedbackInfo {
	nativeEntity := native.NewProblemFeedbackInfo()
	nativeEntity.SetProblem_desc(entity.ProblemDesc)
	nativeEntity.SetRoom_info_count(entity.RoomInfoCount)
	nativeRoomInfos := native.MakeProblemFeedbackRoomInfoArray(entity.RoomInfoCount)
	for i, info := range entity.RoomInfos {
		native.SetProblemFeedbackRoomInfoArrayValueOfIndex(nativeRoomInfos, i, info.RoomID, info.UserID)
	}
	nativeEntity.SetRoom_info(nativeRoomInfos)
	return nativeEntity
}
func deleteNativeProblemFeedbackInfo(entity native.ProblemFeedbackInfo) {
	native.DeleteProblemFeedbackInfoMembers(entity)
	native.DeleteProblemFeedbackInfo(entity)
}

func toNativeLogConfig(entity *LogConfig) native.LogConfig {
	nativeEntity := native.NewLogConfig()
	nativeEntity.SetLog_path(entity.LogPath)
	nativeEntity.SetLog_level(int(entity.LogLevel))
	nativeEntity.SetLog_file_size(entity.LogFileSize)
	nativeEntity.SetLog_filename_prefix(entity.LogFilenamePrefix)
	return nativeEntity
}
func deleteNativeLogConfig(entity native.LogConfig) {
	native.DeleteLogConfigMembers(entity)
	native.DeleteLogConfig(entity)
}

func ToNativeAudioPropertiesConfig(entity *AudioPropertiesConfig) native.AudioPropertiesConfig {
	nativeEntity := native.NewAudioPropertiesConfig()
	nativeEntity.SetInterval(entity.Interval)
	nativeEntity.SetEnable_spectrum(entity.EnableSpectrum)
	nativeEntity.SetEnable_vad(entity.EnableVad)
	nativeEntity.SetLocal_main_report_mode(int(entity.LocalMainReportMode))
	nativeEntity.SetSmooth(entity.Smooth)
	nativeEntity.SetAudio_report_mode(int(entity.AudioReportMode))
	nativeEntity.SetEnable_voice_pitch(entity.EnableVoicePitch)
	return nativeEntity
}
func deleteNativeAudioPropertiesConfig(entity native.AudioPropertiesConfig) {
	native.DeleteAudioPropertiesConfig(entity)
}

func toNativeVideoEncoderConfig(entity VideoEncoderConfig) native.VideoEncoderConfig {
	nativeEntity := native.NewVideoEncoderConfig()
	nativeEntity.SetWidth(entity.Width)
	nativeEntity.SetHeight(entity.Height)
	nativeEntity.SetFrame_rate(entity.FrameRate)
	nativeEntity.SetMax_bitrate(entity.MaxBitRate)
	nativeEntity.SetMin_bitrate(entity.MinBitRate)
	nativeEntity.SetEncoder_preference(int(entity.EncoderPerference))
	return nativeEntity
}
func deleteNativeVideoEncoderConfig(entity native.VideoEncoderConfig) {
	native.DeleteVideoEncoderConfig(entity)
}

func toNativeSubtitleConfig(entity *SubtitleConfig) native.SubtitleConfig {
	nativeEntity := native.NewSubtitleConfig()
	nativeEntity.SetMode(entity.Mode)
	nativeEntity.SetTarget_language(entity.TargetLanguage)
	return nativeEntity
}
func deleteNativeSubtitleConfig(entity native.SubtitleConfig) {
	native.DeleteSubtitleConfigMembers(entity)
	native.DeleteSubtitleConfig(entity)
}

func toVideoRateInfo(nativeEntity native.VideoRateInfo) VideoRateInfo {
	return VideoRateInfo{
		Fps:            nativeEntity.GetFps(),
		BitrateKbps:    nativeEntity.GetBitrate_kbps(),
		MinBitrateKbps: nativeEntity.GetMin_bitrate_kbps(),
	}
}

func toNativeStreamSycnInfoConfig(entity *StreamSycnInfoConfig) native.StreamSycnInfoConfig {
	nativeEntity := native.NewStreamSycnInfoConfig()
	nativeEntity.SetStream_index(entity.StreamIndex)
	nativeEntity.SetRepeat_count(entity.RepeatCount)
	nativeEntity.SetStream_type(entity.StreamType)
	return nativeEntity
}
func deleteNativeStreamSycnInfoConfig(entity native.StreamSycnInfoConfig) {
	native.DeleteStreamSycnInfoConfig(entity)
}

func toStreamSycnInfoConfig(nativeEntity native.StreamSycnInfoConfig) StreamSycnInfoConfig {
	return StreamSycnInfoConfig{
		StreamIndex: nativeEntity.GetStream_index(),
		RepeatCount: nativeEntity.GetRepeat_count(),
		StreamType:  nativeEntity.GetStream_type(),
	}
}

func toNativeCloudProxyConfiguration(entity *CloudProxyConfiguration) native.CloudProxyConfiguration {
	nativeEntity := native.NewCloudProxyConfiguration()
	nativeCloudProxies := native.MakeCloudProxyInfoArray(entity.CloudProxyCount)
	for i, proxy := range entity.CloudProxies {
		native.SetCloudProxyInfoArrayValueOfIndex(nativeCloudProxies, i, proxy.CloudProxyIP, proxy.CloudProxyPort)
	}
	nativeEntity.SetCloud_proxies(nativeCloudProxies)
	nativeEntity.SetCloud_proxy_count(entity.CloudProxyCount)
	return nativeEntity
}
func deleteNativeCloudProxyConfiguration(entity native.CloudProxyConfiguration) {
	native.DeleteCloudProxyConfigurationMembers(entity)
	native.DeleteCloudProxyConfiguration(entity)
}
func toNativeLocalProxyConfigurationArray(entity []LocalProxyConfiguration) native.LocalProxyConfiguration {
	proxyArray := native.MakeLocalProxyConfigurationArray(len(entity))
	for i, proxy := range entity {
		native.SetLocalProxyConfigurationArrayValueOfIndex(proxyArray, i,
			proxy.LocalProxyType,
			proxy.LocalProxyIp,
			proxy.LocalProxyPort,
			proxy.LocalProxyUsername,
			proxy.LocalProxyPassword)
	}
	return proxyArray
}
func deleteNativeLocalProxyConfigurationArray(entity native.LocalProxyConfiguration, length int) {
	native.DeleteArrayOfLocalProxyConfiguration(entity, length)
}

func toNativeVideoCanvas(entity *VideoCanvas) native.VideoCanvas {
	nativeEntity := native.NewVideoCanvas()
	nativeEntity.SetView(entity.View)
	nativeEntity.SetRender_mode(entity.RenderMode)
	nativeEntity.SetBackground_color(uint(entity.BackgroundColor))
	nativeEntity.SetRender_target_type(entity.RenderTargetType)
	nativeEntity.SetRender_rotation(entity.RenderRotation)
	return nativeEntity
}
func deleteNativeVideoCanvas(entity native.VideoCanvas) {
	native.DeleteVideoCanvas(entity)
}

func toFovVideoTileInfo(entity native.FovVideoTileInfo) FovVideoTileInfo {
	return FovVideoTileInfo{
		HdWidth:    entity.GetHd_width(),
		HdHeight:   entity.GetHd_height(),
		LdWidth:    entity.GetLd_width(),
		LdHeight:   entity.GetLd_height(),
		TileWidth:  entity.GetTile_width(),
		TileHeight: entity.GetTile_height(),
		HdRow:      entity.GetHd_row(),
		HdColumn:   entity.GetHd_column(),
		LdRow:      entity.GetLd_row(),
		LdColumn:   entity.GetLd_column(),
		DestRow:    entity.GetDest_row(),
		DestColumn: entity.GetDest_column(),
		TileData:   native.GetDataChunkFromFovVideoTileInfo(entity),
	}
}

func toNativeVideoFrameData(entity *VideoFrameData) native.VideoFrameData {

	nativeEntity := native.NewVideoFrameData()
	nativeEntity.SetBuffer_type(entity.BufferType)
	nativeEntity.SetPixel_format(entity.PixelFormat)
	nativeEntity.SetContent_type(entity.ContentType)
	nativeEntity.SetNumber_of_planes(entity.NumberOfPlanes)
	for i := 0; i < 4; i++ {
		if entity.PlaneData[i] == nil {
			native.SetPlaneDataChunkToVideoFrameData(
				nativeEntity, make([]byte, 0), i)
		} else {
			native.SetPlaneDataChunkToVideoFrameData(
				nativeEntity, entity.PlaneData[i], i,
			)
		}
		native.SetPlaneDataStrideToVideoFrameData(nativeEntity, entity.PlaneStride[i], i)
	}
	native.SetSeiDataChunkToVideoFrameData(
		nativeEntity,
		entity.SeiData,
	)
	native.SetRoiDataChunkToVideoFrameData(
		nativeEntity,
		entity.RoiData,
	)
	nativeEntity.SetWidth(entity.Width)
	nativeEntity.SetHeight(entity.Height)
	nativeEntity.SetRotation(entity.Rotation)
	nativeEntity.SetTimestamp_us(entity.TimestampUs)
	nativeEntity.SetHw_buffer(entity.HwBuffer)
	nativeEntity.SetHw_context(entity.HwContext)

	for i := 0; i < 16; i++ {
		native.SetTextureMatrixDataToVideoFrameData(nativeEntity, entity.TextureMatrix[i], i)
	}
	nativeEntity.SetTexture_id(entity.TextureId)

	return nativeEntity
}

func deleteNativeVideoFrameData(entity native.VideoFrameData) {
	native.FreeVideoFrameData(entity)
	native.DeleteVideoFrameData(entity)
}

func toIAudioFrame(entity native.IAudioFrame) IAudioFrame {
	return &audioFrameProxyImp{nativeAudioFrame: entity}
}

func toNativeVideoPreprocessorConfig(entity VideoPreprocessorConfig) native.VideoPreprocessorConfig {
	nativeEntity := native.NewVideoPreprocessorConfig()
	nativeEntity.SetRequired_pixel_format(entity.RequiredPixelFormat)
	return nativeEntity
}
func deleteNativeVideoPreprocessorConfig(entity native.VideoPreprocessorConfig) {
	native.DeleteVideoPreprocessorConfig(entity)
}

func toVideoPreprocessorConfig(entity native.VideoPreprocessorConfig) VideoPreprocessorConfig {
	return VideoPreprocessorConfig{
		RequiredPixelFormat: entity.GetRequired_pixel_format(),
	}
}

func toVideoFrame(entity native.IVideoFrame) IVideoFrame {
	return &videoFrameImp{native: entity}
}

func toRecordingConfig(entity native.RecordingConfig) RecordingConfig {
	return RecordingConfig{
		DirPath:  entity.GetDir_path(),
		FileType: RecordingFileType(entity.GetFile_type()),
	}
}

func toNativeRecordingConfig(entity RecordingConfig) native.RecordingConfig {
	nativeEntity := native.NewRecordingConfig()
	nativeEntity.SetDir_path(entity.DirPath)
	nativeEntity.SetFile_type(int(entity.FileType))
	return nativeEntity
}

func deleteNativeRecordingConfig(entity native.RecordingConfig) {
	native.DeleteRecordingConfigMembers(entity)
	native.DeleteRecordingConfig(entity)
}

func toAudioRecordingConfig(entity native.AudioRecordingConfig) AudioRecordingConfig {
	return AudioRecordingConfig{
		AbsoluteFileName: entity.GetAbsolute_file_name(),
		FrameSource:      AudioFrameSource(entity.GetFrame_source()),
		SampleRate:       AudioSampleRate(entity.GetSample_rate()),
		Channel:          AudioChannel(entity.GetChannel()),
		Quality:          AudioQuality(entity.GetQuality()),
	}
}

func toNativeAudioRecordingConfig(entity AudioRecordingConfig) native.AudioRecordingConfig {
	nativeEntity := native.NewAudioRecordingConfig()
	nativeEntity.SetAbsolute_file_name(entity.AbsoluteFileName)
	nativeEntity.SetFrame_source(int(entity.FrameSource))
	nativeEntity.SetSample_rate(int(entity.SampleRate))
	nativeEntity.SetChannel(int(entity.Channel))
	nativeEntity.SetQuality(int(entity.Quality))
	return nativeEntity
}

func deleteNativeAudioRecordingConfig(entity native.AudioRecordingConfig) {
	native.DeleteAudioRecordingConfigMembers(entity)
	native.DeleteAudioRecordingConfig(entity)
}

func toNativeVideoEncoderConfigArray(entity []VideoEncoderConfig) native.VideoEncoderConfig {
	nativeConfigArray := native.MakeVideoEncoderConfigArray(len(entity))
	for i, config := range entity {
		n := toNativeVideoEncoderConfig(config)
		defer native.DeleteVideoEncoderConfig(n)
		native.SetVideoEncoderConfigArrayValueOfIndex(nativeConfigArray, i, n)
	}
	return nativeConfigArray
}

func deleteNativeVideoEncoderConfigArray(entity native.VideoEncoderConfig) {
	native.DeleteVideoEncoderConfigArray(entity)
}

func toNativeRemoteVideoSinkConfig(entity RemoteVideoSinkConfig) native.RemoteVideoSinkConfig {
	nativeEntity := native.NewRemoteVideoSinkConfig()
	nativeEntity.SetPosition(entity.Position)
	nativeEntity.SetPixel_format(entity.PixelFormat)
	nativeEntity.SetApply_rotation(entity.ApplyRotation)
	nativeEntity.SetMirror_type(entity.MirrorType)
	return nativeEntity
}

func deleteNativeRemoteVideoSinkConfig(entity native.RemoteVideoSinkConfig) {
	native.DeleteRemoteVideoSinkConfig(entity)
}

func toNaitveVideoDeviceInfo(entity VideoDeviceInfo) native.VideoDeviceInfo {
	nativeEntity := native.NewVideoDeviceInfo()
	nativeEntity.SetDevice_id(entity.DeviceID)
	nativeEntity.SetDevice_name(entity.DeviceName)
	nativeEntity.SetDevice_vid(entity.DeviceVid)
	nativeEntity.SetDevice_pid(entity.DevicePid)
	nativeEntity.SetDevice_facing(int(entity.DeviceFacing))
	nativeEntity.SetTransport_type(int(entity.TransportType))
	return nativeEntity
}

func deleteNativeVideoDeviceInfo(entity native.VideoDeviceInfo) {
	// native VideoDeviceInfo use fixed array, so no need to delete the members
	native.DeleteVideoDeviceInfo(entity)
}

func toNativeEncodedVideoFrameBuilder(entity *EncodedVideoFrameBuilder) native.EncodedVideoFrameBuilder {
	nativeEntity := native.NewEncodedVideoFrameBuilder()
	nativeEntity.SetCodec_type(entity.CodecType)
	nativeEntity.SetPicture_type(entity.PictureType)
	nativeEntity.SetRotation(entity.Rotation)
	native.SetDataChunkToEncodedVideoFrameBuilder(nativeEntity, entity.Data)
	native.SetMemoryDeleterOfEncodedVideoFrameBuilder(nativeEntity)
	nativeEntity.SetSize(entity.Size)
	nativeEntity.SetWidth(entity.Width)
	nativeEntity.SetHeight(entity.Height)
	nativeEntity.SetTimestamp_us(entity.TimestampUs)
	nativeEntity.SetTimestamp_dts_us(entity.TimestampDtsUs)
	return nativeEntity
}

func deleteNativeEncodedVideoFrameBuilder(entity native.EncodedVideoFrameBuilder) {
	native.DeleteEncodedVideoFrameBuilder(entity)
}
