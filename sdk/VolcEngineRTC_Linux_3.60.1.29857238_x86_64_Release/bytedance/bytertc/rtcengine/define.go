package rtcengine

import (
	"bytedance/bytertc/native"
)

type AudioFormat struct {
	SampleRate     AudioSampleRate
	Channel        AudioChannel
	SamplesPerCall int
}

type AudioPropertiesInfo struct {
	LinearVolume    int
	NonlinearVolume int
	Spectrum        []float32
	Vad             int
	VoicePitch      float64
}

type LocalAudioPropertiesInfo struct {
	StreamIndex         StreamIndex
	AudioPropertiesInfo AudioPropertiesInfo
}

type RemoteAudioPropertiesInfo struct {
	StreamKey           RemoteStreamKey
	AudioPropertiesInfo AudioPropertiesInfo
}

type VideoFrameInfo struct {
	Width    int
	Height   int
	Rotation VideoRotation
}

type RemoteStreamSwitch struct {
	UID              string
	IsScreen         bool
	BeforeVideoIndex int
	AfterVideoIndex  int
	BeforeEnable     bool
	AfterEnable      bool
	Reason           FallbackOrRecoverReason
}

type EngineConfig struct {
	AppID      string
	Parameters map[string]interface{}
}

type UserInfo struct {
	UID       string
	ExtraInfo string
}

type DeadLockMsg struct {
	BlockSessionID string
	BlockingPaths  string
	IsCritical     bool
}

type RTCRoomConfig struct {
	RoomProfileType      RoomProfileType
	IsPublishAudio       bool
	IsPublishVideo       bool
	IsAutoSubscribeAudio bool
	IsAutoSubscribeVideo bool
}

type SysStats struct {
	CpuCores         uint
	CpuAppUsage      float64
	CpuTotalUsage    float64
	MemoryUsage      float64
	FullMemory       float64
	TotalMemoryUsage uint64
	FreeMemory       uint64
	MemoryRatio      float64
	TotalMemoryRatio float64
}

type RecordingProgress struct {
	Duration uint64
	FileSize uint64
}

type RTCUser struct {
	UserID   string
	MetaData string
}

type RemoteStreamKey struct {
	RoomID      string
	UserID      string
	StreamIndex StreamIndex
}

type SourceWantedData struct {
	Width     int
	Height    int
	FrameRate int
}

type ServerACKMsg struct {
	Length int
	AckMsg string
}

type RecordingInfo struct {
	FilePath       string
	VideoCodecType VideoCodecType
	Width          int
	Height         int
}

type RoomEventInfo struct {
	ForbiddenTime int64
}

type LocalAudioStats struct {
	AudioLossRate        float32
	SendKbitrate         int
	RecordSampleRate     int
	StatsInterval        int
	RTT                  int
	NumChannels          int
	SentSampleRate       int
	Jitter               int
	AudioDeviceLoopDelay int
}

type LocalVideoStats struct {
	SentKbitrate            int
	InputFrameRate          int
	SentFrameRate           int
	EncoderOutputFrameRate  int
	RendererOutputFrameRate int
	StatsInterval           int
	VideoLossRate           float32
	RTT                     int
	EncodedBitrate          int
	EncodedFrameWidth       int
	EncodedFrameHeight      int
	EncodedFrameCount       int
	CodecType               VideoCodecType
	IsScreen                bool
	Jitter                  int
	VideoDenoiseMode        VideoDenoiseMode
	CodecElapsePerFrame     int
}

type LocalStreamStats struct {
	AudioStats     LocalAudioStats
	VideoStats     LocalVideoStats
	LocalTXQuality NetworkQuality
	LocalRXQuality NetworkQuality
	IsScreen       bool
}

type RemoteAudioStats struct {
	AudioLossRate      float32
	ReceivedKbitrate   int
	StallCount         int
	StallDuration      int
	E2eDelay           int64
	PlayoutSampleRate  int
	StatsInterval      int
	RTT                int
	TotalRtt           int
	Quality            int
	JitterBufferDelay  int
	NumChannels        int
	ReceivedSampleRate int
	FrozenRate         int
	ConcealedSamples   int
	ConcealmentEvent   int
	DecSampleRate      int
	DecDuration        int
	Jitter             int
}

type RemoteVideoStats struct {
	Width                   int
	Height                  int
	VideoLossRate           float32
	ReceivedKbitrate        int
	DecoderOutputFrameRate  int
	RendererOutputFrameRate int
	StallCount              int
	StallDuration           int
	E2eDelay                int64
	IsScreen                bool
	StatsInterval           int
	RTT                     int
	FrozenRate              int
	CodecType               VideoCodecType
	VideoIndex              int
	Jitter                  int
	SuperResolutionMode     VideoSuperResolutionMode
	CodecElapsePerFrame     int
}

type RemoteStreamStats struct {
	UID             string
	AudioStats      RemoteAudioStats
	VideoStats      RemoteVideoStats
	RemoteTXQuality NetworkQuality
	RemoteRXQuality NetworkQuality
	IsScreen        bool
}

type VideoSolutionDescription struct {
	Width            int
	Height           int
	Fps              int
	MaxSendKbps      int
	MinSendKbps      int
	ScaleMode        VideoStreamScaleMode
	CodecName        VideoCodecType
	CodecMode        VideoCodecMode
	EncodePreference VideoEncodePreference
}

type MediaStreamInfo struct {
	UserID       string
	StreamIndex  StreamIndex
	IsScreen     bool
	HasVideo     bool
	HasAudio     bool
	Profiles     []VideoSolutionDescription
	ProfileCount int
	MaxProfile   VideoSolutionDescription
}

type ForwardStreamStateInfo struct {
	RoomID string
	State  ForwardStreamState
	Error  ForwardStreamError
}

type ForwardStreamEventInfo struct {
	RoomID string
	Event  ForwardStreamEvent
}

type NetworkQualityStats struct {
	UID            string
	FractionLost   float64
	RTT            int
	TotalBandwidth int
	TXQuality      NetworkQuality
	RXQuality      NetworkQuality
}

type SubtitleMessage struct {
	UserID   string
	Text     string
	Language string
	Mode     SubtitleMode
	Sequence int
	Definite bool
}

type RTCRoomStats struct {
	TXLostrate         float32
	RXLostrate         float32
	RTT                int
	Duration           uint
	TXBytes            uint
	RXBytes            uint
	TXKbitrate         uint16
	RXKbitrate         uint16
	RXAudioKbitrate    uint16
	TXAudioKbitrate    uint16
	RXVideoKbitrate    uint16
	TXVideoKbitrate    uint16
	RXScreenKbitrate   uint16
	TXScreenKbitrate   uint16
	UserCount          uint
	CpuAppUsage        float64
	CpuTotalUsage      float64
	TXJitter           int
	RXJitter           int
	TXCellularKbitrate uint16
	RXCellularKbitrate uint16
}

type IAudioFrame interface {
	TimestampUs() int64
	SampleRate() AudioSampleRate
	Channel() AudioChannel
	Data() []byte
	DataSize() int
	FrameType() AudioFrameType
	IsMutedData() bool
	release()
}

type IEncodedVideoFrame interface {
	CodecType() VideoCodecType
	TimestampUs() int64
	TimestampDtsUs() int64
	Width() int
	Height() int
	PictureType() VideoPictureType
	Rotation() VideoRotation
	Data() []byte
	DataSize() int
	ShallowCopy() IEncodedVideoFrame
	Release()
}

type ProblemFeedbackRoomInfo struct {
	RoomID string
	UserID string
}

type ProblemFeedbackInfo struct {
	ProblemDesc   string
	RoomInfos     []ProblemFeedbackRoomInfo
	RoomInfoCount int
}

type LogConfig struct {
	LogPath           string
	LogLevel          LocalLogLevel
	LogFileSize       uint
	LogFilenamePrefix string
}

type AudioPropertiesConfig struct {
	Interval            int
	EnableSpectrum      bool
	EnableVad           bool
	LocalMainReportMode AudioReportMode
	Smooth              float32
	AudioReportMode     AudioPropertiesMode
	EnableVoicePitch    bool
}

type AudioFrameBuilder struct {
	SampleRate  AudioSampleRate
	Channel     AudioChannel
	TimestampUs int64
	Data        []byte
}

type EncodedVideoFrameBuilder struct {
	CodecType      VideoCodecType
	PictureType    VideoPictureType
	Rotation       VideoRotation
	Data           []byte
	Size           int
	Width          int
	Height         int
	TimestampUs    int64
	TimestampDtsUs int64
}

type SubtitleConfig struct {
	Mode           SubtitleMode
	TargetLanguage string
}

type VideoEncoderConfig struct {
	Width             int
	Height            int
	FrameRate         int
	MaxBitRate        int
	MinBitRate        int
	EncoderPerference VideoEncodePreference
}

type VideoCanvas struct {
	View             uintptr
	RenderMode       RenderMode
	BackgroundColor  uint32
	RenderTargetType RenderTargetType
	RenderRotation   VideoRotation
}

type VideoRateInfo struct {
	Fps            int
	BitrateKbps    int
	MinBitrateKbps int
}

type StreamSycnInfoConfig struct {
	StreamIndex StreamIndex
	RepeatCount int
	StreamType  SyncInfoStreamType
}

type CloudProxyInfo struct {
	CloudProxyIP   string
	CloudProxyPort int
}

type CloudProxyConfiguration struct {
	CloudProxies    []CloudProxyInfo
	CloudProxyCount int
}

type LocalProxyConfiguration struct {
	LocalProxyType     LocalProxyType
	LocalProxyIp       string
	LocalProxyPort     int
	LocalProxyUsername string
	LocalProxyPassword string
}

type FovVideoTileInfo struct {
	HdWidth    uint
	HdHeight   uint
	LdWidth    uint
	LdHeight   uint
	TileWidth  uint
	TileHeight uint
	HdRow      uint
	HdColumn   uint
	LdRow      uint
	LdColumn   uint
	DestRow    uint
	DestColumn uint
	TileData   []byte
}

type VideoPreprocessorConfig struct {
	RequiredPixelFormat VideoPixelFormat
}

type RecordingConfig struct {
	DirPath  string
	FileType RecordingFileType
}

type AudioRecordingConfig struct {
	AbsoluteFileName string
	FrameSource      AudioFrameSource
	SampleRate       AudioSampleRate
	Channel          AudioChannel
	Quality          AudioQuality
}

type RemoteVideoSinkConfig struct {
	Position      RemoteVideoSinkPosition
	PixelFormat   VideoPixelFormat
	ApplyRotation VideoApplyRotation
	MirrorType    VideoSinkMirrorType
}

type VideoDeviceInfo struct {
	DeviceID      string
	DeviceName    string
	DeviceVid     int64
	DevicePid     int64
	TransportType DeviceTransportType
	DeviceFacing  VideoDeviceFacing
}

type IVideoDeviceCollection interface {
	GetCount() int
	Release()
	GetDevice(index int, deviceInfo VideoDeviceInfo) int
}

type ivideoDeviceCollectionProxyImpl struct {
	nativeVideoDeviceCollection native.IVideoDeviceCollection
}

func (p ivideoDeviceCollectionProxyImpl) GetCount() int {
	return p.nativeVideoDeviceCollection.GetCount()
}
func (p ivideoDeviceCollectionProxyImpl) Release() {
	p.nativeVideoDeviceCollection.Release()
}
func (p ivideoDeviceCollectionProxyImpl) GetDevice(index int, deviceInfo VideoDeviceInfo) int {
	nativeDeviceInfo := toNaitveVideoDeviceInfo(deviceInfo)
	defer deleteNativeVideoDeviceInfo(nativeDeviceInfo)
	return p.nativeVideoDeviceCollection.GetDevice(index, nativeDeviceInfo)
}

type IVideoDeviceManager interface {
	EnumerateVideoCaptureDevices() IVideoDeviceCollection
	SetVideoCaptureDevice(deviceID string) int
	GetVideoCaptureDevice(deviceID string) int
}

type ivideoDeviceManagerProxyImpl struct {
	native native.IVideoDeviceManager
}

func (p ivideoDeviceManagerProxyImpl) EnumerateVideoCaptureDevices() IVideoDeviceCollection {
	nativeDeviceCollections := p.native.EnumerateVideoCaptureDevices()
	return ivideoDeviceCollectionProxyImpl{
		nativeVideoDeviceCollection: nativeDeviceCollections,
	}
}
func (p ivideoDeviceManagerProxyImpl) SetVideoCaptureDevice(deviceID string) int {
	return p.native.SetVideoCaptureDevice(deviceID)
}
func (p ivideoDeviceManagerProxyImpl) GetVideoCaptureDevice(deviceID string) int {
	return p.native.GetVideoCaptureDevice(deviceID)
}
