package rtcengine

import (
	"bytedance/bytertc/native"
	"runtime"
)

type encodedVideoFrameProxyImp struct {
	IEncodedVideoFrame
	nativeVideoFrame native.IEncodedVideoFrame
}

func deleteEncodedVideoFrameProxyImp(f *encodedVideoFrameProxyImp) {
	f.Release()
}

func newEncodedVideoFrameProxyImp(nativeVideoFrame native.IEncodedVideoFrame, needRelease bool) *encodedVideoFrameProxyImp {
	tmp := &encodedVideoFrameProxyImp{
		nativeVideoFrame: nativeVideoFrame,
	}
	if needRelease {
		runtime.SetFinalizer(tmp, deleteEncodedVideoFrameProxyImp)
	}
	return tmp
}

func (f *encodedVideoFrameProxyImp) CodecType() VideoCodecType {
	return f.nativeVideoFrame.CodecType()
}
func (f *encodedVideoFrameProxyImp) TimestampUs() int64 {
	return f.nativeVideoFrame.TimestampUs()
}
func (f *encodedVideoFrameProxyImp) TimestampDtsUs() int64 {
	return f.nativeVideoFrame.TimestampDtsUs()
}
func (f *encodedVideoFrameProxyImp) Width() int {
	return f.nativeVideoFrame.Width()
}
func (f *encodedVideoFrameProxyImp) Height() int {
	return f.nativeVideoFrame.Height()
}
func (f *encodedVideoFrameProxyImp) PictureType() VideoPictureType {
	return f.nativeVideoFrame.PictureType()
}
func (f *encodedVideoFrameProxyImp) Rotation() VideoRotation {
	return f.nativeVideoFrame.Rotation()
}
func (f *encodedVideoFrameProxyImp) Data() []byte {
	// explicitly copy the slice to decouple with the underlying AudioFrame lifetime
	originalData := native.GetDataChunkFromIEncodedVideoFrame(f.nativeVideoFrame)
	data := make([]byte, len(originalData))
	copy(data, originalData)
	return data
}
func (f *encodedVideoFrameProxyImp) DataSize() int {
	return f.nativeVideoFrame.DataSize()
}
func (f *encodedVideoFrameProxyImp) ShallowCopy() IEncodedVideoFrame {
	// TODO:(@tanghaoru) shallow copy
	return &encodedVideoFrameProxyImp{
		nativeVideoFrame: f.nativeVideoFrame,
	}
}
func (f *encodedVideoFrameProxyImp) Release() {
	f.nativeVideoFrame.Release()
}

func (f *encodedVideoFrameProxyImp) toNativeEncodedVideoFrame() native.IEncodedVideoFrame {
	return f.nativeVideoFrame
}
