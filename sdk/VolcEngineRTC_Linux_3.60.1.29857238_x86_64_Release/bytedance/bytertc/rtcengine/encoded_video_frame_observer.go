package rtcengine

import (
	"bytedance/bytertc/native"
)

type ILocalEncodedVideoFrameObserver interface {
	OnLocalEncodedVideoFrame(index StreamIndex, videoStream IEncodedVideoFrame)
}

type LocalEncodedVideoFrameObserverDefaultImpl struct {
	ILocalEncodedVideoFrameObserver
}

func (h *LocalEncodedVideoFrameObserverDefaultImpl) OnLocalEncodedVideoFrame(index StreamIndex, videoStream IEncodedVideoFrame) {
}

type nativeILocalEncodedVideoFrameObserver struct {
	goHandler ILocalEncodedVideoFrameObserver
}

func (h *nativeILocalEncodedVideoFrameObserver) OnLocalEncodedVideoFrame(arg2 int, arg3 native.IEncodedVideoFrame) {
	frameProxy := newEncodedVideoFrameProxyImp(arg3, false)
	h.goHandler.OnLocalEncodedVideoFrame(arg2, frameProxy)
}

type IRemoteEncodedVideoFrameObserver interface {
	OnRemoteEncodedVideoFrame(StreamInfo *RemoteStreamKey, VideoStream IEncodedVideoFrame)
}

type RemoteEncodedVideoFrameObserverDefaultImpl struct {
	IRemoteEncodedVideoFrameObserver
}

func (h *RemoteEncodedVideoFrameObserverDefaultImpl) OnRemoteEncodedVideoFrame(StreamInfo *RemoteStreamKey, VideoStream IEncodedVideoFrame) {
}

type nativeIRemoteEncodedVideoFrameObserver struct {
	goHandler IRemoteEncodedVideoFrameObserver
}

func (h *nativeIRemoteEncodedVideoFrameObserver) OnRemoteEncodedVideoFrame(arg2 native.RemoteStreamKey, arg3 native.IEncodedVideoFrame) {
	remoteStreamKey := toRemoteStreamKey(arg2)
	frameProxy := newEncodedVideoFrameProxyImp(arg3, false)
	h.goHandler.OnRemoteEncodedVideoFrame(&remoteStreamKey, frameProxy)
}
