package rtcengine

type IEncryptHandler interface {
	OnEncryptData(data []byte, buf []byte) uint
	OnDecryptData(data []byte, buf []byte) uint
}

type IEncryptHandlerImp struct {
	IEncryptHandler
}

func (h IEncryptHandlerImp) OnEncryptData(arg2 []byte, arg3 []byte) (_swig_ret uint) {
	arg3 = arg2
	return uint(len(arg2))
}
func (h IEncryptHandlerImp) OnDecryptData(arg2 []byte, arg3 []byte) (_swig_ret uint) {
	arg3 = arg2
	return uint(len(arg2))
}

type nativeEncryptHandlerImp struct {
	GoEncryptHandler IEncryptHandler
}

func (h *nativeEncryptHandlerImp) OnEncryptData(data []byte, buf []byte) uint {
	return h.GoEncryptHandler.OnEncryptData(data, buf)
}
func (h *nativeEncryptHandlerImp) OnDecryptData(data []byte, buf []byte) uint {
	return h.GoEncryptHandler.OnEncryptData(data, buf)
}
