package rtcengine

type VideoRotation = int

const (
	VideoRotation0   VideoRotation = 0
	VideoRotation90                = 90
	VideoRotation180               = 180
	VideoRotation270               = 270
)

type FallbackOrRecoverReason = int

const (
	FallbackOrRecoverReasonUnknown                        FallbackOrRecoverReason = -1
	FallbackOrRecoverReasonSubscribeFallbackByBandwidth                           = 0
	FallbackOrRecoverReasonSubscribeFallbackByPerformance                         = 1
	FallbackOrRecoverReasonSubscribeRecoverByBandwidth                            = 2
	FallbackOrRecoverReasonSubscribeRecoverByPerformance                          = 3
	FallbackOrRecoverReasonPublishFallbackByBandwidth                             = 4
	FallbackOrRecoverReasonPublishFallbackByPerformance                           = 5
	FallbackOrRecoverReasonPublishRecoverByBandwidth                              = 6
	FallbackOrRecoverReasonPublishRecoverByPerformance                            = 7
)

type VideoCodecType = int

const (
	VideoCodecTypeUnknown VideoCodecType = 0
	VideoCodecTypeH264                   = 1
	VideoCodecTypeByteVC1                = 2
)

type AudioSampleRate = int

const (
	AudioSampleRateAuto  AudioSampleRate = -1
	AudioSampleRate8000                  = 8000
	AudioSampleRate11025                 = 11025
	AudioSampleRate16000                 = 16000
	AudioSampleRate22050                 = 22050
	AudioSampleRate24000                 = 24000
	AudioSampleRate32000                 = 32000
	AudioSampleRate44100                 = 44100
	AudioSampleRate48000                 = 48000
)

type AudioChannel = int

const (
	AudioChannelAuto   AudioChannel = -1
	AudioChannelMono                = 1
	AudioChannelStereo              = 2
)

type AudioFrameCallbackMethod = int

const (
	AudioFrameCallbackMethodRecord       AudioFrameCallbackMethod = 0
	AudioFrameCallbackMethodPlayback                              = 1
	AudioFrameCallbackMethodMixed                                 = 2
	AudioFrameCallbackMethodRemoteUser                            = 3
	AudioFrameCallbackMethodRecordScreen                          = 4
	AudioFrameCallbackMethodCaptureMixed                          = 5
)

type AudioSourceType = int

const (
	AudioSourceTypeExternal AudioSourceType = 0
	AudioSourceTypeInternal                 = 1
)

type StreamIndex = int

const (
	StreamIndexMain   StreamIndex = 0
	StreamIndexScreen             = 1
	StreamIndex3rd                = 2
	StreamIndex4th                = 3
	StreamIndex5th                = 4
	StreamIndex6th                = 5
	StreamIndex7th                = 6
	StreamIndexMax                = 7
)

type RoomProfileType = int

const (
	RoomProfileTypeCommunication      RoomProfileType = 0
	RoomProfileTypeLiveBroadcasting                   = 1
	RoomProfileTypeGame                               = 2
	RoomProfileTypeCloudGame                          = 3
	RoomProfileTypeLowLatency                         = 4
	RoomProfileTypeChat                               = 5
	RoomProfileTypeChatRoom                           = 6
	RoomProfileTypeLwTogether                         = 7
	RoomProfileTypeGameHD                             = 8
	RoomProfileTypeCoHost                             = 9
	RoomProfileTypeInteractivePodcast                 = 10
	RoomProfileTypeKTV                                = 11
	RoomProfileTypeChorus                             = 12
	RoomProfileTypeVRChat                             = 13
	RoomProfileTypeGameStreaming                      = 14
	RoomProfileTypeLanLiveStreaming                   = 15
	RoomProfileTypeMeeting                            = 16
	RoomProfileTypeMeetingRoom                        = 17
	RoomProfileTypeClassroom                          = 18
)

type AudioProcessorMethod = int

const (
	AudioProcessorMethodRecord     AudioProcessorMethod = 0
	AudioProcessorMethodPlayback                        = 1
	AudioProcessorMethodRemoteUser                      = 2
	AudioProcessorMethodEarMonitor                      = 3
	AudioProcessorMethodScreen                          = 4
)

type RTCAudioDeviceType = int

const (
	RTCAudioDeviceTypeUnknown             RTCAudioDeviceType = -1
	RTCAudioDeviceTypeRenderDevice                           = 0
	RTCAudioDeviceTypeCaptureDevice                          = 1
	RTCAudioDeviceTypeScreenCaptureDevice                    = 2
)

type AudioRoute = int

const (
	AudioRouteDefault          AudioRoute = -1
	AudioRouteHeadset                     = 1
	AudioRouteEarpiece                    = 2
	AudioRouteSpeakerphone                = 3
	AudioRouteHeadsetBluetooth            = 4
	AudioRouteHeadsetUSB                  = 5
)

type AudioPlaybackDevice = int

const (
	AudioPlaybackDeviceHeadset          AudioPlaybackDevice = 1
	AudioPlaybackDeviceEarpiece                             = 2
	AudioPlaybackDeviceSpeakerphone                         = 3
	AudioPlaybackDeviceHeadsetBluetooth                     = 4
	AudioPlaybackDeviceHeadsetUSB                           = 5
)

type AudioScenarioType = int

const (
	AudioScenarioTypeDefault       AudioScenarioType = 0
	AudioScenarioTypeChatRoom                        = 1
	AudioScenarioTypeGameStreaming                   = 2
	AudioScenarioTypeChorus                          = 3
)

type VoiceChangerType = int

const (
	VoiceChangerTypeOriginal VoiceChangerType = 0
	VoiceChangerTypeGiant                     = 1
	VoiceChangerTypeChipmunk                  = 2
	VoiceChangerTypeMinionst                  = 3
	VoiceChangerTypeVibrato                   = 4
	VoiceChangerTypeRobot                     = 5
)

type VoiceReverbType = int

const (
	VoiceReverbTypeOriginal      VoiceReverbType = 0
	VoiceReverbTypeEcho                          = 1
	VoiceReverbTypeConcert                       = 2
	VoiceReverbTypeEthereal                      = 3
	VoiceReverbTypeKTV                           = 4
	VoiceReverbTypeStudio                        = 5
	VoiceReverbTypeVirtualStereo                 = 6
	VoiceReverbTypeSpacious                      = 7
	VoiceReverbType3D                            = 8
	VoiceReverbTypePop                           = 9
	VoiceReverbTypeDisco                         = 10
	VoiceReverbTypeOldRecord                     = 11
	VoiceReverbTypeHarmony                       = 12
	VoiceReverbTypeRock                          = 13
	VoiceReverbTypeBlues                         = 14
	VoiceReverbTypeJazz                          = 15
	VoiceReverbTypeElectronic                    = 16
	VoiceReverbTypeVinyl                         = 17
	VoiceReverbTypeChamber                       = 18
)

type VoiceEqualizationBandFrequency = int

const (
	VoiceEqualizationBandFrequency31  VoiceEqualizationBandFrequency = 0
	VoiceEqualizationBandFrequency62                                 = 1
	VoiceEqualizationBandFrequency125                                = 2
	VoiceEqualizationBandFrequency250                                = 3
	VoiceEqualizationBandFrequency500                                = 4
	VoiceEqualizationBandFrequency1k                                 = 5
	VoiceEqualizationBandFrequency2k                                 = 6
	VoiceEqualizationBandFrequency4k                                 = 7
	VoiceEqualizationBandFrequency8k                                 = 8
	VoiceEqualizationBandFrequency16k                                = 9
)

type AudioMixingState = int

const (
	AudioMixingStatePreloaded   AudioMixingState = 0
	AudioMixingStatePlaying                      = 1
	AudioMixingStatePaused                       = 2
	AudioMixingStateStopped                      = 3
	AudioMixingStateFailed                       = 4
	AudioMixingStateFinished                     = 5
	AudioMixingStatePCMEnabled                   = 6
	AudioMixingStatePCMDisabled                  = 7
)

type AudioMixingError = int

const (
	AudioMixingErrorOk                   AudioMixingError = 0
	AudioMixingErrorPreloadFailed                         = 1
	AudioMixingErrorStartFailed                           = 2
	AudioMixingErrorIdNotFound                            = 3
	AudioMixingErrorSetPositionFailed                     = 4
	AudioMixingErrorInValidVolume                         = 5
	AudioMixingErrorLoadConflict                          = 6
	AudioMixingErrorIdTypeNotMatch                        = 7
	AudioMixingErrorInValidPitch                          = 8
	AudioMixingErrorInValidAudioTrack                     = 9
	AudioMixingErrorIsStarting                            = 10
	AudioMixingErrorInValidPlaybackSpeed                  = 11
	AudioMixingErrorCanNotOpen                            = 701
)

type PlayerState = int

const (
	PlayerStateIdle         PlayerState = 0
	PlayerStatePreloaded                = 1
	PlayerStateOpened                   = 2
	PlayerStatePlaying                  = 3
	PlayerStatePaused                   = 4
	PlayerStateStopped                  = 5
	PlayerStateFailed                   = 6
	PlayerStateFinished                 = 7
	PlayerStateLoopFinished             = 8
)

type PlayerEvent = int

const (
	PlayerEventSelectAudioTrackBegin     PlayerEvent = 0
	PlayerEventSelectAudioTrackCompleted             = 1
	PlayerEventSelectAudioTrackFailed                = 2
	PlayerEventSeekBegin                             = 3
	PlayerEventSeekCompleted                         = 4
	PlayerEventSeekFailed                            = 5
)

type PlayerError = int

const (
	PlayerErrorOK                     PlayerError = 0
	PlayerErrorFormatNotSupport                   = 1
	PlayerErrorInvalidPath                        = 2
	PlayerErrorInvalidState                       = 3
	PlayerErrorInvalidPosition                    = 4
	PlayerErrorInvalidVolume                      = 5
	PlayerErrorInvalidPitch                       = 6
	PlayerErrorInvalidAudioTrackIndex             = 7
	PlayerErrorInvalidPlaybackSpeed               = 8
	PlayerErrorInvalidEffectId                    = 9
)

type EarMonitorMode = int

const (
	EarMonitorModeOff EarMonitorMode = 0
	EarMonitorModeOn                 = 1
)

type AudioCodecType = int

const (
	AudioCodecTypeNone      AudioCodecType = 0
	AudioCodecTypeOpus                     = 1
	AudioCodecTypeAac                      = 2
	AudioCodecTypeAacLC                    = 2
	AudioCodecTypeAacHEv1                  = 3
	AudioCodecTypeAacHEv2                  = 4
	AudioCodecTypeAacLCadts                = 5
)

type AudioFormatType = int

const (
	AudioFormatTypeRawPCMs16 AudioFormatType = 0
	AudioFormatTypeRawPCMs32                 = 1
)

type BluetoothMode = int

const (
	BluetoothModeAuto BluetoothMode = 0
	BluetoothModeA2DP               = 1
	BluetoothModeHFP                = 2
)

type AudioRenderType = int

const (
	AudioRenderTypeExternal AudioRenderType = 0
	AudioRenderTypeInternal                 = 1
)

type AudioMixingType = int

const (
	AudioMixingTypePlayout           AudioMixingType = 0
	AudioMixingTypePublish                           = 1
	AudioMixingTypePlayoutAndPublish                 = 2
)

type AudioMixingDualMonoMode = int

const (
	AudioMixingDualMonoModeAuto AudioMixingDualMonoMode = 0
	AudioMixingDualMonoModeL                            = 1
	AudioMixingDualMonoModeR                            = 2
	AudioMixingDualMonoModeMix                          = 3
)

type AudioReportMode = int

const (
	AudioReportModeNormal     AudioReportMode = 0
	AudioReportModeDisconnect                 = 1
	AudioReportModeReset                      = 2
)

type AudioPropertiesMode = int

const (
	AudioPropertiesModeMicrophone  AudioPropertiesMode = 0
	AudioPropertiesModeAudioMixing                     = 1
)

type AudioProfileType = int

const (
	AudioProfileTypeDefault        AudioProfileType = 0
	AudioProfileTypeFluent                          = 1
	AudioProfileTypeStandard                        = 2
	AudioProfileTypeHD                              = 3
	AudioProfileTypeStandardStereo                  = 4
	AudioProfileTypeHDMono                          = 5
)

type AnsMode = int

const (
	AnsModeDisable   AnsMode = 0
	AnsModeLow               = 1
	AnsModeMedium            = 2
	AnsModeHigh              = 3
	AnsModeAutomatic         = 4
)

type AudioAbilityType = int

const (
	AudioAbilityTypeUnknown AudioAbilityType = -1
	AudioAbilityTypeAble                     = 0
	AudioAbilityTypeUnable                   = 1
)

type AudioAlignmentMode = int

const (
	AudioAlignmentModeOff         AudioAlignmentMode = 0
	AudioAlignmentModeAudioMixing                    = 1
)

type MulDimSingScoringMode = int

const (
	MulDimSingScoringModeNote MulDimSingScoringMode = 0
)

type AudioFrameSource = int

const (
	AudioFrameSourceMic      AudioFrameSource = 0
	AudioFrameSourcePlayback                  = 1
	AudioFrameSourceMixed                     = 2
)

type AudioQuality = int

const (
	AudioQualityLow       AudioQuality = 0
	AudioQualityMedium                 = 1
	AudioQualityHigh                   = 2
	AudioQualityUltraHigh              = 3
)

type AudioRecordingState = int

const (
	AudioRecordingStateError      AudioRecordingState = 0
	AudioRecordingStateProcessing                     = 1
	AudioRecordingStateSuccess                        = 2
)

type AudioRecordingErrorCode = int

const (
	AudioRecordingErrorCodeOk             AudioRecordingErrorCode = 0
	AudioRecordingErrorCodeNoPermission                           = -1
	AudioRecordingErrorCodeNotInRoom                              = -2
	AudioRecordingErrorCodeAlreadyStarted                         = -3
	AudioRecordingErrorCodeNotStarted                             = -4
	AudioRecordingErrorCodeNotSupport                             = -5
	AudioRecordingErrorCodeOther                                  = -6
)

type AudioFrameType = int

const (
	AudioFrameTypePCM16 AudioFrameType = 0
)

type TorchState = int

const (
	TorchStateOff TorchState = 0
	TorchStateOn             = 1
)

type MusicFilterType = int

const (
	MusicFilterTypeNone                MusicFilterType = 0
	MusicFilterTypeWithoutLyric                        = 1
	MusicFilterTypeUnsupportedScore                    = 2
	MusicFilterTypeUnsupportedAccopmay                 = 4
	MusicFilterTypeUnsupportedClimx                    = 8
)

type AudioTrackType = int

const (
	AudioTrackTypeOriginal AudioTrackType = 1
	AudioTrackTypeAccompy                 = 2
)

type AudioPlayType = int

const (
	AudioPlayTypeLocal          AudioPlayType = 0
	AudioPlayTypeRemote                       = 1
	AudioPlayTypeLocalAndRemote               = 2
)

type PlayState = int

const (
	PlayStatePlaying  PlayState = 1
	PlayStatePaused             = 2
	PlayStateStoped             = 3
	PlayStateFailed             = 4
	PlayStateFinished           = 5
)

type LyricStatus = int

const (
	LyricStatusNone     LyricStatus = 0
	LyricStatusRC                   = 1
	LyricStatusLRC                  = 2
	LyricStatusRCAndLRC             = 3
)

type DownloadLyricType = int

const (
	DownloadLyricTypeRC  DownloadLyricType = 0
	DownloadLyricTypeLRC                   = 1
)

type DownloadFileType = int

const (
	DownloadFileTypeMusic DownloadFileType = 1
	DownloadFileTypeKRC                    = 2
	DownloadFileTypeLRC                    = 3
	DownloadFileTypeMIDI                   = 4
)

type KTVErrorCode = int

const (
	KTVErrorCodeOK                    = 0
	KTVErrorCodeAppidInValid          = -3000
	KTVErrorCodeParasInValid          = -3001
	KTVErrorCodeGetMusicFailed        = -3002
	KTVErrorCodeGetLyricFailed        = -3003
	KTVErrorCodeMusicTakedown         = -3004
	KTVErrorCodeMusicDownload         = -3005
	KTVErrorCodeMidiDownloadFailed    = -3006
	KTVErrorCodeSystemBusy            = -3007
	KTVErrorCodeNetwork               = -3008
	KTVErrorCodeNotJoinRoom           = -3009
	KTVErrorCodeParseData             = -3010
	KTVErrorCodeDownload              = -3011
	KTVErrorCodeDownloading           = -3012
	KTVErrorCodeInternalDomain        = -3013
	KTVErrorCodeInsufficientDiskSpace = -3014
	KTVErrorCodeMusicDecryptionFailed = -3015
	KTVErrorCodeFileRenameFailed      = -3016
	KTVErrorCodeDownloadTimeOut       = -3017
	KTVErrorCodeClearCacheFailed      = -3018
	KTVErrorCodeDownloadCanceled      = -3019
)

type KTVPlayerErrorCode = int

const (
	KTVPlayerErrorCodeOK               = 0
	KTVPlayerErrorCodeFileNotExist     = -3020
	KTVPlayerErrorCodeFileError        = -3021
	KTVPlayerErrorCodeNotJoinRoom      = -3022
	KTVPlayerErrorCodeParam            = -3023
	KTVPlayerErrorCodeStartError       = -3024
	KTVPlayerErrorCodeMixIdError       = -3025
	KTVPlayerErrorCodePositionError    = -3026
	KTVPlayerErrorCodeAudioVolumeError = -3027
	KTVPlayerErrorCodeTypeError        = -3028
	KTVPlayerErrorCodePitchError       = -3029
	KTVPlayerErrorCodeAudioTrackError  = -3030
	KTVPlayerErrorCodeStartingError    = -3031
)

type ReturnStatus = int

const (
	ReturnStatusSuccess                               ReturnStatus = 0
	ReturnStatusFailure                                            = -1
	ReturnStatusParameterErr                                       = -2
	ReturnStatusWrongState                                         = -3
	ReturnStatusHasInRoom                                          = -4
	ReturnStatusHasInLogin                                         = -5
	ReturnStatusHasInEchoTest                                      = -6
	ReturnStatusNeitherVideoNorAudio                               = -7
	ReturnStatusRoomIdInUse                                        = -8
	ReturnStatusScreenNotSupport                                   = -9
	ReturnStatusNotSupport                                         = -10
	ReturnStatusResourceOverflow                                   = -11
	ReturnStatusVideoNotSupport                                    = -12
	ReturnStatusAudioNoFrame                                       = -101
	ReturnStatusAudioNotImplemented                                = -102
	ReturnStatusAudioNoPermission                                  = -103
	ReturnStatusAudioDeviceNotExists                               = -104
	ReturnStatusAudioDeviceFormatNotSupport                        = -105
	ReturnStatusAudioDeviceNoDevice                                = -106
	ReturnStatusAudioDeviceCannotUse                               = -107
	ReturnStatusAudioDeviceInitFailed                              = -108
	ReturnStatusAudioDeviceStartFailed                             = -109
	ReturnStatusAudioDeviceProcessNotExist                         = -110
	ReturnStatusAudioDeviceSpecifyingProcessUnsupport              = -111
	ReturnStatusNativeInvalid                                      = -201
	ReturnStatusVideoTimeStampWarning                              = -202
)

type StreamRemoveReason = int

const (
	StreamRemoveReasonUnpublish             StreamRemoveReason = 0
	StreamRemoveReasonPublishFailed                            = 1
	StreamRemoveReasonKeepLiveFailed                           = 2
	StreamRemoveReasonClientDisconnected                       = 3
	StreamRemoveReasonRepublish                                = 4
	StreamRemoveReasonOther                                    = 5
	StreamRemoveReasonPrivilegeTokenExpired                    = 6
)

type UserRoleType = int

const (
	UserRoleTypeBroadcaster    UserRoleType = 1
	UserRoleTypeSilentAudience              = 2
)

type UserVisibilityChangeError = int

const (
	UserVisibilityChangeErrorOk                 UserVisibilityChangeError = 0
	UserVisibilityChangeErrorUnknown                                      = 1
	UserVisibilityChangeErrorTooManyVisibleUser                           = 2
)

type DataMessageSourceType = int

const (
	DataMessageSourceTypeDefault DataMessageSourceType = 0
	DataMessageSourceTypeSystem                        = 1
)

type SEICountPerFrame = int

const (
	SEICountPerFrameSingle SEICountPerFrame = 0
	SEICountPerFrameMulti                   = 1
)

type MediaDeviceType = int

const (
	MediaDeviceTypeAudioUnknown             MediaDeviceType = -1
	MediaDeviceTypeAudioRenderDevice                        = 0
	MediaDeviceTypeAudioCaptureDevice                       = 1
	MediaDeviceTypeVideoRenderDevice                        = 2
	MediaDeviceTypeVideoCaptureDevice                       = 3
	MediaDeviceTypeScreenVideoCaptureDevice                 = 4
	MediaDeviceTypeScreenAudioCaptureDevice                 = 5
)

type MediaDeviceState = int

const (
	MediaDeviceStateStarted             MediaDeviceState = 1
	MediaDeviceStateStopped                              = 2
	MediaDeviceStateRuntimeError                         = 3
	MediaDeviceStatePaused                               = 4
	MediaDeviceStateResumed                              = 5
	MediaDeviceStateAdded                                = 10
	MediaDeviceStateRemoved                              = 11
	MediaDeviceStateInterruptionBegan                    = 12
	MediaDeviceStateInterruptionEnded                    = 13
	MediaDeviceStateBecomeSystemDefault                  = 14
	MediaDeviceStateResignSystemDefault                  = 15
	MediaDeviceStateListUpdated                          = 16
)

type MediaDeviceError = int

const (
	MediaDeviceErrorOK                                           MediaDeviceError = 0
	MediaDeviceErrorDeviceNoPermission                                            = 1
	MediaDeviceErrorDeviceBusy                                                    = 2
	MediaDeviceErrorDeviceFailure                                                 = 3
	MediaDeviceErrorDeviceNotFound                                                = 4
	MediaDeviceErrorDeviceDisconnected                                            = 5
	MediaDeviceErrorDeviceNoCallback                                              = 6
	MediaDeviceErrorDeviceUNSupportFormat                                         = 7
	MediaDeviceErrorDeviceNotFindGroupId                                          = 8
	MediaDeviceErrorDeviceNotAvailableInBackground                                = 9
	MediaDeviceErrorDeviceVideoInUseByAnotherClient                               = 10
	MediaDeviceErrorDeviceNotAvailableWithMultipleForegroundApps                  = 11
	MediaDeviceErrorDeviceNotAvailableDueToSystemPressure                         = 12
	MediaDeviceErrorAudioServerNeedRestart                                        = 13
)

type MediaDeviceWarning = int

const (
	MediaDeviceWarningOK                                MediaDeviceWarning = 0
	MediaDeviceWarningOperationDenied                                      = 1
	MediaDeviceWarningCaptureSilence                                       = 2
	MediaDeviceWarningAndroidSysSilence                                    = 3
	MediaDeviceWarningAndroidSysSilenceDisappear                           = 4
	MediaDeviceWarningDetectClipping                                       = 10
	MediaDeviceWarningDetectLeakEcho                                       = 11
	MediaDeviceWarningDetectLowSNR                                         = 12
	MediaDeviceWarningDetectInsertSilence                                  = 13
	MediaDeviceWarningCaptureDetectSilence                                 = 14
	MediaDeviceWarningCaptureDetectSilenceDisappear                        = 15
	MediaDeviceWarningCaptureDetectHowling                                 = 16
	MediaDeviceWarningSuddenImpluseDetected                                = 17
	MediaDeviceWarningSquareWavSoundDetected                               = 18
	MediaDeviceWarningSetAudioRouteInvalidScenario                         = 20
	MediaDeviceWarningSetAudioRouteNotExists                               = 21
	MediaDeviceWarningSetAudioRouteFailedByPriority                        = 22
	MediaDeviceWarningSetAudioRouteNotVoipMode                             = 23
	MediaDeviceWarningSetAudioRouteDeviceNotStart                          = 24
	MediaDeviceWarningSetBluetoothModeScenarioUnsupport                    = 25
	MediaDeviceWarningSetBluetoothModeUnsupport                            = 26
	MediaDeviceWarningRecordingUseSilentDevice                             = 27
	MediaDeviceWarningPlayoutUseSilentDevice                               = 28
	MediaDeviceWarningRecordingAllProcess                                  = 29
	MediaDeviceWarningRecordingAllProcessFailed                            = 30
)

type StreamSubscribeState = int

const (
	StreamSubscribeStateSuccess              StreamSubscribeState = 0
	StreamSubscribeStateFailedNotInRoom                           = 1
	StreamSubscribeStateFailedStreamNotFound                      = 2
	StreamSubscribeStateFailedOverLimit                           = 3
)

type PublishState = int

const (
	PublishStatePublished   PublishState = 0
	PublishStateUnpublished              = 1
)

type SubscribeState = int

const (
	SubscribeStateSubscribed   SubscribeState = 0
	SubscribeStateUnsubscribed                = 1
)

type PublishStateChangeReason = int

const (
	PublishStateChangeReasonPublish                  PublishStateChangeReason = 0
	PublishStateChangeReasonUnpublish                                         = 1
	PublishStateChangeReasonNoPublishPermission                               = 2
	PublishStateChangeReasonOverStreamPublishLimit                            = 3
	PublishStateChangeReasonMultiRoomUnpublishFailed                          = 4
	PublishStateChangeReasonPublishStreamFailed                               = 5
	PublishStateChangeReasonPublishStreamForbidden                            = 6
	PublishStateChangeReasonUserInPublish                                     = 7
)

type SubscribeStateChangeReason = int

const (
	SubscribeStateChangeReasonSubscribe                SubscribeStateChangeReason = 0
	SubscribeStateChangeReasonUnsubscribe                                         = 1
	SubscribeStateChangeReasonRemotePublish                                       = 2
	SubscribeStateChangeReasonRemoteUnpublish                                     = 3
	SubscribeStateChangeReasonStreamFailed5xx                                     = 4
	SubscribeStateChangeReasonStreamFailed404                                     = 5
	SubscribeStateChangeReasonOverStreamSubscribeLimit                            = 6
	SubscribeStateChangeReasonNoSubscribePermission                               = 7
)

type SubscribeMode = int

const (
	SubscribeModeAuto   SubscribeMode = 0
	SubscribeModeManual               = 1
)

type RemoteUserPriority = int

const (
	RemoteUserPriorityLow    RemoteUserPriority = 0
	RemoteUserPriorityMedium                    = 100
	RemoteUserPriorityHigh                      = 200
)

type PublishFallbackOption = int

const (
	PublishFallbackOptionDisabled  PublishFallbackOption = 0
	PublishFallbackOptionSimulcast                       = 1
)

type SubscribeFallbackOption = int

const (
	SubscribeFallbackOptionDisable        SubscribeFallbackOption = 0
	SubscribeFallbackOptionVideoStreamLow                         = 1
	SubscribeFallbackOptionAudioOnly                              = 2
)

type PerformanceAlarmReason = int

const (
	PerformanceAlarmReasonBandwidthFallbacked   PerformanceAlarmReason = 0
	PerformanceAlarmReasonBandwidthResumed                             = 1
	PerformanceAlarmReasonPerformanceFallbacked                        = 2
	PerformanceAlarmReasonPerformanceResumed                           = 3
)

type PerformanceAlarmMode = int

const (
	PerformanceAlarmModeNormal    PerformanceAlarmMode = 0
	PerformanceAlarmModeSimulcast                      = 1
)

type RoomState = int

const (
	RoomStateJoinSuccess RoomState = 0
	RoomStateJoinFailed            = 1
	RoomStateLeft                  = 2
)

type RoomStateChangeReason = int

const (
	RoomStateChangeReasonJoinRoom                      RoomStateChangeReason = 0
	RoomStateChangeReasonReconnect                                           = 1
	RoomStateChangeReasonLeaveRoom                                           = 2
	RoomStateChangeReasonJoinRoomFailed                                      = -2001
	RoomStateChangeReasonInvalidToken                                        = -1000
	RoomStateChangeReasonTokenExpired                                        = -1009
	RoomStateChangeReasonUpdateTokenWithInvalidToken                         = -1010
	RoomStateChangeReasonRoomForbidden                                       = -1025
	RoomStateChangeReasonUserForbidden                                       = -1026
	RoomStateChangeReasonKickedOut                                           = -1006
	RoomStateChangeReasonRoomDismiss                                         = -1011
	RoomStateChangeReasonDuplicateLogin                                      = -1004
	RoomStateChangeReasonWithoutLicenseAuthenticateSDK                       = -1012
	RoomStateChangeReasonServerLicenseExpired                                = -1017
	RoomStateChangeReasonExceedsTheUpperLimit                                = -1018
	RoomStateChangeReasonLicenseParameterError                               = -1019
	RoomStateChangeReasonLicenseFilePathError                                = -1020
	RoomStateChangeReasonLicenseIllegal                                      = -1021
	RoomStateChangeReasonLicenseExpired                                      = -1022
	RoomStateChangeReasonLicenseInformationNotMatch                          = -1023
	RoomStateChangeReasonLicenseNotMatchWithCache                            = -1024
	RoomStateChangeReasonLicenseFunctionNotFound                             = -1027
	RoomStateChangeReasonStateAbnormalServerStatus                           = -1084
	RoomStateChangeReasonUnknown                                             = -1001
)

type RoomEvent = int

const (
	RoomEventUserNotifyStop     RoomEvent = -2013
	RoomEventForbidden                    = -2012
	RoomEventRetryOnServerError           = -2014
)

type ErrorCode = int

const (
	ErrorCodeInvalidToken                          ErrorCode = -1000
	ErrorCodeJoinRoom                                        = -1001
	ErrorCodeNoPublishPermission                             = -1002
	ErrorCodeNoSubscribePermission                           = -1003
	ErrorCodeDuplicateLogin                                  = -1004
	ErrorCodeKickedOut                                       = -1006
	ErrorCodeRoomErrorTokenExpired                           = -1009
	ErrorCodeRoomErrorUpdateTokenWithInvalidToken            = -1010
	ErrorCodeRoomDismiss                                     = -1011
	ErrorCodeJoinRoomWithoutLicenseAuthenticateSDK           = -1012
	ErrorCodeRoomAlreadyExist                                = -1013
	ErrorCodeUserIDDifferent                                 = -1014
	ErrorCodeJoinRoomServerLicenseExpired                    = -1017
	ErrorCodeJoinRoomExceedsTheUpperLimit                    = -1018
	ErrorCodeJoinRoomLicenseParameterError                   = -1019
	ErrorCodeJoinRoomLicenseFilePathError                    = -1020
	ErrorCodeJoinRoomLicenseIllegal                          = -1021
	ErrorCodeJoinRoomLicenseExpired                          = -1022
	ErrorCodeJoinRoomLicenseInformationNotMatch              = -1023
	ErrorCodeJoinRoomLicenseNotMatchWithCache                = -1024
	ErrorCodeJoinRoomRoomForbidden                           = -1025
	ErrorCodeJoinRoomUserForbidden                           = -1026
	ErrorCodeJoinRoomLicenseFunctionNotFound                 = -1027
	ErrorCodeOverStreamSubscribeLimit                        = -1070
	ErrorCodeOverStreamPublishLimit                          = -1080
	ErrorCodeOverVideoPublishLimit                           = -1082
	ErrorCodeInvalidAudioSyncUidRepeated                     = -1083
	ErrorCodeAbnormalServerStatus                            = -1084
	ErrorCodeMultiRoomUnpublishFailed                        = -1085
	ErrorCodeWrongAreaCode                                   = -1086
	ErrorCodeInternalDeadLockNotify                          = -1111
)

type WarningCode = int

const (
	WarningCodeJoinRoomFailed                  WarningCode = -2001
	WarningCodePublishStreamFailed                         = -2002
	WarningCodeSubscribeStreamFailed404                    = -2003
	WarningCodeSubscribeStreamFailed5xx                    = -2004
	WarningCodeInvokeError                                 = -2005
	WarningCodeInvalidExpectMediaServerAddress             = -2007
	WarningCodePublishStreamForbidden                      = -2009
	WarningCodeSendCustomMessage                           = -2011
	WarningCodeUserNotifyStop                              = -2013
	WarningCodeUserInPublish                               = -2014
	WarningCodeInEchoTestMode                              = -2017
	WarningCodeNoCameraPermission                          = -5001
	WarningCodeNoMicrophonePermission                      = -5002
	WarningCodeRecodingDeviceStartFailed                   = -5003
	WarningCodePlayoutDeviceStartFailed                    = -5004
	WarningCodeNoRecordingDevice                           = -5005
	WarningCodeNoPlayoutDevice                             = -5006
	WarningCodeRecordingSilence                            = -5007
	WarningCodeMediaDeviceOperationDenied                  = -5008
	WarningCodeSetScreenAudioSourceTypeFailed              = -5009
	WarningCodeSetScreenAudioStreamIndexFailed             = -5010
	WarningCodeInvalidVoicePitch                           = -5011
	WarningCodeInvalidAudioFormat                          = -5012
	WarningCodeInvalidCallForExtAudio                      = -5013
	WarningCodeInvalidRemoteStreamKey                      = -5014
	WarningCodeInvalidCanvasHandle                         = -6001
	WarningCodeLicenseFileExpired                          = -7001
	WarningCodeInvaildSamiAppkeyORToken                    = -7002
	WarningCodeInvaildSamiResourcePath                     = -7003
	WarningCodeLoadSamiLibraryFailed                       = -7004
	WarningCodeInvaildSamiEffectType                       = -7005
)

type LocalAudioStreamState = int

const (
	LocalAudioStreamStateStopped   LocalAudioStreamState = 0
	LocalAudioStreamStateRecording                       = 1
	LocalAudioStreamStateEncoding                        = 2
	LocalAudioStreamStateFailed                          = 3
	LocalAudioStreamStateMute                            = 4
	LocalAudioStreamStateUnmute                          = 5
)

type LocalAudioStreamError = int

const (
	LocalAudioStreamErrorOk                 LocalAudioStreamError = 0
	LocalAudioStreamErrorFailure                                  = 1
	LocalAudioStreamErrorDeviceNoPermission                       = 2
	LocalAudioStreamErrorDeviceBusy                               = 3
	LocalAudioStreamErrorRecordFailure                            = 4
	LocalAudioStreamErrorEncodeFailure                            = 5
	LocalAudioStreamErrorNoRecordingDevice                        = 6
)

type LocalVideoStreamState = int

const (
	LocalVideoStreamStateStopped   LocalVideoStreamState = 0
	LocalVideoStreamStateRecording                       = 1
	LocalVideoStreamStateEncoding                        = 2
	LocalVideoStreamStateFailed                          = 3
)

type LocalVideoStreamError = int

const (
	LocalVideoStreamErrorOk                 LocalVideoStreamError = 0
	LocalVideoStreamErrorFailure                                  = 1
	LocalVideoStreamErrorDeviceNoPermission                       = 2
	LocalVideoStreamErrorDeviceBusy                               = 3
	LocalVideoStreamErrorDeviceNotFound                           = 4
	LocalVideoStreamErrorCaptureFailure                           = 5
	LocalVideoStreamErrorEncodeFailure                            = 6
	LocalVideoStreamErrorDeviceDisconnected                       = 7
)

type RemoteAudioState = int

const (
	RemoteAudioStateStopped  RemoteAudioState = 0
	RemoteAudioStateStarting                  = 1
	RemoteAudioStateDecoding                  = 2
	RemoteAudioStateFrozen                    = 3
	RemoteAudioStateFailed                    = 4
)

type RemoteAudioStateChangeReason = int

const (
	RemoteAudioStateChangeReasonInternal          RemoteAudioStateChangeReason = 0
	RemoteAudioStateChangeReasonNetworkCongestion                              = 1
	RemoteAudioStateChangeReasonNetworkRecovery                                = 2
	RemoteAudioStateChangeReasonLocalMuted                                     = 3
	RemoteAudioStateChangeReasonLocalUnmuted                                   = 4
	RemoteAudioStateChangeReasonRemoteMuted                                    = 5
	RemoteAudioStateChangeReasonRemoteUnmuted                                  = 6
	RemoteAudioStateChangeReasonRemoteOffline                                  = 7
)

type RemoteVideoState = int

const (
	RemoteVideoStateStopped  RemoteVideoState = 0
	RemoteVideoStateStarting                  = 1
	RemoteVideoStateDecoding                  = 2
	RemoteVideoStateFrozen                    = 3
	RemoteVideoStateFailed                    = 4
)

type RemoteVideoStateChangeReason = int

const (
	RemoteVideoStateChangeReasonInternal          RemoteVideoStateChangeReason = 0
	RemoteVideoStateChangeReasonNetworkCongestion                              = 1
	RemoteVideoStateChangeReasonNetworkRecovery                                = 2
	RemoteVideoStateChangeReasonLocalMuted                                     = 3
	RemoteVideoStateChangeReasonLocalUnmuted                                   = 4
	RemoteVideoStateChangeReasonRemoteMuted                                    = 5
	RemoteVideoStateChangeReasonRemoteUnmuted                                  = 6
	RemoteVideoStateChangeReasonRemoteOffline                                  = 7
)

type SEIStreamEventType = int

const (
	SEIStreamEventTypeStreamAdd    SEIStreamEventType = 0
	SEIStreamEventTypeStreamRemove                    = 1
)

type VideoSuperResolutionMode = int

const (
	VideoSuperResolutionModeOff VideoSuperResolutionMode = 0
	VideoSuperResolutionModeOn                           = 1
)

type VideoDenoiseMode = int

const (
	VideoDenoiseModeOff  VideoDenoiseMode = 0
	VideoDenoiseModeAuto                  = 1
)

type ProblemFeedbackOption = int

const (
	ProblemFeedbackOptionNone                ProblemFeedbackOption = 0
	ProblemFeedbackOptionOtherMessage                              = 1
	ProblemFeedbackOptionDisconnected                              = 2
	ProblemFeedbackOptionEarBackDelay                              = 4
	ProblemFeedbackOptionLocalNoise                                = 1024
	ProblemFeedbackOptionLocalAudioLagging                         = 2048
	ProblemFeedbackOptionLocalNoAudio                              = 4096
	ProblemFeedbackOptionLocalAudioStrength                        = 8192
	ProblemFeedbackOptionLocalEcho                                 = 16384
	ProblemFeedbackOptionLocalVideoFuzzy                           = 16777216
	ProblemFeedbackOptionLocalNotSync                              = 33554432
	ProblemFeedbackOptionLocalVideoLagging                         = 67108864
	ProblemFeedbackOptionLocalNoVideo                              = 134217728
	ProblemFeedbackOptionRemoteNoise                               = 137438953472
	ProblemFeedbackOptionRemoteAudioLagging                        = 274877906944
	ProblemFeedbackOptionRemoteNoAudio                             = 549755813888
	ProblemFeedbackOptionRemoteAudioStrength                       = 1099511627776
	ProblemFeedbackOptionRemoteEcho                                = 2199023255552
	ProblemFeedbackOptionRemoteVideoFuzzy                          = 2251799813685248
	ProblemFeedbackOptionRemoteNotSync                             = 4503599627370496
	ProblemFeedbackOptionRemoteVideoLagging                        = 9007199254740992
	ProblemFeedbackOptionRemoteNoVideo                             = 18014398509481984
)

type EncryptType = int

const (
	EncryptTypeNone      EncryptType = 0
	EncryptTypeAES128CBC             = 1
	EncryptTypeAES256CBC             = 2
	EncryptTypeAES128ECB             = 3
	EncryptTypeAES256ECB             = 4
)

type FirstFrameSendState = int

const (
	FirstFrameSendStateSending FirstFrameSendState = 0
	FirstFrameSendStateSent                        = 1
	FirstFrameSendStateEnd                         = 2
)

type FirstFramePlayState = int

const (
	FirstFramePlayStatePlaying FirstFramePlayState = 0
	FirstFramePlayStatePlayed                      = 1
	FirstFramePlayStateEnd                         = 2
)

type MuteState = int

const (
	MuteStateOff MuteState = 0
	MuteStateOn            = 1
)

type AggregationOption = int

const (
	AggregationOptionMin      AggregationOption = 0
	AggregationOptionMax                        = 1
	AggregationOptionMajority                   = 2
)

type RecordingFileType = int

const (
	RecordingFileTypeAAC RecordingFileType = 0
	RecordingFileTypeMP4                   = 1
)

type RecordingState = int

const (
	RecordingStateError      RecordingState = 0
	RecordingStateProcessing                = 1
	RecordingStateSuccess                   = 2
)

type RecordingErrorCode = int

const (
	RecordingErrorCodeOk           RecordingErrorCode = 0
	RecordingErrorCodeNoPermission                    = -1
	RecordingErrorCodeNotSupport                      = -2
	RecordingErrorCodeOther                           = -3
)

type UserWorkerType = int

const (
	UserWorkerTypeNormal                  UserWorkerType = 0
	UserWorkerTypeSupportSip                             = 1
	UserWorkerTypeByteVc1Transcoder                      = 2
	UserWorkerTypeNeedUserListAndCb                      = 4
	UserWorkerTypeNeedStreamCallBack                     = 8
	UserWorkerTypeAudioSelectionExemption                = 16
)

type AVSyncState = int

const (
	AVSyncStateAVStreamSyncBegin AVSyncState = 0
	AVSyncStateAudioStreamRemove             = 1
	AVSyncStateVdieoStreamRemove             = 2
	AVSyncStateSetAVSyncStreamId             = 3
)

type RtcRoomMode = int

const (
	RtcRoomModeNormalMode         RtcRoomMode = 0
	RtcRoomModeAudioSelectionMode             = 1
)

type SyncInfoStreamType = int

const (
	SyncInfoStreamTypeAudio SyncInfoStreamType = 0
)

type ForwardStreamState = int

const (
	ForwardStreamStateIdle    ForwardStreamState = 0
	ForwardStreamStateSuccess                    = 1
	ForwardStreamStateFailure                    = 2
)

type ForwardStreamError = int

const (
	ForwardStreamErrorOK                ForwardStreamError = 0
	ForwardStreamErrorInvalidArgument                      = 1201
	ForwardStreamErrorInvalidToken                         = 1202
	ForwardStreamErrorResponse                             = 1203
	ForwardStreamErrorRemoteKickedicked                    = 1204
	ForwardStreamErrorNotSupport                           = 1205
)

type ForwardStreamEvent = int

const (
	ForwardStreamEventDisconnected    ForwardStreamEvent = 0
	ForwardStreamEventConnected                          = 1
	ForwardStreamEventInterrupt                          = 2
	ForwardStreamEventDstRoomUpdated                     = 3
	ForwardStreamEventUnExpectAPICall                    = 4
)

type DeviceTransportType = int

const (
	DeviceTransportTypeUnknown                DeviceTransportType = 0
	DeviceTransportTypeBuiltIn                                    = 1
	DeviceTransportTypeBlueToothUnknownMode                       = 2
	DeviceTransportTypeBlueToothHandsFreeMode                     = 3
	DeviceTransportTypeBlueToothStereoMode                        = 4
	DeviceTransportTypeDisplayAudio                               = 5
	DeviceTransportTypeVirtual                                    = 6
	DeviceTransportTypeUSB                                        = 7
	DeviceTransportTypePCI                                        = 8
	DeviceTransportTypeAirPlay                                    = 9
	DeviceTransportTypeContinuityCamera                           = 100
)

type EchoTestResult = int

const (
	EchoTestResultTestSuccess       EchoTestResult = 0
	EchoTestResultTestTimeout                      = 1
	EchoTestResultTestIntervalShort                = 2
	EchoTestResultAudioDeviceError                 = 3
	EchoTestResultVideoDeviceError                 = 4
	EchoTestResultAudioReceiveError                = 5
	EchoTestResultVideoReceiveError                = 6
	EchoTestResultInternalError                    = 7
)

type AudioDumpStatus = int

const (
	AudioDumpStatusStartFailure   AudioDumpStatus = 0
	AudioDumpStatusStartSuccess                   = 1
	AudioDumpStatusStopFailure                    = 2
	AudioDumpStatusStopSuccess                    = 3
	AudioDumpStatusRunningFailure                 = 4
	AudioDumpStatusRunningSuccess                 = 5
)

type NetworkDetectionStartReturn = int

const (
	NetworkDetectionStartReturnSuccess    NetworkDetectionStartReturn = 0
	NetworkDetectionStartReturnParamErr                               = 1
	NetworkDetectionStartReturnStreaming                              = 2
	NetworkDetectionStartReturnStarted                                = 3
	NetworkDetectionStartReturnNotSupport                             = 4
)

type NetworkDetectionStopReason = int

const (
	NetworkDetectionStopReasonUser           NetworkDetectionStopReason = 0
	NetworkDetectionStopReasonTimeout                                   = 1
	NetworkDetectionStopReasonConnectionLost                            = 2
	NetworkDetectionStopReasonStreaming                                 = 3
	NetworkDetectionStopReasonInnerErr                                  = 4
)

type NetworkDetectionLinkType = int

const (
	NetworkDetectionLinkTypeUp   NetworkDetectionLinkType = 0
	NetworkDetectionLinkTypeDown                          = 1
)

type HardwareEchoDetectionResult = int

const (
	HardwareEchoDetectionResultCanceled HardwareEchoDetectionResult = 0
	HardwareEchoDetectionResultUnknown                              = 1
	HardwareEchoDetectionResultNormal                               = 2
	HardwareEchoDetectionResultPoor                                 = 3
)

type AudioSelectionPriority = int

const (
	AudioSelectionPriorityNormal AudioSelectionPriority = 0
	AudioSelectionPriorityHigh                          = 1
)

type SetRoomExtraInfoResult = int

const (
	SetRoomExtraInfoResultSuccess           SetRoomExtraInfoResult = 0
	SetRoomExtraInfoResultErrorNotJoinRoom                         = -1
	SetRoomExtraInfoResultErrorKeyIsNull                           = -2
	SetRoomExtraInfoResultErrorValueIsNull                         = -3
	SetRoomExtraInfoResultResultUnknow                             = -99
	SetRoomExtraInfoResultErrorKeyIsEmpty                          = -400
	SetRoomExtraInfoResultErrorTooOften                            = -406
	SetRoomExtraInfoResultErrorSilentUser                          = -412
	SetRoomExtraInfoResultErrorKeyTooLong                          = -413
	SetRoomExtraInfoResultErrorValueTooLong                        = -414
	SetRoomExtraInfoResultErrorServer                              = -500
)

type SubtitleState = int

const (
	SubtitleStateStarted SubtitleState = 0
	SubtitleStateStoped                = 1
	SubtitleStateError                 = 2
)

type SubtitleMode = int

const (
	SubtitleModeRecognition SubtitleMode = 0
	SubtitleModeTranslation              = 1
)

type SubtitleErrorCode = int

const (
	SubtitleErrorCodeUnknow              SubtitleErrorCode = -1
	SubtitleErrorCodeSuccess                               = 0
	SubtitleErrorCodePostProcessError                      = 1
	SubtitleErrorCodeASRConnectionError                    = 2
	SubtitleErrorCodeASRServiceError                       = 3
	SubtitleErrorCodeBeforeJoinRoom                        = 4
	SubtitleErrorCodeAlreadyOn                             = 5
	SubtitleErrorCodeUnsupportedLanguage                   = 6
	SubtitleErrorCodePostProcessTimeout                    = 7
)

type EffectErrorType = int

const (
	EffectErrorTypeVirtualBackgroundFailure EffectErrorType = 1
	EffectErrorTypeChildProcTerminate                       = 2
)

type RenderError = int

const (
	RenderErrorOk                   RenderError = 0
	RenderErrorUsingInternalSurface             = -1
	RenderErrorUsingSoftwareDecoder             = -2
)

type AVSyncEvent = int

const (
	AVSyncEventInvalidUidRepeated AVSyncEvent = 0
)

type AudioVADType = int

const (
	AudioVADTypeNoSpeech AudioVADType = 0
	AudioVADTypeSpeech                = 1
)

type AudioAEDType = int

const (
	AudioAEDTypeNoMusic AudioAEDType = 0
	AudioAEDTypeMusic                = 1
)

type MediaPlayerCustomSourceMode = int

const (
	MediaPlayerCustomSourceModePush MediaPlayerCustomSourceMode = 0
	MediaPlayerCustomSourceModePull                             = 1
)

type MediaPlayerCustomSourceSeekWhence = int

const (
	MediaPlayerCustomSourceSeekWhenceSet  MediaPlayerCustomSourceSeekWhence = 0
	MediaPlayerCustomSourceSeekWhenceCur                                    = 1
	MediaPlayerCustomSourceSeekWhenceEnd                                    = 2
	MediaPlayerCustomSourceSeekWhenceSize                                   = 3
)

type MediaPlayerCustomSourceStreamType = int

const (
	MediaPlayerCustomSourceStreamTypeRaw     MediaPlayerCustomSourceStreamType = 0
	MediaPlayerCustomSourceStreamTypeEncoded                                   = 1
)

type AttenuationType = int

const (
	AttenuationTypeNone        AttenuationType = 0
	AttenuationTypeLinear                      = 1
	AttenuationTypeExponential                 = 2
)

type RtsReturnStatus = int

const (
	RtsReturnStatusSuccess      RtsReturnStatus = 0
	RtsReturnStatusFailure                      = -1
	RtsReturnStatusParameterErr                 = -2
	RtsReturnStatusWrongState                   = -3
	RtsReturnStatusHasInRoom                    = -4
	RtsReturnStatusHasInLogin                   = -5
	RtsReturnStatusRoomIdInUse                  = -8
)

type UserOfflineReason = int

const (
	UserOfflineReasonQuit              UserOfflineReason = 0
	UserOfflineReasonDropped                             = 1
	UserOfflineReasonSwitchToInvisible                   = 2
	UserOfflineReasonKickedByAdmin                       = 3
)

type JoinRoomType = int

const (
	JoinRoomTypeFirst       JoinRoomType = 0
	JoinRoomTypeReconnected              = 1
)

type LoginType = int

const (
	LoginTypeFirst       LoginType = 0
	LoginTypeReconnected           = 1
)

type LoginErrorCode = int

const (
	LoginErrorCodeSuccess       LoginErrorCode = 0
	LoginErrorCodeInvalidToken                 = -1000
	LoginErrorCodeLoginFailed                  = -1001
	LoginErrorCodeInvalidUserId                = -1002
	LoginErrorCodeServerError                  = -1003
)

type LogoutReason = int

const (
	LogoutReasonLogout         LogoutReason = 0
	LogoutReasonDuplicateLogin              = 1
)

type UserOnlineStatus = int

const (
	UserOnlineStatusOffline     UserOnlineStatus = 0
	UserOnlineStatusOnline                       = 1
	UserOnlineStatusUnreachable                  = 2
)

type MessageConfig = int

const (
	MessageConfigReliableOrdered     MessageConfig = 0
	MessageConfigUnreliableOrdered                 = 1
	MessageConfigUnreliableUnordered               = 2
)

type UserMessageSendResult = int

const (
	UserMessageSendResultSuccess             UserMessageSendResult = 0
	UserMessageSendResultTimeout                                   = 1
	UserMessageSendResultNetworkDisconnected                       = 2
	UserMessageSendResultNoReceiver                                = 3
	UserMessageSendResultNoRelayPath                               = 4
	UserMessageSendResultExceedQPS                                 = 5
	UserMessageSendResultE2BSSendFailed                            = 17
	UserMessageSendResultE2BSReturnFailed                          = 18
	UserMessageSendResultNotJoin                                   = 100
	UserMessageSendResultInit                                      = 101
	UserMessageSendResultNoConnection                              = 102
	UserMessageSendResultExceedMaxLength                           = 103
	UserMessageSendResultEmptyUser                                 = 104
	UserMessageSendResultNotLogin                                  = 105
	UserMessageSendResultServerParamsNotSet                        = 106
	UserMessageSendResultUnknown                                   = 1000
)

type RoomMessageSendResult = int

const (
	RoomMessageSendResultSuccess             RoomMessageSendResult = 200
	RoomMessageSendResultTimeout                                   = 1
	RoomMessageSendResultNetworkDisconnected                       = 2
	RoomMessageSendResultExceedQPS                                 = 5
	RoomMessageSendResultNotJoin                                   = 100
	RoomMessageSendResultNoConnection                              = 102
	RoomMessageSendResultExceedMaxLength                           = 103
	RoomMessageSendResultUnknown                                   = 1000
)

type ConnectionState = int

const (
	ConnectionStateDisconnected ConnectionState = 1
	ConnectionStateConnecting                   = 2
	ConnectionStateConnected                    = 3
	ConnectionStateReconnecting                 = 4
	ConnectionStateReconnected                  = 5
	ConnectionStateLost                         = 6
	ConnectionStateFailed                       = 7
)

type NetworkQuality = int

const (
	NetworkQualityUnknown   NetworkQuality = 0
	NetworkQualityExcellent                = 1
	NetworkQualityGood                     = 2
	NetworkQualityPoor                     = 3
	NetworkQualityBad                      = 4
	NetworkQualityVbad                     = 5
	NetworkQualityDown                     = 6
)

type NetworkType = int

const (
	NetworkTypeUnknown      NetworkType = -1
	NetworkTypeDisconnected             = 0
	NetworkTypeLAN                      = 1
	NetworkTypeWIFI                     = 2
	NetworkTypeMobile2G                 = 3
	NetworkTypeMobile3G                 = 4
	NetworkTypeMobile4G                 = 5
	NetworkTypeMobile5G                 = 6
)

type RtsErrorCode = int

const (
	RtsErrorCodeInvalidToken                          RtsErrorCode = -1000
	RtsErrorCodeJoinRoom                                           = -1001
	RtsErrorCodeDuplicateLogin                                     = -1004
	RtsErrorCodeKickedOut                                          = -1006
	RtsErrorCodeRoomErrorRoomIdIllegal                             = -1007
	RtsErrorCodeRoomErrorTokenExpired                              = -1009
	RtsErrorCodeRoomErrorUpdateTokenWithInvalidToken               = -1010
	RtsErrorCodeRoomDismiss                                        = -1011
	RtsErrorCodeRoomAlreadyExist                                   = -1013
	RtsErrorCodeUserIDDifferent                                    = -1014
	RtsErrorCodeAbnormalServerStatus                               = -1084
	RtsErrorCodeInternalDeadLockNotify                             = -1111
	RtsErrorCodeJoinRoomWithoutLicenseAuthenticateSDK              = -1012
)

type RtsWarningCode = int

const (
	RtsWarningCodeJoinRoomFailed                  RtsWarningCode = -2001
	RtsWarningCodePublishStreamFailed                            = -2002
	RtsWarningCodeInvokeError                                    = -2005
	RtsWarningCodeInvalidExpectMediaServerAddress                = -2007
	RtsWarningCodeSendCustomMessage                              = -2011
	RtsWarningCodeUserNotifyStop                                 = -2013
	RtsWarningCodeOldRoomBeenReplaced                            = -2016
)

type BusinessCheckCode = int

const (
	BusinessCheckCodeAlreadyInRoom   BusinessCheckCode = -6001
	BusinessCheckCodeInputInvalidate                   = -6002
)

type HttpProxyState = int

const (
	HttpProxyStateInit      HttpProxyState = 0
	HttpProxyStateConnected                = 1
	HttpProxyStateError                    = 2
)

type Socks5ProxyState = int

const (
	Socks5ProxyStateInit            Socks5ProxyState = 0
	Socks5ProxyStateConnected                        = 1
	Socks5ProxyStateError                            = 2
	Socks5ProxyStateTcpConnectFail                   = 3
	Socks5ProxyStateTcpClose                         = 4
	Socks5ProxyStateProtocolTcpFail                  = 5
	Socks5ProxyStateProtocolUdpFail                  = 6
	Socks5ProxyStateAuthFail                         = 7
	Socks5ProxyStateUnknown                          = 8
)

type LocalProxyType = int

const (
	LocalProxyTypeSocks5     LocalProxyType = 1
	LocalProxyTypeHttpTunnel                = 2
)

type LocalProxyState = int

const (
	LocalProxyStateInited    LocalProxyState = 0
	LocalProxyStateConnected                 = 1
	LocalProxyStateError                     = 2
)

type LocalProxyError = int

const (
	LocalProxyErrorOK                     LocalProxyError = 0
	LocalProxyErrorSocks5VersionError                     = 1
	LocalProxyErrorSocks5FormatError                      = 2
	LocalProxyErrorSocks5InvalidValue                     = 3
	LocalProxyErrorSocks5UserPassNotGiven                 = 4
	LocalProxyErrorSocks5TcpClosed                        = 5
	LocalProxyErrorHttpTunnelFailed                       = 6
)

type LocalLogLevel = int

const (
	LocalLogLevelInfo    LocalLogLevel = 0
	LocalLogLevelWarning               = 1
	LocalLogLevelError                 = 2
	LocalLogLevelNone                  = 3
)

type MixedStreamTaskEvent = int

const (
	MixedStreamTaskEventBase          MixedStreamTaskEvent = 0
	MixedStreamTaskEventStartSuccess                       = 1
	MixedStreamTaskEventStartFailed                        = 2
	MixedStreamTaskEventUpdateSuccess                      = 3
	MixedStreamTaskEventUpdateFailed                       = 4
	MixedStreamTaskEventStopSuccess                        = 5
	MixedStreamTaskEventStopFailed                         = 6
	MixedStreamTaskEventWarning                            = 7
)

type SingleStreamTaskEvent = int

const (
	SingleStreamTaskEventBase         SingleStreamTaskEvent = 0
	SingleStreamTaskEventStartSuccess                       = 1
	SingleStreamTaskEventStartFailed                        = 2
	SingleStreamTaskEventStopSuccess                        = 3
	SingleStreamTaskEventStopFailed                         = 4
	SingleStreamTaskEventWarning                            = 5
)

type MixedStreamTaskErrorCode = int

const (
	MixedStreamTaskErrorCodeOK                   MixedStreamTaskErrorCode = 0
	MixedStreamTaskErrorCodeBase                                          = 1090
	MixedStreamTaskErrorCodeTimeout                                       = 1091
	MixedStreamTaskErrorCodeInvalidParamByServer                          = 1092
	MixedStreamTaskErrorCodeSubTimeoutByServer                            = 1093
	MixedStreamTaskErrorCodeInvalidStateByServer                          = 1094
	MixedStreamTaskErrorCodeAuthenticationByCDN                           = 1095
	MixedStreamTaskErrorCodeUnKnownByServer                               = 1096
	MixedStreamTaskErrorCodeSignalRequestTimeout                          = 1097
	MixedStreamTaskErrorCodeMixImageFailed                                = 1098
	MixedStreamTaskErrorCodeStreamSyncWorse                               = 1099
	MixedStreamTaskErrorCodePushWTNFailed                                 = 1195
	MixedStreamTaskErrorCodeMax                                           = 1199
)

type SingleStreamTaskErrorCode = int

const (
	SingleStreamTaskErrorCodeOK                   SingleStreamTaskErrorCode = 0
	SingleStreamTaskErrorCodeBase                                           = 1090
	SingleStreamTaskErrorCodeUnKnownByServer                                = 1091
	SingleStreamTaskErrorCodeSignalRequestTimeout                           = 1092
)

type MixedStreamType = int

const (
	MixedStreamTypeByServer MixedStreamType = 0
	MixedStreamTypeByClient                 = 1
)

type MixedStreamPushTargetType = int

const (
	MixedStreamPushTargetTypeToCDN MixedStreamPushTargetType = 0
	MixedStreamPushTargetTypeToWTN                           = 1
)

type MixedStreamAudioProfile = int

const (
	MixedStreamAudioProfileLC   MixedStreamAudioProfile = 0
	MixedStreamAudioProfileHEv1                         = 1
	MixedStreamAudioProfileHEv2                         = 2
)

type MixedStreamSyncStrategy = int

const (
	MixedStreamSyncStrategyNoSync           MixedStreamSyncStrategy = 0
	MixedStreamSyncStrategyAudioPreciseSync                         = 1
	MixedStreamSyncStrategySimplexModeSync                          = 2
)

type MixedStreamAudioCodecType = int

const (
	MixedStreamAudioCodecTypeAAC MixedStreamAudioCodecType = 0
)

type MixedStreamSEIContentMode = int

const (
	MixedStreamSEIContentModeDefault                MixedStreamSEIContentMode = 0
	MixedStreamSEIContentModeEnableVolumeIndication                           = 1
)

type MixedStreamVideoCodecType = int

const (
	MixedStreamVideoCodecTypeH264    MixedStreamVideoCodecType = 0
	MixedStreamVideoCodecTypeByteVC1                           = 1
)

type MixedStreamRenderMode = int

const (
	MixedStreamRenderModeHidden   MixedStreamRenderMode = 1
	MixedStreamRenderModeFit                            = 2
	MixedStreamRenderModeAdaptive                       = 3
)

type MixedStreamMediaType = int

const (
	MixedStreamMediaTypeAudioAndVideo MixedStreamMediaType = 0
	MixedStreamMediaTypeAudioOnly                          = 1
	MixedStreamMediaTypeVideoOnly                          = 2
)

type MixedStreamLayoutRegionType = int

const (
	MixedStreamLayoutRegionTypeVideoStream MixedStreamLayoutRegionType = 0
	MixedStreamLayoutRegionTypeImage                                   = 1
)

type MixedStreamClientMixVideoFormat = int

const (
	MixedStreamClientMixVideoFormatI420              MixedStreamClientMixVideoFormat = 0
	MixedStreamClientMixVideoFormatTexture2D                                         = 1
	MixedStreamClientMixVideoFormatCVPixelBufferBGRA                                 = 2
	MixedStreamClientMixVideoFormatNV12                                              = 3
	MixedStreamClientMixVideoFormatD3D11Texture2D                                    = 4
)

type MixedStreamPushMode = int

const (
	MixedStreamPushModeOnStream       MixedStreamPushMode = 0
	MixedStreamPushModeOnStartRequest                     = 1
)

type MixedStreamAlternateImageFillMode = int

const (
	MixedStreamAlternateImageFillModeFit  MixedStreamAlternateImageFillMode = 0
	MixedStreamAlternateImageFillModeFill                                   = 1
)

type MixedStreamVideoType = int

const (
	MixedStreamVideoTypeMain   MixedStreamVideoType = 0
	MixedStreamVideoTypeScreen                      = 1
)

type StreamLayoutMode = int

const (
	StreamLayoutModeAuto   StreamLayoutMode = 0
	StreamLayoutModeCustom                  = 2
)

type InterpolationMode = int

const (
	InterpolationModeLastFrameFill       InterpolationMode = 0
	InterpolationModeBackgroundImageFill                   = 1
)

type DataFrameType = int

const (
	DataFrameTypeSei DataFrameType = 0
)

type TranscoderRoomStatus = int

const (
	TranscoderRoomStatusJoinRoom  TranscoderRoomStatus = 0
	TranscoderRoomStatusLeaveRoom                      = 1
	TranscoderRoomStatusOffline                        = 2
	TranscoderRoomStatusOnline                         = 3
)

type ChorusCacheSyncMode = int

const (
	ChorusCacheSyncModeProducer      ChorusCacheSyncMode = 0
	ChorusCacheSyncModeRetransmitter                     = 1
	ChorusCacheSyncModeConsumer                          = 2
)

type ChorusCacheSyncEvent = int

const (
	ChorusCacheSyncEventStartSuccess ChorusCacheSyncEvent = 0
	ChorusCacheSyncEventStartFailed                       = 1
)

type ChorusCacheSyncError = int

const (
	ChorusCacheSyncErrorOK             ChorusCacheSyncError = 0
	ChorusCacheSyncErrorWrongState                          = 1
	ChorusCacheSyncErrorAlreadyRunning                      = 2
)

type SimulcastStreamType = int

const (
	SimulcastStreamTypeWeak SimulcastStreamType = 0
	SimulcastStreamTypeLow                      = 1
	SimulcastStreamTypeMid                      = 2
	SimulcastStreamTypeHigh                     = 3
)

type VideoSimulcastMode = int

const (
	VideoSimulcastModeOnlyOne         VideoSimulcastMode = 0
	VideoSimulcastModeOnDemand                           = 1
	VideoSimulcastModeAlwaysSimulcast                    = 2
)

type RenderMode = int

const (
	RenderModeHidden RenderMode = 1
	RenderModeFit               = 2
	RenderModeFill              = 3
)

type SubscribeMediaType = int

const (
	SubscribeMediaTypeNone          SubscribeMediaType = 0
	SubscribeMediaTypeAudioOnly                        = 1
	SubscribeMediaTypeVideoOnly                        = 2
	SubscribeMediaTypeVideoAndAudio                    = 3
)

type PauseResumeControlMediaType = int

const (
	PauseResumeControlMediaTypeAudio         PauseResumeControlMediaType = 0
	PauseResumeControlMediaTypeVideo                                     = 1
	PauseResumeControlMediaTypeVideoAndAudio                             = 2
)

type SVCLayer = int

const (
	SVCLayerDefault SVCLayer = 0
	SVCLayerBase             = 1
	SVCLayerMain             = 2
	SVCLayerHigh             = 3
)

type BackgroundMode = int

const (
	BackgroundModeNone BackgroundMode = 0
	BackgroundModeBlur                = 1
	BackgroundModeA                   = 2
	BackgroundModeB                   = 3
)

type DivideMode = int

const (
	DivideModeNone   DivideMode = 0
	DivideModeEffect            = 1
)

type VideoStreamType = int

const (
	VideoStreamTypeHigh VideoStreamType = 0
	VideoStreamTypeLow                  = 1
)

type RenderTargetType = int

const (
	RenderTargetTypeView    RenderTargetType = 0
	RenderTargetTypeSurface                  = 1
)

type MouseCursorCaptureState = int

const (
	MouseCursorCaptureStateOn  MouseCursorCaptureState = 0
	MouseCursorCaptureStateOff                         = 1
)

type ContentHint = int

const (
	ContentHintDetails ContentHint = 0
	ContentHintMotion              = 1
)

type PixelFormat = int

const (
	PixelFormatI420     PixelFormat = 1
	PixelFormatRGBA                 = 5
	PixelFormatOriginal             = 0
)

type LocalVideoSinkPosition = int

const (
	LocalVideoSinkPositionAfterCapture    LocalVideoSinkPosition = 0
	LocalVideoSinkPositionAfterPreProcess                        = 1
)

type VideoApplyRotation = int

const (
	VideoApplyRotationDefault VideoApplyRotation = -1
	VideoApplyRotation0                          = 0
)

type VideoSinkMirrorType = int

const (
	VideoSinkMirrorTypeON  VideoSinkMirrorType = 1
	VideoSinkMirrorTypeOFF                     = 2
)

type RemoteVideoSinkPosition = int

const (
	RemoteVideoSinkPositionAfterDecoder     RemoteVideoSinkPosition = 0
	RemoteVideoSinkPositionAfterPostProcess                         = 1
)

type MirrorMode = int

const (
	MirrorModeOff MirrorMode = 0
	MirrorModeOn             = 1
)

type MirrorType = int

const (
	MirrorTypeNone             MirrorType = 0
	MirrorTypeRender                      = 1
	MirrorTypeRenderAndEncoder            = 3
)

type RemoteMirrorType = int

const (
	RemoteMirrorTypeNone   RemoteMirrorType = 0
	RemoteMirrorTypeRender                  = 1
)

type VideoEnhancementMode = int

const (
	VideoEnhancementModeDisabled VideoEnhancementMode = 0
	VideoEnhancementModeAuto                          = 1
)

type VideoSourceType = int

const (
	VideoSourceTypeExternal                    VideoSourceType = 0
	VideoSourceTypeInternal                                    = 1
	VideoSourceTypeEncodedWithAutoSimulcast                    = 2
	VideoSourceTypeEncodedWithoutAutoSimulcast                 = 3
)

type AlphaLayout = int

const (
	AlphaLayoutTop    AlphaLayout = 0
	AlphaLayoutBottom             = 1
	AlphaLayoutLeft               = 2
	AlphaLayoutRight              = 3
)

type ZoomConfigType = int

const (
	ZoomConfigTypeFocusOffset ZoomConfigType = 0
	ZoomConfigTypeMoveOffset                 = 1
)

type ZoomDirectionType = int

const (
	ZoomDirectionTypeMoveLeft  ZoomDirectionType = 0
	ZoomDirectionTypeMoveRight                   = 1
	ZoomDirectionTypeMoveUp                      = 2
	ZoomDirectionTypeMoveDown                    = 3
	ZoomDirectionTypeZoomOut                     = 4
	ZoomDirectionTypeZoomIn                      = 5
	ZoomDirectionTypeReset                       = 6
)

type VideoDecoderConfig = int

const (
	VideoDecoderConfigRaw    VideoDecoderConfig = 0
	VideoDecoderConfigEncode                    = 1
	VideoDecoderConfigBoth                      = 2
)

type CapturePreference = int

const (
	CapturePreferenceAuto            CapturePreference = 0
	CapturePreferenceManual                            = 1
	CapturePreferenceAutoPerformance                   = 2
)

type RecordingType = int

const (
	RecordingTypeAudioOnly     RecordingType = 0
	RecordingTypeVideoOnly                   = 1
	RecordingTypeVideoAndAudio               = 2
)

type RTCVideoDeviceType = int

const (
	RTCVideoDeviceTypeUnknown             RTCVideoDeviceType = -1
	RTCVideoDeviceTypeRenderDevice                           = 0
	RTCVideoDeviceTypeCaptureDevice                          = 1
	RTCVideoDeviceTypeScreenCaptureDevice                    = 2
)

type PublicStreamErrorCode = int

const (
	PublicStreamErrorCodeOK               PublicStreamErrorCode = 0
	PublicStreamErrorCodePullNoPushStream                       = 1300
)

type VideoRotationMode = int

const (
	VideoRotationModeFollowApp     VideoRotationMode = 0
	VideoRotationModeFollowGSensor                   = 1
)

type MediaStreamType = int

const (
	MediaStreamTypeAudio MediaStreamType = 1
	MediaStreamTypeVideo                 = 2
	MediaStreamTypeBoth                  = 3
)

type FrameRateRatio = int

const (
	FrameRateRatioOriginal FrameRateRatio = 0
	FrameRateRatioHalf                    = 1
	FrameRateRatioQuarter                 = 2
)

type ScreenMediaType = int

const (
	ScreenMediaTypeVideoOnly     ScreenMediaType = 0
	ScreenMediaTypeAudioOnly                     = 1
	ScreenMediaTypeVideoAndAudio                 = 2
)

type EffectBeautyMode = int

const (
	EffectBeautyModeWhite   EffectBeautyMode = 0
	EffectBeautyModeSmooth                   = 1
	EffectBeautyModeSharpen                  = 2
	EffectBeautyModeClear                    = 3
)

type VideoDeviceFacing = int

const (
	VideoDeviceFacingFront   VideoDeviceFacing = 0
	VideoDeviceFacingBack                      = 1
	VideoDeviceFacingUnknown                   = 2
)

type VideoOrientation = int

const (
	VideoOrientationAdaptive  VideoOrientation = 0
	VideoOrientationPortrait                   = 1
	VideoOrientationLandscape                  = 2
)

type VideoSuperResolutionModeChangedReason = int

const (
	VideoSuperResolutionModeChangedReasonAPIOff               VideoSuperResolutionModeChangedReason = 0
	VideoSuperResolutionModeChangedReasonAPIOn                                                      = 1
	VideoSuperResolutionModeChangedReasonResolutionExceed                                           = 2
	VideoSuperResolutionModeChangedReasonOverUse                                                    = 3
	VideoSuperResolutionModeChangedReasonDeviceNotSupport                                           = 4
	VideoSuperResolutionModeChangedReasonDynamicClose                                               = 5
	VideoSuperResolutionModeChangedReasonOtherSettingDisabled                                       = 6
	VideoSuperResolutionModeChangedReasonOtherSettingEnabled                                        = 7
	VideoSuperResolutionModeChangedReasonNoComponent                                                = 8
	VideoSuperResolutionModeChangedReasonStreamNotExist                                             = 9
)

type VideoDenoiseModeChangedReason = int

const (
	VideoDenoiseModeChangedReasonNull              VideoDenoiseModeChangedReason = -1
	VideoDenoiseModeChangedReasonApiOff                                          = 0
	VideoDenoiseModeChangedReasonApiOn                                           = 1
	VideoDenoiseModeChangedReasonConfigDisabled                                  = 2
	VideoDenoiseModeChangedReasonConfigEnabled                                   = 3
	VideoDenoiseModeChangedReasonInternalException                               = 4
	VideoDenoiseModeChangedReasonDynamicClose                                    = 5
	VideoDenoiseModeChangedReasonDynamicOpen                                     = 6
	VideoDenoiseModeChangedReasonResolution                                      = 7
)

type ScreenCaptureSourceType = int

const (
	ScreenCaptureSourceTypeUnknown ScreenCaptureSourceType = 0
	ScreenCaptureSourceTypeWindow                          = 1
	ScreenCaptureSourceTypeScreen                          = 2
)

type VideoContentCategory = int

const (
	VideoContentCategoryCamera VideoContentCategory = 0
	VideoContentCategoryScreen                      = 1
)

type StreamPriority = int

const (
	StreamPriorityLow    StreamPriority = 0
	StreamPriorityNormal                = 1
	StreamPriorityHigh                  = 2
)

type VirtualBackgroundSourceType = int

const (
	VirtualBackgroundSourceTypeColor VirtualBackgroundSourceType = 0
	VirtualBackgroundSourceTypeImage                             = 1
)

type VideoPictureType = int

const (
	VideoPictureTypeUnknown VideoPictureType = 0
	VideoPictureTypeI                        = 1
	VideoPictureTypeP                        = 2
	VideoPictureTypeB                        = 3
)

type VideoStreamScaleMode = int

const (
	VideoStreamScaleModeAuto            VideoStreamScaleMode = 0
	VideoStreamScaleModeStretch                              = 1
	VideoStreamScaleModeFitWithCropping                      = 2
	VideoStreamScaleModeFitWithFilling                       = 3
)

type VideoCodecMode = int

const (
	VideoCodecModeAuto     VideoCodecMode = 0
	VideoCodecModeHardware                = 1
	VideoCodecModeSoftware                = 2
)

type VideoEncodePreference = int

const (
	VideoEncodePreferenceDisabled  VideoEncodePreference = 0
	VideoEncodePreferenceFramerate                       = 1
	VideoEncodePreferenceQuality                         = 2
	VideoEncodePreferenceAuto                            = 3
)

type CameraID = int

const (
	CameraIDFront    CameraID = 0
	CameraIDBack              = 1
	CameraIDExternal          = 2
	CameraIDInvalid           = 3
)

type VideoPixelFormat = int

const (
	VideoPixelFormatUnknown    VideoPixelFormat = 0
	VideoPixelFormatI420                        = 1
	VideoPixelFormatNV12                        = 2
	VideoPixelFormatNV21                        = 3
	VideoPixelFormatRGB24                       = 4
	VideoPixelFormatRGBA                        = 5
	VideoPixelFormatARGB                        = 6
	VideoPixelFormatBGRA                        = 7
	VideoPixelFormatEndMark                     = 255
	VideoPixelFormatTexture2D                   = 3553
	VideoPixelFormatTextureOES                  = 36197
)

type VideoContentType = int

const (
	VideoContentTypeNormalFrame VideoContentType = 0
	VideoContentTypeBlackFrame                   = 1
)

type VideoBufferType = int

const (
	VideoBufferTypeRawMemory       VideoBufferType = 0
	VideoBufferTypeCVPixelBuffer                   = 1
	VideoBufferTypeGLTexture                       = 2
	VideoBufferTypeCuda                            = 3
	VideoBufferTypeD3D11                           = 4
	VideoBufferTypeVAAPI                           = 5
	VideoBufferTypeNvidiaJetsonDma                 = 6
)

type WTNSubscribeState = int

const (
	WTNSubscribeStateSubscribed   WTNSubscribeState = 0
	WTNSubscribeStateUnsubscribed                   = 1
)

type WTNSubscribeStateChangeReason = int

const (
	WTNSubscribeStateChangeReasonSubscribe                       WTNSubscribeStateChangeReason = 0
	WTNSubscribeStateChangeReasonUnsubscribe                                                   = 1300
	WTNSubscribeStateChangeReasonRemoteUnpublish                                               = 1301
	WTNSubscribeStateChangeReasonOverClientSubscribeStreamLimit                                = 1310
	WTNSubscribeStateChangeReasonOverStreamSubscribeUserLimit                                  = 1311
	WTNSubscribeStateChangeReasonOverStreamSubscribeRequestLimit                               = 1312
)
