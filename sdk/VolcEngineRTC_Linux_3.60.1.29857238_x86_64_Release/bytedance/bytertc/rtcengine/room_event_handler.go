package rtcengine

import (
	"bytedance/bytertc/native"
	"fmt"
)

type IRTCRoomEventHandler interface {
	OnRoomStateChanged(roomId string, uid string, state int, extraInfo string)
	OnLeaveRoom(arg2 RTCRoomStats)
	OnVideoPublishStateChanged(roomId string, uid string, state PublishState, reason PublishStateChangeReason)
	OnAudioPublishStateChanged(roomId string, uid string, state PublishState, reason PublishStateChangeReason)
	OnVideoSubscribeStateChanged(roomId string, uid string, state SubscribeState, reason SubscribeStateChangeReason)
	OnAudioSubscribeStateChanged(roomId string, uid string, state SubscribeState, reason SubscribeStateChangeReason)
	OnTokenWillExpire()
	OnPublishPrivilegeTokenWillExpire()
	OnSubscribePrivilegeTokenWillExpire()
	OnRoomStats(stats RTCRoomStats)
	OnRemoteStreamStats(stats RemoteStreamStats)
	OnUserJoined(userInfo UserInfo)
	OnUserLeave(uid string, reason UserOfflineReason)
	OnUserPublishStreamVideo(roomId string, uid string, isPublish bool)
	OnUserPublishStreamAudio(roomId string, uid string, isPublish bool)
	OnUserPublishScreenVideo(roomId string, uid string, isPublish bool)
	OnUserPublishScreenAudio(roomId string, uid string, isPublish bool)
	OnRoomMessageReceived(uid string, message string)
	OnRoomBinaryMessageReceived(uid string, message []byte)
	OnUserMessageReceived(uid string, message string)
	OnUserBinaryMessageReceived(uid string, message []byte)
	OnUserMessageSendResult(msgId int64, errorCode int)
	OnRoomMessageSendResult(msgId int64, errorCode int)
	OnVideoStreamBanned(uid string, banned bool)
	OnAVSyncStateChange(state AVSyncState)
	OnNetworkQuality(localQuality NetworkQualityStats, remoteQualities []NetworkQualityStats, remoteQualityNum int)
	OnSetRoomExtraInfoResult(taskId int64, result SetRoomExtraInfoResult)
	OnRoomExtraInfoUpdate(key string, value string, lastUpdateUserId string, lastUpdateTimeMs int64)
	OnUserVisibilityChanged(currentUserVisibility bool, errorCode UserVisibilityChangeError)
	OnSubtitleStateChanged(state SubtitleState, errorCode SubtitleErrorCode, errorMessage string)
	OnSubtitleMessageReceived(subtitles []SubtitleMessage)
	OnAVSyncEvent(roomId string, uid string, event AVSyncEvent)
	OnLocalStreamStats(stats LocalStreamStats)
	OnRoomStateChangedWithReason(roomId string, uid string, state RoomState, reason RoomStateChangeReason)
	OnRoomEvent(roomId string, uid string, state RoomEvent, info *RoomEventInfo)
	OnStreamAdd(stream *MediaStreamInfo)
}

type IRTCRoomEventHandlerDefaultImpl struct {
	IRTCRoomEventHandler
}

func (h *IRTCRoomEventHandlerDefaultImpl) OnRoomStateChanged(roomId string, uid string, state int, extraInfo string) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnLeaveRoom(arg2 RTCRoomStats) {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnVideoPublishStateChanged(roomId string, uid string, state PublishState, reason PublishStateChangeReason) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnAudioPublishStateChanged(roomId string, uid string, state PublishState, reason PublishStateChangeReason) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnVideoSubscribeStateChanged(roomId string, uid string, state SubscribeState, reason SubscribeStateChangeReason) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnAudioSubscribeStateChanged(roomId string, uid string, state SubscribeState, reason SubscribeStateChangeReason) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnTokenWillExpire()                               {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnPublishPrivilegeTokenWillExpire()               {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnSubscribePrivilegeTokenWillExpire()             {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnRoomStats(stats RTCRoomStats)                   {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnRemoteStreamStats(stats RemoteStreamStats)      {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserJoined(userInfo UserInfo)                   {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserLeave(uid string, reason UserOfflineReason) {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserPublishStreamVideo(roomId string, uid string, isPublish bool) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserPublishStreamAudio(roomId string, uid string, isPublish bool) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserPublishScreenVideo(roomId string, uid string, isPublish bool) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserPublishScreenAudio(roomId string, uid string, isPublish bool) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnRoomMessageReceived(uid string, message string)       {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnRoomBinaryMessageReceived(uid string, message []byte) {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserMessageReceived(uid string, message string)       {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserBinaryMessageReceived(uid string, message []byte) {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserMessageSendResult(msgId int64, errorCode int)     {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnRoomMessageSendResult(msgId int64, errorCode int)     {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnVideoStreamBanned(uid string, banned bool)            {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnAVSyncStateChange(state AVSyncState)                  {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnNetworkQuality(localQuality NetworkQualityStats, remoteQualities []NetworkQualityStats, remoteQualityNum int) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnSetRoomExtraInfoResult(taskId int64, result SetRoomExtraInfoResult) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnRoomExtraInfoUpdate(key string, value string, lastUpdateUserId string, lastUpdateTimeMs int64) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnUserVisibilityChanged(currentUserVisibility bool, errorCode UserVisibilityChangeError) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnSubtitleStateChanged(state SubtitleState, errorCode SubtitleErrorCode, errorMessage string) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnSubtitleMessageReceived(subtitles []SubtitleMessage) {}
func (h *IRTCRoomEventHandlerDefaultImpl) OnAVSyncEvent(roomId string, uid string, event AVSyncEvent) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnLocalStreamStats(stats LocalStreamStats) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnRoomEvent(roomId string, uid string, state RoomEvent, info *RoomEventInfo) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnStreamAdd(stream *MediaStreamInfo) {
}
func (h *IRTCRoomEventHandlerDefaultImpl) OnRoomStateChangedWithReason(roomId string, uid string, state RoomState, reason RoomStateChangeReason) {
}

type nativeIRTCRoomEventHandlerImp struct {
	native.IRTCRoomEventHandler
	GoHandler IRTCRoomEventHandler
}

// these callback need to be done in go
func (h *nativeIRTCRoomEventHandlerImp) OnRoomStateChanged(arg2 string, arg3 string, arg4 int, arg5 string) {
	fmt.Println("[rtcengine] OnRoomStateChanged")
	h.GoHandler.OnRoomStateChanged(arg2, arg3, arg4, arg5)
}
func (h *nativeIRTCRoomEventHandlerImp) OnLeaveRoom(arg2 native.RtcRoomStats) {
	h.GoHandler.OnLeaveRoom(toRTCRoomStats(arg2))
}
func (h *nativeIRTCRoomEventHandlerImp) OnVideoPublishStateChanged(arg2 string, arg3 string, arg4 int, arg5 int) {
	h.GoHandler.OnVideoPublishStateChanged(
		arg2,
		arg3,
		PublishState(arg4),
		PublishStateChangeReason(arg5))
}
func (h *nativeIRTCRoomEventHandlerImp) OnAudioPublishStateChanged(arg2 string, arg3 string, arg4 int, arg5 int) {
	h.GoHandler.OnAudioPublishStateChanged(
		arg2,
		arg3,
		PublishState(arg4),
		PublishStateChangeReason(arg5))
}
func (h *nativeIRTCRoomEventHandlerImp) OnVideoSubscribeStateChanged(arg2 string, arg3 string, arg4 int, arg5 int) {
	h.GoHandler.OnVideoSubscribeStateChanged(
		arg2,
		arg3,
		SubscribeState(arg4),
		SubscribeStateChangeReason(arg5))
}
func (h *nativeIRTCRoomEventHandlerImp) OnAudioSubscribeStateChanged(arg2 string, arg3 string, arg4 int, arg5 int) {
	h.GoHandler.OnAudioSubscribeStateChanged(
		arg2,
		arg3,
		SubscribeState(arg4),
		SubscribeStateChangeReason(arg5))
}
func (h *nativeIRTCRoomEventHandlerImp) OnTokenWillExpire() {
	h.GoHandler.OnTokenWillExpire()
}
func (h *nativeIRTCRoomEventHandlerImp) OnPublishPrivilegeTokenWillExpire() {
	h.GoHandler.OnPublishPrivilegeTokenWillExpire()
}
func (h *nativeIRTCRoomEventHandlerImp) OnSubscribePrivilegeTokenWillExpire() {
	h.GoHandler.OnSubscribePrivilegeTokenWillExpire()
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomStats(arg2 native.RtcRoomStats) {
	h.GoHandler.OnRoomStats(toRTCRoomStats(arg2))
}
func (h *nativeIRTCRoomEventHandlerImp) OnRemoteStreamStats(arg2 native.RemoteStreamStats) {
	h.GoHandler.OnRemoteStreamStats(toRemoteStreamStats(arg2))
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserJoined(arg2 native.UserInfo) {
	fmt.Println("[rtcengine] OnUserJoined")
	h.GoHandler.OnUserJoined(toUserInfo(arg2))
}

func (h *nativeIRTCRoomEventHandlerImp) OnUserLeave(arg2 string, arg3 int) {
	h.GoHandler.OnUserLeave(arg2, UserOfflineReason(arg3))
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserPublishStreamVideo(arg2 string, arg3 string, arg4 bool) {
	h.GoHandler.OnUserPublishStreamVideo(arg2, arg3, arg4)
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserPublishStreamAudio(arg2 string, arg3 string, arg4 bool) {
	h.GoHandler.OnUserPublishStreamAudio(arg2, arg3, arg4)
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserPublishScreenVideo(arg2 string, arg3 string, arg4 bool) {
	h.GoHandler.OnUserPublishScreenVideo(arg2, arg3, arg4)
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserPublishScreenAudio(arg2 string, arg3 string, arg4 bool) {
	h.GoHandler.OnUserPublishScreenAudio(arg2, arg3, arg4)
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomMessageReceived(arg2 string, arg3 string) {
	h.GoHandler.OnRoomMessageReceived(arg2, arg3)
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomBinaryMessageReceived(arg2 string, arg3 []byte) {
	h.GoHandler.OnRoomBinaryMessageReceived(arg2, arg3)
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserMessageReceived(arg2 string, arg3 string) {
	h.GoHandler.OnUserMessageReceived(arg2, arg3)
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserBinaryMessageReceived(arg2 string, arg3 []byte) {
	h.GoHandler.OnUserBinaryMessageReceived(arg2, arg3)
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserMessageSendResult(arg2 int64, arg3 int) {
	h.GoHandler.OnUserMessageSendResult(arg2, arg3)
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomMessageSendResult(arg2 int64, arg3 int) {
	h.GoHandler.OnRoomMessageSendResult(arg2, arg3)
}
func (h *nativeIRTCRoomEventHandlerImp) OnVideoStreamBanned(arg2 string, arg3 bool) {
	h.GoHandler.OnVideoStreamBanned(arg2, arg3)
}
func (h *nativeIRTCRoomEventHandlerImp) OnAVSyncStateChange(arg2 int) {
	h.GoHandler.OnAVSyncStateChange(AVSyncState(arg2))
}
func (h *nativeIRTCRoomEventHandlerImp) OnNetworkQuality(arg2 native.NetworkQualityStats, arrayHead native.NetworkQualityStats, size int) {
	infoArray := make([]NetworkQualityStats, size)
	for i := 0; i < size; i++ {
		stas := native.GetElementOfNetworkQualityStats(arrayHead, i)
		infoArray[i] = toNetworkQualityStats(stas)
	}
	h.GoHandler.OnNetworkQuality(toNetworkQualityStats(arg2), infoArray, size)
}
func (h *nativeIRTCRoomEventHandlerImp) OnSetRoomExtraInfoResult(arg2 int64, arg3 int) {
	h.GoHandler.OnSetRoomExtraInfoResult(arg2, SetRoomExtraInfoResult(arg3))
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomExtraInfoUpdate(arg2 string, arg3 string, arg4 string, arg5 int64) {
	h.GoHandler.OnRoomExtraInfoUpdate(arg2, arg3, arg4, arg5)
}
func (h *nativeIRTCRoomEventHandlerImp) OnUserVisibilityChanged(arg2 bool, arg3 int) {
	h.GoHandler.OnUserVisibilityChanged(arg2, UserVisibilityChangeError(arg3))
}
func (h *nativeIRTCRoomEventHandlerImp) OnSubtitleStateChanged(arg2 int, arg3 int, arg4 string) {
	h.GoHandler.OnSubtitleStateChanged(
		SubtitleState(arg2),
		SubtitleErrorCode(arg3),
		arg4)
}
func (h *nativeIRTCRoomEventHandlerImp) OnSubtitleMessageReceived(arrayHead native.SubtitleMessage, size int) {
	infoArray := make([]SubtitleMessage, size)
	for i := 0; i < size; i++ {
		infoArray[i] = toSubtitleMessage(native.GetElementOfSubtitleMessage(arrayHead, i))
	}
	h.GoHandler.OnSubtitleMessageReceived(infoArray)
}
func (h *nativeIRTCRoomEventHandlerImp) OnAVSyncEvent(arg2 string, arg3 string, arg4 int) {
	h.GoHandler.OnAVSyncEvent(arg2, arg3, AVSyncEvent(arg4))
}

// these callback is added in testing!
func (h *nativeIRTCRoomEventHandlerImp) OnLocalStreamStats(arg2 native.LocalStreamStats) {
	h.GoHandler.OnLocalStreamStats(toLocalStreamStats(arg2))
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomStateChangedWithReason(arg2 string, arg3 string, arg4 int, arg5 int) {
	h.GoHandler.OnRoomStateChangedWithReason(arg2, arg3, arg4, arg5)
}

// these callback need to be quiet
func (h *nativeIRTCRoomEventHandlerImp) OnScreenVideoPublishStateChanged(arg2 string, arg3 string, arg4 int, arg5 int) {
	// h.GoHandler.OnScreenVideoPublishStateChanged(arg2 , arg3 , arg4 , arg5 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnScreenAudioPublishStateChanged(arg2 string, arg3 string, arg4 int, arg5 int) {
	// h.GoHandler.OnScreenAudioPublishStateChanged(arg2 , arg3 , arg4 , arg5 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnScreenVideoSubscribeStateChanged(arg2 string, arg3 string, arg4 int, arg5 int) {
	// h.GoHandler.OnScreenVideoSubscribeStateChanged(arg2 , arg3 , arg4 , arg5 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnScreenAudioSubscribeStateChanged(arg2 string, arg3 string, arg4 int, arg5 int) {
	// h.GoHandler.OnScreenAudioSubscribeStateChanged(arg2 , arg3 , arg4 , arg5 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomWarning(arg2 int) {
	// h.GoHandler.OnRoomWarning(arg2 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomEvent(arg2 string, arg3 string, arg4 int, arg5 native.RoomEventInfo) {
	roomEventInfo := toRoomEventInfo(arg5)
	h.GoHandler.OnRoomEvent(arg2, arg3, arg4, &roomEventInfo)
}
func (h *nativeIRTCRoomEventHandlerImp) OnStreamRemove(arg2 native.MediaStreamInfo, arg3 int) {
	// h.GoHandler.OnStreamRemove( toMediaStreamInfo(arg2), arg3 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnStreamAdd(arg2 native.MediaStreamInfo) {
	streamInfo := toMediaStreamInfo(arg2)
	h.GoHandler.OnStreamAdd(&streamInfo)
}
func (h *nativeIRTCRoomEventHandlerImp) OnStreamPublishSuccess(arg2 string, arg3 bool) {
	// h.GoHandler.OnStreamPublishSuccess(arg2 , arg3 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnRoomModeChanged(arg2 int) {
	// h.GoHandler.OnRoomModeChanged(arg2 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnMaximumScreenShareFpsUpdated(arg2 int) {
	// h.GoHandler.OnMaximumScreenShareFpsUpdated(arg2 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnMaximumScreenSharePixelsUpdated(arg2 int) {
	// h.GoHandler.OnMaximumScreenSharePixelsUpdated(arg2 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnAudioStreamBanned(arg2 string, arg3 bool) {
	// h.GoHandler.OnAudioStreamBanned(arg2 , arg3 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnForwardStreamStateChanged(arg2 native.ForwardStreamStateInfo, arg3 int) {
	// h.GoHandler.OnForwardStreamStateChanged( toForwardStreamStateInfo(arg2), arg3 )
}
func (h *nativeIRTCRoomEventHandlerImp) OnForwardStreamEvent(arg2 native.ForwardStreamEventInfo, arg3 int) {
	// h.GoHandler.OnForwardStreamEvent( toForwardStreamEventInfo(arg2), arg3 )
}
