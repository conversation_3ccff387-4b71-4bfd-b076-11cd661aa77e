package rtcengine

import (
	"bytedance/bytertc/native"
)

type IRTCEngineEventHandler interface {
	OnWarning(warn int)
	OnError(err int)
	OnExtensionAccessError(extensionName string, msg string)
	OnLogReport(logType string, logContent string)
	OnConnectionStateChanged(state ConnectionState)
	OnNetworkTypeChanged(networkType NetworkType)
	OnSimulcastSubscribeFallback(event RemoteStreamSwitch)
	OnPerformanceAlarms(mode PerformanceAlarmMode, roomId string, reason PerformanceAlarmReason, data SourceWantedData)
	OnSysStats(stats SysStats)
	OnRecordingStateUpdate(streamType StreamIndex, state RecordingState, errorCode RecordingErrorCode, info RecordingInfo)
	OnRecordingProgressUpdate(streamIndex StreamIndex, process RecordingProgress, info RecordingInfo)
	OnAudioRecordingStateUpdate(state AudioRecordingState, errorCode AudioRecordingErrorCode)
	OnLoginResult(uid string, errorCode int, elapsed int)
	OnLogout(reason LogoutReason)
	OnServerParamsSetResult(errorCode int)
	OnGetPeerOnlineStatus(peerUserId string, status int)
	OnUserMessageReceivedOutsideRoom(uid string, message string)
	OnUserBinaryMessageReceivedOutsideRoom(uid string, message []byte)
	OnUserMessageSendResultOutsideRoom(msgid int64, errorCode int)
	OnServerMessageSendResult(msgid int64, errorCode int, msg ServerACKMsg)
	OnSEIMessageReceived(streamKey RemoteStreamKey, message []byte)
	OnSEIStreamUpdate(key RemoteStreamKey, eventType SEIStreamEventType)
	OnRemoteAudioPropertiesReport(audioPropertiesInfos []RemoteAudioPropertiesInfo, totalRemoteVolume int)
	OnLocalAudioPropertiesReport(audioPropertiesInfos []LocalAudioPropertiesInfo)
	OnActiveSpeaker(roomId string, uid string)
	OnNetworkDetectionResult(detectType NetworkDetectionLinkType, quality NetworkQuality, rtt int, loseRate float64, bitRate int, jitter int)
	OnNetworkDetectionStopped(reason NetworkDetectionStopReason)
	OnLocalVideoSizeChanged(index StreamIndex, info VideoFrameInfo)
	OnRemoteVideoSizeChanged(key RemoteStreamKey, info VideoFrameInfo)
	OnFirstLocalVideoFrameCaptured(index StreamIndex, info VideoFrameInfo)
	OnFirstRemoteAudioFrame(key RemoteStreamKey)
	OnRemoteAudioStateChanged(key RemoteStreamKey, state RemoteAudioState, reason RemoteAudioStateChangeReason)
	OnRemoteVideoStateChanged(key RemoteStreamKey, state RemoteVideoState, reason RemoteVideoStateChangeReason)
	OnAudioFrameSendStateChanged(roomId string, user RTCUser, state FirstFrameSendState)
	OnVideoFrameSendStateChanged(roomId string, user RTCUser, state FirstFrameSendState)
	OnFirstRemoteVideoFrameRendered(key RemoteStreamKey, info VideoFrameInfo)
	OnFirstRemoteVideoFrameDecoded(key RemoteStreamKey, info VideoFrameInfo)
	OnAudioFramePlayStateChanged(roomId string, user RTCUser, state FirstFramePlayState)
	OnVideoFramePlayStateChanged(roomId string, user RTCUser, state FirstFramePlayState)
	OnFirstLocalAudioFrame(index StreamIndex)
	OnCloudProxyConnected(interval int)
	OnLocalProxyStateChanged(localProxyType LocalProxyType, localProxyState LocalProxyState, localProxyError LocalProxyError)
	OnUserStartAudioCapture(roomID string, userID string)
	OnUserStopAudioCapture(roomID string, userID string)
	OnAudioDeviceStateChanged(deviceID string, deviceType RTCAudioDeviceType, deviceState MediaDeviceState, deviceError MediaDeviceError)
}

type IRTCEngineEventHandlerDefaultImp struct {
	IRTCEngineEventHandler
}

func (h *IRTCEngineEventHandlerDefaultImp) OnWarning(warn int)                                      {}
func (h *IRTCEngineEventHandlerDefaultImp) OnError(err int)                                         {}
func (h *IRTCEngineEventHandlerDefaultImp) OnExtensionAccessError(extensionName string, msg string) {}
func (h *IRTCEngineEventHandlerDefaultImp) OnLogReport(logType string, logContent string)           {}
func (h *IRTCEngineEventHandlerDefaultImp) OnConnectionStateChanged(state ConnectionState)          {}
func (h *IRTCEngineEventHandlerDefaultImp) OnNetworkTypeChanged(networkType NetworkType)            {}
func (h *IRTCEngineEventHandlerDefaultImp) OnSimulcastSubscribeFallback(event RemoteStreamSwitch)   {}
func (h *IRTCEngineEventHandlerDefaultImp) OnPerformanceAlarms(mode PerformanceAlarmMode, roomId string, reason PerformanceAlarmReason, data SourceWantedData) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnSysStats(stats SysStats) {}
func (h *IRTCEngineEventHandlerDefaultImp) OnRecordingStateUpdate(streamType StreamIndex, state RecordingState, errorCode RecordingErrorCode, info RecordingInfo) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnRecordingProgressUpdate(streamIndex StreamIndex, process RecordingProgress, info RecordingInfo) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnAudioRecordingStateUpdate(state AudioRecordingState, errorCode AudioRecordingErrorCode) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnLoginResult(uid string, errorCode int, elapsed int) {}
func (h *IRTCEngineEventHandlerDefaultImp) OnLogout(reason LogoutReason)                         {}
func (h *IRTCEngineEventHandlerDefaultImp) OnServerParamsSetResult(errorCode int)                {}
func (h *IRTCEngineEventHandlerDefaultImp) OnGetPeerOnlineStatus(peerUserId string, status int)  {}
func (h *IRTCEngineEventHandlerDefaultImp) OnUserMessageReceivedOutsideRoom(uid string, message string) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnUserBinaryMessageReceivedOutsideRoom(uid string, message []byte) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnUserMessageSendResultOutsideRoom(msgid int64, errorCode int) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnServerMessageSendResult(msgid int64, errorCode int, msg ServerACKMsg) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnSEIMessageReceived(streamKey RemoteStreamKey, message []byte) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnSEIStreamUpdate(key RemoteStreamKey, eventType SEIStreamEventType) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnRemoteAudioPropertiesReport(audioPropertiesInfos []RemoteAudioPropertiesInfo, totalRemoteVolume int) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnLocalAudioPropertiesReport(audioPropertiesInfos []LocalAudioPropertiesInfo) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnActiveSpeaker(roomId string, uid string) {}
func (h *IRTCEngineEventHandlerDefaultImp) OnNetworkDetectionResult(detectType NetworkDetectionLinkType, quality NetworkQuality, rtt int, loseRate float64, bitRate int, jitter int) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnNetworkDetectionStopped(reason NetworkDetectionStopReason) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnLocalVideoSizeChanged(index StreamIndex, info VideoFrameInfo) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnRemoteVideoSizeChanged(key RemoteStreamKey, info VideoFrameInfo) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnFirstLocalVideoFrameCaptured(index StreamIndex, info VideoFrameInfo) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnFirstRemoteAudioFrame(key RemoteStreamKey) {}
func (h *IRTCEngineEventHandlerDefaultImp) OnRemoteAudioStateChanged(key RemoteStreamKey, state RemoteAudioState, reason RemoteAudioStateChangeReason) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnRemoteVideoStateChanged(key RemoteStreamKey, state RemoteVideoState, reason RemoteVideoStateChangeReason) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnAudioFrameSendStateChanged(roomId string, user RTCUser, state FirstFrameSendState) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnVideoFrameSendStateChanged(roomId string, user RTCUser, state FirstFrameSendState) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnFirstRemoteVideoFrameRendered(key RemoteStreamKey, info VideoFrameInfo) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnFirstRemoteVideoFrameDecoded(key RemoteStreamKey, info VideoFrameInfo) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnAudioFramePlayStateChanged(roomId string, user RTCUser, state FirstFramePlayState) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnVideoFramePlayStateChanged(roomId string, user RTCUser, state FirstFramePlayState) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnFirstLocalAudioFrame(index StreamIndex) {}
func (h *IRTCEngineEventHandlerDefaultImp) OnCloudProxyConnected(interval int)       {}
func (h *IRTCEngineEventHandlerDefaultImp) OnLocalProxyStateChanged(localProxyType LocalProxyType, localProxyState LocalProxyState, localProxyError LocalProxyError) {
}
func (h *IRTCEngineEventHandlerDefaultImp) OnUserStartAudioCapture(roomID string, userID string) {}
func (h *IRTCEngineEventHandlerDefaultImp) OnUserStopAudioCapture(roomID string, userID string) {}
func (h *IRTCEngineEventHandlerDefaultImp) OnAudioDeviceStateChanged(deviceID string, deviceType RTCAudioDeviceType, deviceState MediaDeviceState, deviceError MediaDeviceError) {}

type nativeIRTCEngineEventHandlerImp struct {
	native.IRTCEngineEventHandler
	GoHandler IRTCEngineEventHandler
}

// the following callback will proxy to GO SDK
func (h *nativeIRTCEngineEventHandlerImp) OnWarning(arg2 int) {
	h.GoHandler.OnWarning(arg2)
}
func (h *nativeIRTCEngineEventHandlerImp) OnError(arg2 int) {
	h.GoHandler.OnError(arg2)
}
func (h *nativeIRTCEngineEventHandlerImp) OnExtensionAccessError(arg2 string, arg3 string) {
	h.GoHandler.OnExtensionAccessError(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnLogReport(arg2 string, arg3 string) {
	h.GoHandler.OnLogReport(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnConnectionStateChanged(arg2 int) {
	h.GoHandler.OnConnectionStateChanged(ConnectionState(arg2))
}
func (h *nativeIRTCEngineEventHandlerImp) OnNetworkTypeChanged(arg2 int) {
	h.GoHandler.OnNetworkTypeChanged(NetworkType(arg2))
}
func (h *nativeIRTCEngineEventHandlerImp) OnSimulcastSubscribeFallback(arg2 native.RemoteStreamSwitch) {
	h.GoHandler.OnSimulcastSubscribeFallback(toRemoteStreamSwitch(arg2))
	// native.DeleteRemoteStreamSwitch(arg2)
}
func (h *nativeIRTCEngineEventHandlerImp) OnPerformanceAlarms(arg2 int, arg3 string, arg4 int, arg5 native.SourceWantedData) {
	h.GoHandler.OnPerformanceAlarms(PerformanceAlarmMode(arg2), arg3, PerformanceAlarmReason(arg4), toSourceWantedData(arg5))
	// native.DeleteSourceWantedData(arg5)
}
func (h *nativeIRTCEngineEventHandlerImp) OnSysStats(arg2 native.SysStats) {
	h.GoHandler.OnSysStats(toSysStats(arg2))
	// native.DeleteSysStats(arg2)
}
func (h *nativeIRTCEngineEventHandlerImp) OnRecordingStateUpdate(arg2 int, arg3 int, arg4 int, arg5 native.RecordingInfo) {
	h.GoHandler.OnRecordingStateUpdate(StreamIndex(arg2), RecordingState(arg3), RecordingErrorCode(arg4), toRecordingInfo(arg5))
	// native.DeleteRecordingInfo(arg5)
}
func (h *nativeIRTCEngineEventHandlerImp) OnRecordingProgressUpdate(arg2 int, arg3 native.RecordingProgress, arg4 native.RecordingInfo) {
	h.GoHandler.OnRecordingProgressUpdate(StreamIndex(arg2), toRecordingProgress(arg3), toRecordingInfo(arg4))
	// native.DeleteRecordingProgress(arg3)
	// native.DeleteRecordingInfo(arg4)
}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioRecordingStateUpdate(arg2 int, arg3 int) {
	h.GoHandler.OnAudioRecordingStateUpdate(AudioRecordingState(arg2), AudioRecordingErrorCode(arg3))
}
func (h *nativeIRTCEngineEventHandlerImp) OnLoginResult(arg2 string, arg3 int, arg4 int) {
	h.GoHandler.OnLoginResult(arg2, arg3, arg4)
}
func (h *nativeIRTCEngineEventHandlerImp) OnLogout(arg2 int) {
	h.GoHandler.OnLogout(LogoutReason(arg2))
}
func (h *nativeIRTCEngineEventHandlerImp) OnServerParamsSetResult(arg2 int) {
	h.GoHandler.OnServerParamsSetResult(arg2)
}
func (h *nativeIRTCEngineEventHandlerImp) OnGetPeerOnlineStatus(arg2 string, arg3 int) {
	h.GoHandler.OnGetPeerOnlineStatus(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnUserMessageReceivedOutsideRoom(arg2 string, arg3 string) {
	h.GoHandler.OnUserMessageReceivedOutsideRoom(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnUserBinaryMessageReceivedOutsideRoom(arg2 string, arg3 []byte) {
	h.GoHandler.OnUserBinaryMessageReceivedOutsideRoom(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnUserMessageSendResultOutsideRoom(arg2 int64, arg3 int) {
	h.GoHandler.OnUserMessageSendResultOutsideRoom(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnServerMessageSendResult(arg2 int64, arg3 int, arg4 native.ServerACKMsg) {
	h.GoHandler.OnServerMessageSendResult(arg2, arg3, toServerACKMsg(arg4))
	// native.DeleteServerACKMsg(arg4)
}
func (h *nativeIRTCEngineEventHandlerImp) OnSEIMessageReceived(arg2 native.RemoteStreamKey, arg3 []byte) {
	h.GoHandler.OnSEIMessageReceived(toRemoteStreamKey(arg2), arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnSEIStreamUpdate(arg2 native.RemoteStreamKey, arg3 int) {
	h.GoHandler.OnSEIStreamUpdate(toRemoteStreamKey(arg2), SEIStreamEventType(arg3))
}
func (h *nativeIRTCEngineEventHandlerImp) OnRemoteAudioPropertiesReport(arrayHead native.RemoteAudioPropertiesInfo, size int, arg4 int) {
	infoArray := make([]RemoteAudioPropertiesInfo, size)
	for i := 0; i < size; i++ {
		infoArray[i] = toRemoteAudioPropertiesInfo(native.GetElementOfRemoteAudioPropertiesInfo(arrayHead, i))
	}
	h.GoHandler.OnRemoteAudioPropertiesReport(infoArray, arg4)
}
func (h *nativeIRTCEngineEventHandlerImp) OnLocalAudioPropertiesReport(array_head native.LocalAudioPropertiesInfo, size int) {
	infoArray := make([]LocalAudioPropertiesInfo, size)
	for i := 0; i < size; i++ {
		infoArray[i] = toLocalAudioPropertiesInfo(native.GetElementOfLocalAudioPropertiesInfo(array_head, i))
	}
	h.GoHandler.OnLocalAudioPropertiesReport(infoArray)
}
func (h *nativeIRTCEngineEventHandlerImp) OnActiveSpeaker(arg2 string, arg3 string) {
	h.GoHandler.OnActiveSpeaker(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnNetworkDetectionResult(arg2 int, arg3 int, arg4 int, arg5 float64, arg6 int, arg7 int) {
	h.GoHandler.OnNetworkDetectionResult(NetworkDetectionLinkType(arg2),
		NetworkQuality(arg3),
		arg4, arg5, arg6, arg7)
}
func (h *nativeIRTCEngineEventHandlerImp) OnNetworkDetectionStopped(arg2 int) {
	h.GoHandler.OnNetworkDetectionStopped(NetworkDetectionStopReason(arg2))
}
func (h *nativeIRTCEngineEventHandlerImp) OnLocalVideoSizeChanged(arg2 int, arg3 native.VideoFrameInfo) {
	h.GoHandler.OnLocalVideoSizeChanged(StreamIndex(arg2), toVideoFrameInfo(arg3))
}
func (h *nativeIRTCEngineEventHandlerImp) OnRemoteVideoSizeChanged(arg2 native.RemoteStreamKey, arg3 native.VideoFrameInfo) {
	h.GoHandler.OnRemoteVideoSizeChanged(toRemoteStreamKey(arg2), toVideoFrameInfo(arg3))
}
func (h *nativeIRTCEngineEventHandlerImp) OnFirstLocalVideoFrameCaptured(arg2 int, arg3 native.VideoFrameInfo) {
	h.GoHandler.OnFirstLocalVideoFrameCaptured(StreamIndex(arg2), toVideoFrameInfo(arg3))
}
func (h *nativeIRTCEngineEventHandlerImp) OnFirstRemoteAudioFrame(arg2 native.RemoteStreamKey) {
	h.GoHandler.OnFirstRemoteAudioFrame(toRemoteStreamKey(arg2))
}
func (h *nativeIRTCEngineEventHandlerImp) OnRemoteAudioStateChanged(arg2 native.RemoteStreamKey, arg3 int, arg4 int) {
	h.GoHandler.OnRemoteAudioStateChanged(
		toRemoteStreamKey(arg2),
		RemoteAudioState(arg3),
		RemoteAudioStateChangeReason(arg4),
	)
}
func (h *nativeIRTCEngineEventHandlerImp) OnRemoteVideoStateChanged(arg2 native.RemoteStreamKey, arg3 int, arg4 int) {
	h.GoHandler.OnRemoteVideoStateChanged(
		toRemoteStreamKey(arg2),
		RemoteVideoState(arg3),
		RemoteVideoStateChangeReason(arg4),
	)
}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioFrameSendStateChanged(arg2 string, arg3 native.RtcUser, arg4 int) {
	h.GoHandler.OnAudioFrameSendStateChanged(arg2,
		toRTCUser(arg3),
		FirstFrameSendState(arg4))
}
func (h *nativeIRTCEngineEventHandlerImp) OnVideoFrameSendStateChanged(arg2 string, arg3 native.RtcUser, arg4 int) {
	h.GoHandler.OnVideoFrameSendStateChanged(arg2,
		toRTCUser(arg3),
		FirstFrameSendState(arg4))
}
func (h *nativeIRTCEngineEventHandlerImp) OnFirstRemoteVideoFrameRendered(arg2 native.RemoteStreamKey, arg3 native.VideoFrameInfo) {
	h.GoHandler.OnFirstRemoteVideoFrameRendered(
		toRemoteStreamKey(arg2),
		toVideoFrameInfo(arg3))
}
func (h *nativeIRTCEngineEventHandlerImp) OnFirstRemoteVideoFrameDecoded(arg2 native.RemoteStreamKey, arg3 native.VideoFrameInfo) {
	h.GoHandler.OnFirstRemoteVideoFrameDecoded(
		toRemoteStreamKey(arg2),
		toVideoFrameInfo(arg3))
}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioFramePlayStateChanged(arg2 string, arg3 native.RtcUser, arg4 int) {
	h.GoHandler.OnAudioFramePlayStateChanged(arg2,
		toRTCUser(arg3),
		FirstFramePlayState(arg4))
}
func (h *nativeIRTCEngineEventHandlerImp) OnVideoFramePlayStateChanged(arg2 string, arg3 native.RtcUser, arg4 int) {
	h.GoHandler.OnVideoFramePlayStateChanged(arg2,
		toRTCUser(arg3),
		FirstFramePlayState(arg4))
}
func (h *nativeIRTCEngineEventHandlerImp) OnFirstLocalAudioFrame(arg2 int) {
	h.GoHandler.OnFirstLocalAudioFrame(StreamIndex(arg2))
}
func (h *nativeIRTCEngineEventHandlerImp) OnCloudProxyConnected(arg2 int) {
	h.GoHandler.OnCloudProxyConnected(arg2)
}
func (h *nativeIRTCEngineEventHandlerImp) OnLocalProxyStateChanged(arg2 int, arg3 int, arg4 int) {
	h.GoHandler.OnLocalProxyStateChanged(
		LocalProxyType(arg2),
		LocalProxyState(arg3),
		LocalProxyError(arg4),
	)
}
func (h *nativeIRTCEngineEventHandlerImp) OnUserStartAudioCapture(arg2 string, arg3 string)         {
	h.GoHandler.OnUserStartAudioCapture(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnUserStopAudioCapture(arg2 string, arg3 string)          {
	h.GoHandler.OnUserStopAudioCapture(arg2, arg3)
}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioDeviceStateChanged(arg2 string, arg3 int, arg4 int, arg5 int) {
	h.GoHandler.OnAudioDeviceStateChanged(arg2, RTCAudioDeviceType(arg3), MediaDeviceState(arg4), MediaDeviceError(arg5))
}

// the following callback dose not need to handle
func (h *nativeIRTCEngineEventHandlerImp) OnDeadLockError(arg2 native.DeadLockMsg)           {}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioMixingPlayingProgress(arg2 int, arg3 int64) {}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioRouteChanged(arg2 int)                      {}
func (h *nativeIRTCEngineEventHandlerImp) OnVideoDeviceStateChanged(arg2 string, arg3 int, arg4 int, arg5 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioDeviceWarning(arg2 string, arg3 int, arg4 int) {}
func (h *nativeIRTCEngineEventHandlerImp) OnVideoDeviceWarning(arg2 string, arg3 int, arg4 int) {}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioAEDStateUpdate(arg2 int)                       {}
func (h *nativeIRTCEngineEventHandlerImp) OnRemoteAudioPropertiesReportEx(arg2 native.RemoteAudioPropertiesInfo, arg3 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioVADStateUpdate(arg2 int)                           {}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioPlaybackDeviceTestVolume(arg2 int)                 {}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioDeviceVolumeChanged(arg2 int, arg3 int, arg4 bool) {}
func (h *nativeIRTCEngineEventHandlerImp) OnLocalAudioStateChanged(arg2 int, arg3 int)              {}
func (h *nativeIRTCEngineEventHandlerImp) OnStreamSyncInfoReceived(arg2 native.RemoteStreamKey, arg3 int, arg4 []byte) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnUserStartVideoCapture(arg2 string, arg3 string)      {}
func (h *nativeIRTCEngineEventHandlerImp) OnUserStopVideoCapture(arg2 string, arg3 string)       {}
func (h *nativeIRTCEngineEventHandlerImp) OnLocalVideoStateChanged(arg2 int, arg3 int, arg4 int) {}
func (h *nativeIRTCEngineEventHandlerImp) OnRemoteVideoSuperResolutionModeChanged(arg2 native.RemoteStreamKey, arg3 int, arg4 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnVideoDenoiseModeChanged(arg2 int, arg3 int) {}
func (h *nativeIRTCEngineEventHandlerImp) OnScreenAudioFrameSendStateChanged(arg2 string, arg3 native.RtcUser, arg4 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnScreenVideoFrameSendStateChanged(arg2 string, arg3 native.RtcUser, arg4 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnScreenAudioFramePlayStateChanged(arg2 string, arg3 native.RtcUser, arg4 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnScreenVideoFramePlayStateChanged(arg2 string, arg3 native.RtcUser, arg4 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnPushPublicStreamResult(arg2 string, arg3 string, arg4 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnEchoTestResult(arg2 int)                               {}
func (h *nativeIRTCEngineEventHandlerImp) OnAudioDumpStateChanged(arg2 int)                        {}
func (h *nativeIRTCEngineEventHandlerImp) OnNetworkTimeSynchronized()                              {}
func (h *nativeIRTCEngineEventHandlerImp) OnLicenseWillExpire(arg2 int)                            {}
func (h *nativeIRTCEngineEventHandlerImp) OnExternalScreenFrameUpdate(arg2 native.FrameUpdateInfo) {}
func (h *nativeIRTCEngineEventHandlerImp) OnHardwareEchoDetectionResult(arg2 int)                  {}
func (h *nativeIRTCEngineEventHandlerImp) OnEffectError(arg2 int, arg3 string)                     {}
func (h *nativeIRTCEngineEventHandlerImp) OnRemoteRenderError(arg2 native.RemoteStreamKey, arg3 int, arg4 string) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnMixedStreamEvent(arg2 native.MixedStreamTaskInfo, arg3 int, arg4 int) {
}
func (h *nativeIRTCEngineEventHandlerImp) OnSingleStreamEvent(arg2 string, arg3 int, arg4 int) {}
func (h *nativeIRTCEngineEventHandlerImp) OnExperimentalCallback(arg2 string)                  {}
