package rtcengine

import (
	"bytedance/bytertc/native"
	"sync"

	_ "github.com/ianlancetaylor/cgosymbolizer"
)

type IRTCEngine interface {
	SetAudioSourceType(audioSourceType int) int
	PushExternalAudioFrame(audioFrame IAudioFrame) int
	StartVideoCapture() int
	SetLocalSimulcastMode(mode VideoSimulcastMode) int
	SetLocalSimulcastModeWithStreamConfigs(mode VideoSimulcastMode, streamConfig []VideoEncoderConfig) int
	SetVideoEncoderConfig(encoderConfig VideoEncoderConfig) int
	SetLocalVideoCanvas(index StreamIndex, arg3 VideoCanvas) int
	SetRemoteVideoCanvas(streamKey RemoteStreamKey, canvas VideoCanvas) int
	SetRemoteVideoSink(streamKey RemoteStreamKey, videoSink IVideoSink, pixelFormat PixelFormat) int
	SetVideoSourceType(streamIndex StreamIndex, sourceType VideoSourceType) int
	PushExternalVideoFrame(frame VideoFrameData) int
	CreateRTCRoom(roomID string) IRTCRoom
	SetPublishFallbackOption(option PublishFallbackOption) int
	SetSubscribeFallbackOption(option SubscribeFallbackOption) int
	SetRemoteUserPriority(roomId string, userId string, priority RemoteUserPriority) int
	SetBusinessId(businessId string) int
	SetEncryptInfo(encryptType EncryptType, keyData []byte) int
	SetCustomizeEncryptHandler(handler IEncryptHandler) int
	EnableAudioFrameCallback(method AudioFrameCallbackMethod, format *AudioFormat) int
	DisableAudioFrameCallback(method AudioFrameCallbackMethod) int
	RegisterAudioFrameObserver(observer IAudioFrameObserver) int
	RegisterAudioProcessor(processor IAudioFrameProcessor) int
	EnableAudioProcessor(method AudioProcessorMethod, format AudioFormat) int
	DisableAudioProcessor(method AudioProcessorMethod) int
	RegisterLocalVideoProcessor(processor IVideoProcessor, config VideoPreprocessorConfig) int
	SendSEIMessage(streamIndex StreamIndex, message []byte, repeatCount int, mode SEICountPerFrame) int
	StartFileRecording(steamIndex StreamIndex, config RecordingConfig, recordingType RecordingType) int
	StopFileRecording(steamType StreamIndex) int
	StartAudioRecording(config AudioRecordingConfig) int
	StopAudioRecording() int
	SetRuntimeParameters(jsonString string) int
	Feedback(feedbackType uint64, info *ProblemFeedbackInfo) int
	Login(token string, uid string) int
	Logout() int
	UpdateLoginToken(token string) int
	SetServerParams(signature string, url string) int
	GetPeerOnlineStatus(peerUserId string) int
	SendUserMessageOutsideRoom(uid string, message string, config MessageConfig) int64
	SendUserBinaryMessageOutsideRoom(uid string, message []byte, config MessageConfig) int64
	SendServerMessage(message string) int64
	SendServerBinaryMessage(message []byte) int64
	StartNetworkDetection(isTestUplink bool, expectedUplinkBitrate int, isTestDownlink bool, expectedDownlinkBiterate int) int
	StopNetworkDetection() int
	SetScreenAudioSourceType(sourceType AudioSourceType) int
	SetScreenAudioStreamIndex(index StreamIndex) int
	PushScreenAudioFrame(frame IAudioFrame) int
	EnableAudioPropertiesReport(config *AudioPropertiesConfig) int
	RegisterLocalEncodedVideoFrameObserver(observer ILocalEncodedVideoFrameObserver) int
	RegisterRemoteEncodedVideoFrameObserver(observer IRemoteEncodedVideoFrameObserver) int
	SetExternalVideoEncoderEventHandler(encoderHandler IExternalVideoEncoderEventHandler) int
	PushExternalEncodedVideoFrame(streamIndex StreamIndex, videoIndex int, videoStream IEncodedVideoFrame) int
	SetVideoDecoderConfig(key *RemoteStreamKey, config VideoDecoderConfig) int
	RequestRemoteVideoKeyFrame(streamInfo *RemoteStreamKey) int
	SendStreamSyncInfo(data []byte, config *StreamSycnInfoConfig) int
	StartCloudProxy(configuration *CloudProxyConfiguration) int
	StopCloudProxy() int
	SetLocalProxy(configurations []LocalProxyConfiguration) int
	StartAudioCapture() int
	GetVideoDeviceManager() IVideoDeviceManager
	StopVideoCapture() int
}

type rtcEngineImp struct {
	nativeEngine               native.IRTCEngine
	nativeEngineHandler        native.IRTCEngineEventHandler
	nativeIEcryptHandler       native.IEncryptHandler
	nativeIAudioFrameProcessor native.IAudioFrameProcessor
	nativeIVideoProcessor      native.IVideoProcessor
	engineAPILock              sync.RWMutex
}

var (
	engineCreationLock sync.Mutex
	engineInstance     IRTCEngine
)

func CreateRTCEngine(config *EngineConfig, eventHandler IRTCEngineEventHandler) IRTCEngine {
	engineCreationLock.Lock()
	defer engineCreationLock.Unlock()
	if engineInstance != nil {
		return engineInstance
	}

	nativeConfig := toNativeEngineConfig(config)
	defer deleteNativeEngineConfig(nativeConfig)

	var nativeEngineHandler native.IRTCEngineEventHandler = nil
	if eventHandler != nil {
		nativeEngineHandler = native.NewDirectorIRTCEngineEventHandler(
			&nativeIRTCEngineEventHandlerImp{
				GoHandler: eventHandler,
			},
		)
	}
	nativeEngine := native.IRTCEngineCreateRTCEngine(
		nativeConfig,
		nativeEngineHandler,
	)
	engineInstance = &rtcEngineImp{nativeEngine: nativeEngine, nativeEngineHandler: nativeEngineHandler}
	return engineInstance
}

func CreateRTCEngineMulti(config *EngineConfig, eventHandler IRTCEngineEventHandler) IRTCEngine {
	nativeConfig := toNativeEngineConfig(config)
	defer deleteNativeEngineConfig(nativeConfig)
	var nativeEngineHandler native.IRTCEngineEventHandler = nil
	if eventHandler != nil {
		nativeEngineHandler = native.NewDirectorIRTCEngineEventHandler(
			&nativeIRTCEngineEventHandlerImp{
				GoHandler: eventHandler,
			},
		)
	}
	nativeEngine := native.IRTCEngineCreateRTCEngineMulti(
		nativeConfig,
		nativeEngineHandler,
	)
	return &rtcEngineImp{nativeEngine: nativeEngine, nativeEngineHandler: nativeEngineHandler}
}

func DestroyRTCEngine() {
	engineCreationLock.Lock()
	defer engineCreationLock.Unlock()
	if engineInstance != nil {
		if rtcEngine, ok := engineInstance.(*rtcEngineImp); ok {
			rtcEngine.destroyRTCEngine()
		}
		engineInstance = nil
	}
}

func DestroyRTCEngineMulti(engine IRTCEngine) {
	rtcEngine, ok := engine.(*rtcEngineImp)
	rtcEngine.engineAPILock.Lock()
	if !ok || rtcEngine.nativeEngine == nil {
		rtcEngine.engineAPILock.Unlock()
		return
	}
	nativeEngine := rtcEngine.nativeEngine
	rtcEngine.nativeEngine = nil
	rtcEngine.engineAPILock.Unlock()
	native.IRTCEngineDestroyRTCEngineMulti(nativeEngine)
}

func GetSDKVersion() string {
	return native.IRTCEngineGetSDKVersion()
}

func SetLogConfig(logConfig *LogConfig) int {
	if logConfig == nil {
		return 0
	}
	nativeLogConfig := toNativeLogConfig(logConfig)
	defer deleteNativeLogConfig(nativeLogConfig)
	return native.IRTCEngineSetLogConfig(nativeLogConfig)
}

func (engine *rtcEngineImp) destroyRTCEngine() {
	engine.engineAPILock.Lock()
	engine.nativeEngine = nil
	engine.engineAPILock.Unlock()
	native.IRTCEngineDestroyRTCEngine()
}

func (engine *rtcEngineImp) StartVideoCapture() int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.StartVideoCapture()
}
func (engine *rtcEngineImp) SetLocalSimulcastMode(mode VideoSimulcastMode) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetLocalSimulcastMode(mode)
}

func (engine *rtcEngineImp) SetLocalSimulcastModeWithStreamConfigs(mode VideoSimulcastMode, streamConfig []VideoEncoderConfig) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeConfigArray := toNativeVideoEncoderConfigArray(streamConfig)
	defer deleteNativeVideoEncoderConfigArray(nativeConfigArray)
	return engine.nativeEngine.SetLocalSimulcastMode(mode, nativeConfigArray, len(streamConfig))
}

func (engine *rtcEngineImp) StartAudioCapture() int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.StartAudioCapture()
}

func (engine *rtcEngineImp) SetVideoEncoderConfig(encoderConfig VideoEncoderConfig) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeConfig := toNativeVideoEncoderConfig(encoderConfig)
	defer native.DeleteVideoEncoderConfig(nativeConfig)
	return engine.nativeEngine.SetVideoEncoderConfig(nativeConfig)
}
func (engine *rtcEngineImp) SetLocalVideoCanvas(index StreamIndex, canvas VideoCanvas) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeVideoCanvas := toNativeVideoCanvas(&canvas)
	defer deleteNativeVideoCanvas(nativeVideoCanvas)

	return engine.nativeEngine.SetLocalVideoCanvas(int(index), nativeVideoCanvas)
}
func (engine *rtcEngineImp) SetRemoteVideoCanvas(streamKey RemoteStreamKey, canvas VideoCanvas) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeRemoteStreamKey := toNativeRemoteStreamKey(&streamKey)
	defer deleteNativeRemoteStreamKey(nativeRemoteStreamKey)

	nativeVideoCanvas := toNativeVideoCanvas(&canvas)
	defer deleteNativeVideoCanvas(nativeVideoCanvas)

	return engine.nativeEngine.SetRemoteVideoCanvas(nativeRemoteStreamKey, nativeVideoCanvas)
}

func (engine *rtcEngineImp) SetRemoteVideoSink(streamKey RemoteStreamKey, videoSink IVideoSink, pixelFormat PixelFormat) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeRemoteStreamKey := toNativeRemoteStreamKey(&streamKey)
	defer deleteNativeRemoteStreamKey(nativeRemoteStreamKey)

	nativeVideo, ok := videoSink.(videoSinkDefaultImp)
	if !ok {
		panic("videoSink instance error")
	}

	return engine.nativeEngine.SetRemoteVideoSink(nativeRemoteStreamKey, nativeVideo, pixelFormat)
}

func (engine *rtcEngineImp) SetVideoSourceType(streamIndex StreamIndex, sourceType VideoSourceType) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetVideoSourceType(streamIndex, sourceType)
}
func (engine *rtcEngineImp) PushExternalVideoFrame(frame VideoFrameData) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeVideoFrameData := toNativeVideoFrameData(&frame)
	defer deleteNativeVideoFrameData(nativeVideoFrameData)

	return engine.nativeEngine.PushExternalVideoFrame(nativeVideoFrameData)
}
func (engine *rtcEngineImp) SetPublishFallbackOption(option PublishFallbackOption) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetPublishFallbackOption(option)
}
func (engine *rtcEngineImp) SetSubscribeFallbackOption(option SubscribeFallbackOption) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetSubscribeFallbackOption(option)
}
func (engine *rtcEngineImp) SetRemoteUserPriority(roomId string, userId string, priority RemoteUserPriority) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetRemoteUserPriority(roomId, userId, priority)
}
func (engine *rtcEngineImp) SetBusinessId(businessId string) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetBusinessId(businessId)
}
func (engine *rtcEngineImp) SetEncryptInfo(encryptType EncryptType, keyData []byte) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetEncryptInfo(encryptType, keyData)
}

func (engine *rtcEngineImp) SetCustomizeEncryptHandler(handler IEncryptHandler) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}

	var nativeHandler native.IEncryptHandler = nil
	if handler != nil {
		nativeHandler = native.NewDirectorIEncryptHandler(
			&nativeEncryptHandlerImp{
				GoEncryptHandler: handler,
			},
		)
	}
	engine.nativeIEcryptHandler = nativeHandler
	return engine.nativeEngine.SetCustomizeEncryptHandler(nativeHandler)
}
func (engine *rtcEngineImp) DisableAudioFrameCallback(method AudioFrameCallbackMethod) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.DisableAudioFrameCallback(method)
}
func (engine *rtcEngineImp) RegisterAudioProcessor(processor IAudioFrameProcessor) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}

	var nativeProcessor native.IAudioFrameProcessor = nil
	if processor != nil {
		nativeProcessor = native.NewDirectorIAudioFrameProcessor(
			&nativeIAudioFrameProcessorImp{
				GoHandler: processor,
			},
		)
	}
	engine.nativeIAudioFrameProcessor = nativeProcessor
	return engine.nativeEngine.RegisterAudioProcessor(nativeProcessor)
}
func (engine *rtcEngineImp) EnableAudioProcessor(method AudioProcessorMethod, format AudioFormat) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeAudioFormat := toNativeAudioFormat(&format)
	defer deleteNativeAudioFormat(nativeAudioFormat)
	return engine.nativeEngine.EnableAudioProcessor(method, nativeAudioFormat)

}
func (engine *rtcEngineImp) DisableAudioProcessor(method AudioProcessorMethod) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.DisableAudioProcessor(method)
}
func (engine *rtcEngineImp) RegisterLocalVideoProcessor(processor IVideoProcessor, config VideoPreprocessorConfig) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}

	nativeConfig := toNativeVideoPreprocessorConfig(config)
	defer deleteNativeVideoPreprocessorConfig(nativeConfig)
	if processor == nil {
		return engine.nativeEngine.RegisterLocalVideoProcessor(nil, nativeConfig)
	}

	var nativeVideoProcessor native.IVideoProcessor = nil
	if processor != nil {
		nativeVideoProcessor = native.NewDirectorIVideoProcessor(
			&nativeIVideoProcessorDefaultImp{
				GoHandler: processor,
			},
		)
	}
	engine.nativeIVideoProcessor = nativeVideoProcessor
	return engine.nativeEngine.RegisterLocalVideoProcessor(nativeVideoProcessor, nativeConfig)
}
func (engine *rtcEngineImp) SendSEIMessage(streamIndex StreamIndex, message []byte, repeatCount int, mode SEICountPerFrame) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SendSEIMessage(streamIndex, message, repeatCount, mode)
}
func (engine *rtcEngineImp) StartFileRecording(steamIndex StreamIndex, config RecordingConfig, recordingType RecordingType) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeRecordingConfig := toNativeRecordingConfig(config)
	defer deleteNativeRecordingConfig(nativeRecordingConfig)

	return engine.nativeEngine.StartFileRecording(steamIndex, nativeRecordingConfig, recordingType)
}
func (engine *rtcEngineImp) StopFileRecording(steamType StreamIndex) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.StopFileRecording(steamType)
}
func (engine *rtcEngineImp) StartAudioRecording(config AudioRecordingConfig) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeConfig := toNativeAudioRecordingConfig(config)
	defer deleteNativeAudioRecordingConfig(nativeConfig)

	return engine.nativeEngine.StartAudioRecording(nativeConfig)
}

func (engine *rtcEngineImp) CreateRTCRoom(roomID string) IRTCRoom {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return nil
	}
	room := engine.nativeEngine.CreateRTCRoom(roomID)
	return &rtcRoomImpl{nativeRoom: room}
}

func (engine *rtcEngineImp) RegisterAudioFrameObserver(observer IAudioFrameObserver) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}

	if observer == nil {
		return engine.nativeEngine.RegisterAudioFrameObserver(nil)
	}

	nativeAudioFrameObserver := native.NewDirectorIAudioFrameObserver(
		&nativeIAudioFrameObserverImp{
			GoHandler: observer,
		},
	)
	return engine.nativeEngine.RegisterAudioFrameObserver(nativeAudioFrameObserver)
}

func (engine *rtcEngineImp) EnableAudioFrameCallback(method AudioFrameCallbackMethod, format *AudioFormat) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeAudioFormat := toNativeAudioFormat(format)
	defer deleteNativeAudioFormat(nativeAudioFormat)
	return engine.nativeEngine.EnableAudioFrameCallback(method, nativeAudioFormat)
}

func (engine *rtcEngineImp) SetAudioSourceType(sourceType AudioSourceType) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetAudioSourceType(sourceType)
}

func BuildAudioFrame(b AudioFrameBuilder) IAudioFrame {
	nativeBuilder := native.NewAudioFrameBuilder()
	nativeBuilder.SetSample_rate(b.SampleRate)
	nativeBuilder.SetChannel(b.Channel)
	nativeBuilder.SetTimestamp_us(b.TimestampUs)
	native.SetDataChunkToAudioFrameBuilder(
		nativeBuilder,
		b.Data,
	)
	nativeBuilder.SetData_size((int64)(len(b.Data)))
	nativeBuilder.SetDeep_copy(true)
	defer native.DeleteAudioFrameBuilder(nativeBuilder)

	nativeFrame := native.BuildAudioFrame(nativeBuilder)
	return newAudioFrameProxyImp(nativeFrame, true)
}

func BuildEncodedVideoFrame(builder *EncodedVideoFrameBuilder) IEncodedVideoFrame {
	nativeBuilder := toNativeEncodedVideoFrameBuilder(builder)
	defer deleteNativeEncodedVideoFrameBuilder(nativeBuilder)
	nativeFrame := native.BuildEncodedVideoFrame(nativeBuilder)
	proxy := newEncodedVideoFrameProxyImp(nativeFrame, true)
	return proxy
}

func (engine *rtcEngineImp) PushExternalAudioFrame(audioFrame IAudioFrame) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	proxyImp, ok := audioFrame.(*audioFrameProxyImp)
	if !ok {
		panic("bad inputs audioFrame")
	}
	return engine.nativeEngine.PushExternalAudioFrame(proxyImp.toNativeAudioFrame())
}

func (engine *rtcEngineImp) StopAudioRecording() int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.StopAudioRecording()
}
func (engine *rtcEngineImp) SetRuntimeParameters(jsonString string) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetRuntimeParameters(jsonString)
}
func (engine *rtcEngineImp) Feedback(feedbackType uint64, info *ProblemFeedbackInfo) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeInfo := toNativeProblemFeedbackInfo(info)
	defer deleteNativeProblemFeedbackInfo(nativeInfo)
	return engine.nativeEngine.Feedback(feedbackType, nativeInfo)
}
func (engine *rtcEngineImp) Login(token string, uid string) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.Login(token, uid)
}
func (engine *rtcEngineImp) Logout() int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.Logout()
}
func (engine *rtcEngineImp) UpdateLoginToken(token string) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.UpdateLoginToken(token)
}
func (engine *rtcEngineImp) SetServerParams(signature string, url string) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetServerParams(signature, url)
}
func (engine *rtcEngineImp) GetPeerOnlineStatus(peerUserId string) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.GetPeerOnlineStatus(peerUserId)
}
func (engine *rtcEngineImp) SendUserMessageOutsideRoom(uid string, message string, config MessageConfig) int64 {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SendUserMessageOutsideRoom(uid, message, config)
}
func (engine *rtcEngineImp) SendUserBinaryMessageOutsideRoom(uid string, message []byte, config MessageConfig) int64 {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SendUserBinaryMessageOutsideRoom(uid, message, config)
}
func (engine *rtcEngineImp) SendServerMessage(message string) int64 {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SendServerMessage(message)
}
func (engine *rtcEngineImp) SendServerBinaryMessage(message []byte) int64 {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SendServerBinaryMessage(message)
}
func (engine *rtcEngineImp) StartNetworkDetection(isTestUplink bool, expectedUplinkBitrate int, isTestDownlink bool, expectedDownlinkBiterate int) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.StartNetworkDetection(isTestUplink, expectedUplinkBitrate, isTestDownlink, expectedDownlinkBiterate)
}
func (engine *rtcEngineImp) StopNetworkDetection() int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.StopNetworkDetection()
}
func (engine *rtcEngineImp) SetScreenAudioSourceType(sourceType AudioSourceType) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetScreenAudioSourceType(sourceType)
}
func (engine *rtcEngineImp) SetScreenAudioStreamIndex(index StreamIndex) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.SetScreenAudioStreamIndex(index)
}
func (engine *rtcEngineImp) PushScreenAudioFrame(frame IAudioFrame) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	proxyImp, ok := frame.(*audioFrameProxyImp)
	if !ok {
		panic("bad inputs audioFrame")
	}
	return engine.nativeEngine.PushScreenAudioFrame(proxyImp.toNativeAudioFrame())
}

func (engine *rtcEngineImp) EnableAudioPropertiesReport(config *AudioPropertiesConfig) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeConfig := ToNativeAudioPropertiesConfig(config)
	defer deleteNativeAudioPropertiesConfig(nativeConfig)
	return engine.nativeEngine.EnableAudioPropertiesReport(nativeConfig)
}
func (engine *rtcEngineImp) RegisterLocalEncodedVideoFrameObserver(observer ILocalEncodedVideoFrameObserver) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeLocalEncodedVideoFrameObserver := native.NewDirectorILocalEncodedVideoFrameObserver(
		&nativeILocalEncodedVideoFrameObserver{
			goHandler: observer,
		},
	)
	return engine.nativeEngine.RegisterLocalEncodedVideoFrameObserver(nativeLocalEncodedVideoFrameObserver)
}
func (engine *rtcEngineImp) RegisterRemoteEncodedVideoFrameObserver(observer IRemoteEncodedVideoFrameObserver) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeLocalEncodedVideoFrameObserver := native.NewDirectorIRemoteEncodedVideoFrameObserver(
		&nativeIRemoteEncodedVideoFrameObserver{
			goHandler: observer,
		},
	)
	return engine.nativeEngine.RegisterRemoteEncodedVideoFrameObserver(nativeLocalEncodedVideoFrameObserver)
}
func (engine *rtcEngineImp) SetExternalVideoEncoderEventHandler(encoderHandler IExternalVideoEncoderEventHandler) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeEncoderHandler := native.NewDirectorIExternalVideoEncoderEventHandler(
		&nativeIExternalVideoEncoderEventHandlerImpl{
			goHandler: encoderHandler,
		},
	)
	return engine.nativeEngine.SetExternalVideoEncoderEventHandler(nativeEncoderHandler)
}
func (engine *rtcEngineImp) PushExternalEncodedVideoFrame(streamIndex StreamIndex, videoIndex int, videoStream IEncodedVideoFrame) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	proxyImp, ok := videoStream.(*encodedVideoFrameProxyImp)
	if !ok {
		panic("bad inputs videoFrame")
	}
	return engine.nativeEngine.PushExternalEncodedVideoFrame(streamIndex, videoIndex, proxyImp.toNativeEncodedVideoFrame())
}
func (engine *rtcEngineImp) SetVideoDecoderConfig(key *RemoteStreamKey, config VideoDecoderConfig) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeKey := toNativeRemoteStreamKey(key)
	defer deleteNativeRemoteStreamKey(nativeKey)
	return engine.nativeEngine.SetVideoDecoderConfig(nativeKey, config)
}
func (engine *rtcEngineImp) RequestRemoteVideoKeyFrame(streamInfo *RemoteStreamKey) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeStreamInfo := toNativeRemoteStreamKey(streamInfo)
	defer deleteNativeRemoteStreamKey(nativeStreamInfo)

	return engine.nativeEngine.RequestRemoteVideoKeyFrame(nativeStreamInfo)
}
func (engine *rtcEngineImp) SendStreamSyncInfo(data []byte, config *StreamSycnInfoConfig) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeConfig := toNativeStreamSycnInfoConfig(config)
	defer deleteNativeStreamSycnInfoConfig(nativeConfig)
	return engine.nativeEngine.SendStreamSyncInfo(data, nativeConfig)
}
func (engine *rtcEngineImp) StartCloudProxy(configuration *CloudProxyConfiguration) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeConfiguration := toNativeCloudProxyConfiguration(configuration)
	defer deleteNativeCloudProxyConfiguration(nativeConfiguration)
	return engine.nativeEngine.StartCloudProxy(nativeConfiguration)
}
func (engine *rtcEngineImp) StopCloudProxy() int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.StopCloudProxy()
}
func (engine *rtcEngineImp) SetLocalProxy(configurations []LocalProxyConfiguration) int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	nativeConfigurationArray := toNativeLocalProxyConfigurationArray(configurations)
	defer deleteNativeLocalProxyConfigurationArray(nativeConfigurationArray, len(configurations))

	return engine.nativeEngine.SetLocalProxy(nativeConfigurationArray, len(configurations))
}

func (engine *rtcEngineImp) GetVideoDeviceManager() IVideoDeviceManager {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return nil
	}
	return ivideoDeviceManagerProxyImpl{
		native: engine.nativeEngine.GetVideoDeviceManager(),
	}
}

func (engine *rtcEngineImp) StopVideoCapture() int {
	engine.engineAPILock.RLock()
	defer engine.engineAPILock.RUnlock()
	if engine.nativeEngine == nil {
		return 0
	}
	return engine.nativeEngine.StopVideoCapture()
}
