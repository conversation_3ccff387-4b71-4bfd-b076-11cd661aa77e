package rtcengine

import (
	"sync"
	"testing"
)

const (
	appID           = "641430cb525fcd0124277b6e"
	userID          = "linuxgo"
	roomToken       = ""
	roomID          = "gosdktest"
	testRepeatCount = 10
)

type testRTCEngineEventHandler struct {
	IRTCEngineEventHandlerDefaultImp
	t *testing.T
}

func (h *testRTCEngineEventHandler) OnExtensionAccessError(extensionName string, msg string) {
	// h.t.Logf("RTCEngineEventHandler::onExtensionAccessError, extension_name: %s, msg: %s", extensionName, msg)
}
func (h *testRTCEngineEventHandler) OnSysStats(stats SysStats) {
	// h.t.Log("RTCEngineEventHandler::onSysStats", stats)
}

func initEngineConfig() *EngineConfig {
	engineParameters := make(map[string]interface{})
	engineParameters["rtc.env"] = 2 // TODO: hidden internal
	engineParameters["config_hosts"] = []string{"rtc-test.bytedance.com"}
	engineParameters["access_hosts"] = []string{"rtc-pre-access.bytedance.com", "rtc-pre-access-sg.bytevcloud.com", "rtc-pre-access-va.bytevcloud.com"}
	engineConfig := &EngineConfig{AppID: appID, Parameters: engineParameters}
	return engineConfig
}

func assertGlobalEngineInstanceIsNil(t *testing.T) {
	t.Helper()
	if engineInstance != nil {
		t.Fatalf("global engine instance expect nil")
	}
}

// 单实例引擎
// 1. 多次 createRTCEngine 返回同一引擎对象
// 2. 多线程 createRTCEngine 返回同一引擎对象
// 3. 多线程销毁 destroyRTCEngine、createRTCEngine 无崩溃
// 4. 多线程调用引擎 API 与 destroyRTCEngine 无崩溃
// 5. 重复调用销毁引擎 无崩溃
// 6. 销毁引擎后调用接口 无崩溃
// 7. 先销毁 EventHandler, 后销毁引擎无崩溃
// 8. 回调中调用销毁引擎

// 多次 createRTCEngine 返回同一引擎对象
func TestRepeatCreateEngine(t *testing.T) {
	assertGlobalEngineInstanceIsNil(t)

	engines := make([]IRTCEngine, testRepeatCount)
	for i := 0; i < testRepeatCount; i++ {
		engines[i] = CreateRTCEngine(initEngineConfig(), &testRTCEngineEventHandler{t: t})
		if engineInstance == nil {
			t.Fatalf("create engine failed")
		}
	}

	engine0 := engines[0]
	for _, engine := range engines {
		if engine != engine0 {
			t.Fatalf("engine instance expect same")
		}
	}

	DestroyRTCEngine()
	assertGlobalEngineInstanceIsNil(t)
}

// 多线程 createRTCEngine 返回同一引擎对象
func TestMultiThreadCreateEngine(t *testing.T) {
	assertGlobalEngineInstanceIsNil(t)

	engineConfig := initEngineConfig()
	engines := make([]IRTCEngine, testRepeatCount)
	var engineCreationWork sync.WaitGroup
	for i := 0; i < testRepeatCount; i++ {
		engineCreationWork.Add(1)
		go func(i int) {
			engines[i] = CreateRTCEngine(engineConfig, &testRTCEngineEventHandler{t: t})
			engineCreationWork.Done()
		}(i)
	}

	engineCreationWork.Wait()
	engine0 := engines[0]
	for _, engine := range engines {
		if engine != engine0 {
			t.Fatalf("engine instance expect same")
		}
	}

	DestroyRTCEngine()
	assertGlobalEngineInstanceIsNil(t)
}

// 多线程销毁 destroyRTCEngine、createRTCEngine 无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestMultiThreadCreateAndDestroy(t *testing.T) {
	assertGlobalEngineInstanceIsNil(t)

	engineConfig := initEngineConfig()
	engines := make([]IRTCEngine, testRepeatCount)
	var engineCreationWork sync.WaitGroup
	for i := 0; i < testRepeatCount; i++ {
		engineCreationWork.Add(2)
		go func(i int) {
			engines[i] = CreateRTCEngine(engineConfig, &testRTCEngineEventHandler{t: t})
			engineCreationWork.Done()
		}(i)
		go func() {
			DestroyRTCEngine()
			engineCreationWork.Done()
		}()
	}

	engineCreationWork.Wait()

	DestroyRTCEngine()
	assertGlobalEngineInstanceIsNil(t)
}

// 多线程调用引擎 API 与 destroyRTCEngine 无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestMultiThreadCallAndDestroy(t *testing.T) {
	assertGlobalEngineInstanceIsNil(t)

	engineConfig := initEngineConfig()
	engines := make([]IRTCEngine, testRepeatCount)
	var engineWork sync.WaitGroup
	for i := 0; i < testRepeatCount; i++ {
		engineWork.Add(2)
		go func(i int) {
			engines[i] = CreateRTCEngine(engineConfig, &testRTCEngineEventHandler{t: t})
			engines[i].CreateRTCRoom(roomID)
			engineWork.Done()
		}(i)
		go func() {
			DestroyRTCEngine()
			engineWork.Done()
		}()
	}

	engineWork.Wait()

	DestroyRTCEngine()
	assertGlobalEngineInstanceIsNil(t)
}

// 重复调用销毁引擎 无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestRepeatDestroyEngine(t *testing.T) {
	for i := 0; i < testRepeatCount; i++ {
		assertGlobalEngineInstanceIsNil(t)
		CreateRTCEngine(initEngineConfig(), &testRTCEngineEventHandler{t: t})
		for i := 0; i < testRepeatCount; i++ {
			DestroyRTCEngine()
		}
	}
	assertGlobalEngineInstanceIsNil(t)
}

// 销毁引擎后调用接口 无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestCallAfterDestroy(t *testing.T) {
	for i := 0; i < testRepeatCount; i++ {
		assertGlobalEngineInstanceIsNil(t)
		engine := CreateRTCEngine(initEngineConfig(), &testRTCEngineEventHandler{t: t})
		DestroyRTCEngine()
		engine.CreateRTCRoom(roomID)
	}
	assertGlobalEngineInstanceIsNil(t)
}

// 先销毁 EventHandler, 后销毁引擎无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestDestroyEventHandler(t *testing.T) {
	for i := 0; i < testRepeatCount; i++ {
		assertGlobalEngineInstanceIsNil(t)
		handler := &testRTCEngineEventHandler{t: t}
		engine := CreateRTCEngine(initEngineConfig(), handler)
		handler = nil
		engine.CreateRTCRoom(roomID)
		DestroyRTCEngine()
	}
	assertGlobalEngineInstanceIsNil(t)
}

type testRTCRoomEventHandler struct {
	IRTCRoomEventHandlerDefaultImpl
	TestDestroyWork sync.WaitGroup
	t               *testing.T
}

func (h *testRTCRoomEventHandler) OnRoomStateChangedWithReason(roomId string, uid string, state RoomState, reason RoomStateChangeReason) {
	// h.t.Logf("RTCRoomEventHandler::onRoomStateChangedWithReason, room_id: %s, uid: %s, state: %d, reason: %d", roomId, uid, state, reason)
	DestroyRTCEngine()
	h.TestDestroyWork.Done()
}

// 回调中调用销毁引擎
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestDestroyOnCallback(t *testing.T) {
	for i := 0; i < testRepeatCount; i++ {
		assertGlobalEngineInstanceIsNil(t)
		engine := CreateRTCEngine(initEngineConfig(), &testRTCEngineEventHandler{t: t})
		handler := &testRTCRoomEventHandler{t: t}
		handler.TestDestroyWork.Add(1)
		room := engine.CreateRTCRoom(roomID)
		room.SetRTCRoomEventHandler(handler)
		room.JoinRoom(roomToken, &UserInfo{UID: userID}, &RTCRoomConfig{})
		handler.TestDestroyWork.Wait()
	}
	assertGlobalEngineInstanceIsNil(t)
}

// 多实例引擎
// 1. 多次 createRTCEngineMulti 返回不同引擎对象
// 4. 多线程调用引擎 API 与 destroyRTCEngineMulti 无崩溃
// 5. 重复调用销毁引擎 无崩溃
// 6. 销毁引擎后调用接口 无崩溃
// 7. 先销毁 EventHandler, 后销毁引擎无崩溃
// 8. 回调中调用销毁引擎

func TestRepeatCreateEngineMulti(t *testing.T) {
	engines := make([]IRTCEngine, testRepeatCount)
	for i := 0; i < testRepeatCount; i++ {
		engines[i] = CreateRTCEngineMulti(initEngineConfig(), &testRTCEngineEventHandler{t: t})
		if engines[i].(*rtcEngineImp).nativeEngine == nil {
			t.Fatalf("create engine failed")
		}
	}

	for i := 0; i < testRepeatCount-1; i++ {
		for j := i + 1; j < testRepeatCount; j++ {
			if engines[i] == engines[j] {
				t.Fatalf("multi engine instance expect not same")
			}
		}
	}

	for _, engine := range engines {
		DestroyRTCEngineMulti(engine)
		if engine.(*rtcEngineImp).nativeEngine != nil {
			t.Fatalf("destroy engine failed")
		}
	}
}

// 多线程调用引擎 API 与 destroyRTCEngine 无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestMultiThreadCallAndDestroyMulti(t *testing.T) {
	engineConfig := initEngineConfig()
	engines := make([]IRTCEngine, testRepeatCount)
	var engineWork sync.WaitGroup
	for i := 0; i < testRepeatCount; i++ {
		engineWork.Add(2)
		engines[i] = CreateRTCEngineMulti(engineConfig, &testRTCEngineEventHandler{t: t})

		if i%2 == 0 {
			go func(i int) {
				engines[i].CreateRTCRoom(roomID)
				engineWork.Done()
			}(i)
			go func(i int) {
				DestroyRTCEngineMulti(engines[i])
				engineWork.Done()
			}(i)
		} else {
			go func(i int) {
				DestroyRTCEngineMulti(engines[i])
				engineWork.Done()
			}(i)
			go func(i int) {
				engines[i].CreateRTCRoom(roomID)
				engineWork.Done()
			}(i)
		}
	}

	engineWork.Wait()
	for _, engine := range engines {
		DestroyRTCEngineMulti(engine)
		if engine.(*rtcEngineImp).nativeEngine != nil {
			t.Fatalf("destroy engine failed")
		}
	}
}

// 重复调用销毁引擎 无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestRepeatDestroyEngineMulti(t *testing.T) {
	for i := 0; i < testRepeatCount; i++ {
		engine := CreateRTCEngineMulti(initEngineConfig(), &testRTCEngineEventHandler{t: t})
		for i := 0; i < testRepeatCount; i++ {
			DestroyRTCEngineMulti(engine)
		}
	}
}

// 销毁引擎后调用接口 无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestCallAfterDestroyMulti(t *testing.T) {
	for i := 0; i < testRepeatCount; i++ {
		engine := CreateRTCEngineMulti(initEngineConfig(), &testRTCEngineEventHandler{t: t})
		DestroyRTCEngineMulti(engine)
		engine.CreateRTCRoom(roomID)
	}
}

// 先销毁 EventHandler, 后销毁引擎无崩溃
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestDestroyEventHandlerMulti(t *testing.T) {
	for i := 0; i < testRepeatCount; i++ {
		handler := &testRTCEngineEventHandler{t: t}
		engine := CreateRTCEngineMulti(initEngineConfig(), handler)
		handler = nil
		engine.CreateRTCRoom(roomID)
		DestroyRTCEngineMulti(engine)
	}
}

type testRTCRoomEventHandlerForMultiEngine struct {
	IRTCRoomEventHandlerDefaultImpl
	TestDestroyWork sync.WaitGroup
	t               *testing.T
	engine          IRTCEngine
}

func (h *testRTCRoomEventHandlerForMultiEngine) OnRoomStateChangedWithReason(roomId string, uid string, state RoomState, reason RoomStateChangeReason) {
	// h.t.Logf("RTCRoomEventHandler::onRoomStateChangedWithReason, room_id: %s, uid: %s, state: %d, reason: %d", roomId, uid, state, reason)
	DestroyRTCEngineMulti(h.engine)
	h.TestDestroyWork.Done()
}

// 回调中调用销毁引擎
// NOTE: 用例耗时较长(每次循环耗时 2s)
func TestDestroyOnCallbackMulti(t *testing.T) {
	for i := 0; i < testRepeatCount; i++ {
		engine := CreateRTCEngineMulti(initEngineConfig(), &testRTCEngineEventHandler{t: t})
		handler := &testRTCRoomEventHandlerForMultiEngine{t: t, engine: engine}
		handler.TestDestroyWork.Add(1)
		room := engine.CreateRTCRoom(roomID)
		room.SetRTCRoomEventHandler(handler)
		room.JoinRoom(roomToken, &UserInfo{UID: userID}, &RTCRoomConfig{})
		handler.TestDestroyWork.Wait()
		if engine.(*rtcEngineImp).nativeEngine != nil {
			t.Fatalf("destroy engine failed")
		}
	}
}
