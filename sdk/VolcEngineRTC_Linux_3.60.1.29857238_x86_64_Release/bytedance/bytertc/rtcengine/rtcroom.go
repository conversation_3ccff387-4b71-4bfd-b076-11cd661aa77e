package rtcengine

import (
	"bytedance/bytertc/native"
	"sync"
)

type IRTCRoom interface {
	Destroy()
	SetUserVisibility(enable bool) int
	SetRTCRoomEventHandler(roomEventHandler IRTCRoomEventHandler) int
	JoinRoom(token string, userInfo *UserInfo, roomConfig *RTCRoomConfig) int
	LeaveRoom() int
	UpdateToken(token string) int
	SendUserMessage(uid string, message string, config MessageConfig) int64
	SendUserMessageWithReliableOrdered(uid string, message string) int64
	SendUserBinaryMessage(uid string, message []byte, config MessageConfig) int64
	SendRoomMessage(message string) int64
	SendRoomBinaryMessage(message []byte) int64
	PublishStreamVideo(publish bool) int
	PublishStreamAudio(publish bool) int
	SubscribeStreamVideo(userID string, subscribe bool) int
	SubscribeStreamAudio(userID string, subscribe bool) int
	SetRemoteSimulcastStreamType(userID string, streamType SimulcastStreamType) int
	SubscribeAllStreams(streamType MediaStreamType) int
	UnsubscribeAllStreams(streamType MediaStreamType) int
	PauseAllSubscribedStream(mediaType PauseResumeControlMediaType) int
	ResumeAllSubscribedStream(mediaType PauseResumeControlMediaType) int
	SetMultiDeviceAVSync(audioUserID string) int
	StartSubtitle(subtitleConfig *SubtitleConfig) int
	StopSubtitle() int
	SetRoomExtraInfo(key string, value string) int64
}

type rtcRoomImpl struct {
	nativeRoom native.IRTCRoom
	roomAPILock sync.RWMutex
}

func (room *rtcRoomImpl) SetRTCRoomEventHandler(roomEventHandler IRTCRoomEventHandler) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	nativeRoomHandler := native.NewDirectorIRTCRoomEventHandler(&nativeIRTCRoomEventHandlerImp{
		GoHandler: roomEventHandler,
	})
	return room.nativeRoom.SetRTCRoomEventHandler(nativeRoomHandler)
}

func (room *rtcRoomImpl) JoinRoom(token string, userInfo *UserInfo, roomConfig *RTCRoomConfig) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	nativeUserInfo := toNativeUserInfo(userInfo)
	defer deleteNativeUserInfo(nativeUserInfo)

	nativeRoomConfig := toNativeRTCRoomConfig(roomConfig)
	defer deleteNativeRTCRoomConfig(nativeRoomConfig)

	return room.nativeRoom.JoinRoom(token, nativeUserInfo, nativeRoomConfig)
}

func (room *rtcRoomImpl) LeaveRoom() int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.LeaveRoom()
}

func (room *rtcRoomImpl) Destroy() {
	room.roomAPILock.Lock()
	nativeRoom := room.nativeRoom
	room.nativeRoom = nil
	room.roomAPILock.Unlock()

	if nativeRoom != nil {
		nativeRoom.Destroy()
	}
}

func (room *rtcRoomImpl) SetRoomExtraInfo(key string, value string) int64 {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SetRoomExtraInfo(key, value)
}

func (room *rtcRoomImpl) UpdateToken(token string) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.UpdateToken(token)
}

func (room *rtcRoomImpl) SetUserVisibility(enable bool) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SetUserVisibility(enable)
}

func (room *rtcRoomImpl) SendUserMessage(uid string, message string, config MessageConfig) int64 {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SendUserMessage(uid, message, config)
}

func (room *rtcRoomImpl) SendUserMessageWithReliableOrdered(uid string, message string) int64 {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SendUserMessage(uid, message, MessageConfigReliableOrdered)
}

func (room *rtcRoomImpl) SendUserBinaryMessage(uid string, message []byte, config MessageConfig) int64 {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SendUserBinaryMessage(uid, message, config)
}

func (room *rtcRoomImpl) SendRoomMessage(message string) int64 {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SendRoomMessage(message)
}

func (room *rtcRoomImpl) SendRoomBinaryMessage(message []byte) int64 {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SendRoomBinaryMessage(message)
}

func (room *rtcRoomImpl) PublishStreamVideo(publish bool) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.PublishStreamVideo(publish)
}

func (room *rtcRoomImpl) PublishStreamAudio(publish bool) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.PublishStreamAudio(publish)
}

func (room *rtcRoomImpl) SubscribeStreamVideo(userID string, subscribe bool) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SubscribeStreamVideo(userID, subscribe)
}

func (room *rtcRoomImpl) SubscribeStreamAudio(userID string, subscribe bool) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SubscribeStreamAudio(userID, subscribe)
}

func (room *rtcRoomImpl) SetRemoteSimulcastStreamType(userID string, streamType SimulcastStreamType) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SetRemoteSimulcastStreamType(userID, streamType)
}

func (room *rtcRoomImpl) SubscribeAllStreams(streamType MediaStreamType) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SubscribeAllStreams(streamType)
}

func (room *rtcRoomImpl) UnsubscribeAllStreams(streamType MediaStreamType) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.UnsubscribeAllStreams(streamType)
}

func (room *rtcRoomImpl) PauseAllSubscribedStream(mediaType PauseResumeControlMediaType) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.PauseAllSubscribedStream(mediaType)
}

func (room *rtcRoomImpl) ResumeAllSubscribedStream(mediaType PauseResumeControlMediaType) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.ResumeAllSubscribedStream(mediaType)
}

func (room *rtcRoomImpl) SetMultiDeviceAVSync(audioUserID string) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.SetMultiDeviceAVSync(audioUserID)
}

func (room *rtcRoomImpl) StartSubtitle(subtitleConfig *SubtitleConfig) int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	nativeSubtitleConfig := toNativeSubtitleConfig(subtitleConfig)
	defer deleteNativeSubtitleConfig(nativeSubtitleConfig)
	return room.nativeRoom.StartSubtitle(nativeSubtitleConfig)
}

func (room *rtcRoomImpl) StopSubtitle() int {
	room.roomAPILock.RLock()
	defer room.roomAPILock.RUnlock()
	return room.nativeRoom.StopSubtitle()
}
