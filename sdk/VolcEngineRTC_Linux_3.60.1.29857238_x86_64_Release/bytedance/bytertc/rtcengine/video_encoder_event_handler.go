package rtcengine

import "bytedance/bytertc/native"

type IExternalVideoEncoderEventHandler interface {
	OnStart(index StreamIndex)
	OnStop(index StreamIndex)
	OnRateUpdate(index StreamIndex, videoIndex int, info *VideoRateInfo)
	OnRequestKeyFrame(index StreamIndex, videoIndex int)
	OnActiveVideoLayer(index StreamIndex, videoIndex int, active bool)
}

type ExternalVideoEncoderEventHandlerDefaultImpl struct {
	IExternalVideoEncoderEventHandler
}

func (h *ExternalVideoEncoderEventHandlerDefaultImpl) OnStart(index StreamIndex) {}
func (h *ExternalVideoEncoderEventHandlerDefaultImpl) OnStop(index StreamIndex)  {}
func (h *ExternalVideoEncoderEventHandlerDefaultImpl) OnRateUpdate(index StreamIndex, videoIndex int, info *VideoRateInfo) {
}
func (h *ExternalVideoEncoderEventHandlerDefaultImpl) OnRequestKeyFrame(index StreamIndex, videoIndex int) {
}
func (h *ExternalVideoEncoderEventHandlerDefaultImpl) OnActiveVideoLayer(index StreamIndex, videoIndex int, active bool) {
}

type nativeIExternalVideoEncoderEventHandlerImpl struct {
	goHandler IExternalVideoEncoderEventHandler
}

func (h *nativeIExternalVideoEncoderEventHandlerImpl) OnStart(arg2 int) {
	h.goHandler.OnStart(arg2)
}
func (h *nativeIExternalVideoEncoderEventHandlerImpl) OnStop(arg2 int) {
	h.goHandler.OnStop(arg2)
}
func (h *nativeIExternalVideoEncoderEventHandlerImpl) OnRateUpdate(arg2 int, arg3 int, arg4 native.VideoRateInfo) {
	rateInfo := toVideoRateInfo(arg4)
	h.goHandler.OnRateUpdate(arg2, arg3, &rateInfo)
}
func (h *nativeIExternalVideoEncoderEventHandlerImpl) OnRequestKeyFrame(arg2 int, arg3 int) {
	h.goHandler.OnRequestKeyFrame(arg2, arg3)
}
func (h *nativeIExternalVideoEncoderEventHandlerImpl) OnActiveVideoLayer(arg2 int, arg3 int, arg4 bool) {
	h.goHandler.OnActiveVideoLayer(arg2, arg3, arg4)
}
