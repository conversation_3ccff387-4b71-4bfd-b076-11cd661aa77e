package rtcengine

import (
	"bytedance/bytertc/native"
	"unsafe"
)

// func BuildIVideoFrame(data *VideoFrameData) IVideoFrame {
// 	nativeVData := toNativeVideoFrameData(data)
// 	nativeFrame := native.BuildVideoFrame(nativeVData)
// 	return videoFrameImp{nativeFrame}
// }

type VideoFrameData struct {
	BufferType     VideoBufferType
	PixelFormat    VideoPixelFormat
	ContentType    VideoContentType
	NumberOfPlanes int
	PlaneData      [4][]byte
	PlaneStride    [4]int
	SeiData        []byte
	RoiData        []byte

	Width         int
	Height        int
	Rotation      VideoRotation
	TimestampUs   int64
	HwBuffer      uintptr
	HwContext     uintptr
	TextureMatrix [16]float32
	TextureId     uint
}

type IVideoFrame interface {
	BufferType() VideoBufferType
	PixelFormat() VideoPixelFormat
	ContentType() VideoContentType
	TimestampUs() int64
	Width() int
	Height() int
	Rotation() VideoRotation
	NumberOfPlanes() int
	PlaneData(planeIndex int) []byte
	PlaneStride(planeIndex int) int
	SeiData(size int) []byte
	HwBuffer() uintptr
	HwContext() uintptr
	TextureMatrix(matrix [15]float32)
	TextureId() uint
	AddRef()
	ReleaseRef() int64
	CameraId() CameraID
	FovTileInfo() FovVideoTileInfo
}

type IVideoSink interface {
	OnFrame(videoFrame IVideoFrame) bool
	GetRenderElapse() int
	Release()
	UniqueId() uintptr
}

// TODO:(@tanghaoru) 暴露 defaultImpl 给用户
type videoSinkDefaultImp struct{}

func (h videoSinkDefaultImp) OnFrame(videoFrame IVideoFrame) bool {
	return true
}
func (h videoSinkDefaultImp) GetRenderElapse() int {
	return 0
}
func (h videoSinkDefaultImp) Release() {}
func (h videoSinkDefaultImp) UniqueId() uintptr {
	return uintptr(unsafe.Pointer(&h))
}

type videoFrameImp struct {
	native native.IVideoFrame
}

func (f videoFrameImp) BufferType() VideoBufferType {
	return f.native.BufferType()
}
func (f videoFrameImp) PixelFormat() VideoPixelFormat {
	return f.PixelFormat()
}
func (f videoFrameImp) ContentType() VideoContentType {
	return f.native.BufferType()
}
func (f videoFrameImp) TimestampUs() int64 {
	return f.native.TimestampUs()
}
func (f videoFrameImp) Width() int {
	return f.native.Width()
}
func (f videoFrameImp) Height() int {
	return f.native.Height()
}
func (f videoFrameImp) Rotation() VideoRotation {
	return f.native.Rotation()
}
func (f videoFrameImp) NumberOfPlanes() int {
	return f.native.NumberOfPlanes()
}
func (f videoFrameImp) PlaneData(planeIndex int) []byte {
	// explicitly copy the slice to decouple with the underlying AudioFrame lifetime
	originalData := native.GetPanelDataChunkFromIVideoFrame(f.native, planeIndex)
	data := make([]byte, len(originalData))
	copy(data, originalData)
	return data

}
func (f videoFrameImp) PlaneStride(planeIndex int) int {
	return f.native.PlaneStride(planeIndex)
}
func (f videoFrameImp) SeiData(size int) []byte {
	// explicitly copy the slice to decouple with the underlying AudioFrame lifetime
	originalData := native.GetSeiDataChunkFromIVideoFrame(f.native, size)
	data := make([]byte, len(originalData))
	copy(data, originalData)
	return data
}
func (f videoFrameImp) HwBuffer() uintptr {
	return f.native.HwBuffer()
}
func (f videoFrameImp) HwContext() uintptr {
	return f.native.HwContext()
}
func (f videoFrameImp) TextureMatrix(matrix [15]float32) {
	data := native.GetTextureMatrixDataFromIVideoFrame(f.native)
	for i := 0; i < 16; i++ {
		matrix[i] = data[i]
	}
	native.DeleteTheFloatArray(&data[0])
}
func (f videoFrameImp) TextureId() uint {
	return f.native.TextureId()
}
func (f videoFrameImp) AddRef() {
	f.native.AddRef()
}
func (f videoFrameImp) ReleaseRef() int64 {
	return f.native.ReleaseRef()
}
func (f videoFrameImp) CameraId() CameraID {
	return f.native.CameraId()
}
func (f videoFrameImp) FovTileInfo() FovVideoTileInfo {
	return toFovVideoTileInfo(f.native.FovTileInfo())
}
