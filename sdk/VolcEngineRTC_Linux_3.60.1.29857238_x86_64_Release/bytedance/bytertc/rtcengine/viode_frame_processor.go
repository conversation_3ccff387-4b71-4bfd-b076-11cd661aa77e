package rtcengine

import "bytedance/bytertc/native"

type IVideoProcessor interface {
	ProcessVideoFrame(srcFrame IVideoFrame) IVideoFrame
}

type IVideoProcessorDefaultImp struct {
}

func (p IVideoProcessorDefaultImp) ProcessVideoFrame(srcFrame IVideoFrame) IVideoFrame {
	return srcFrame
}

type nativeIVideoProcessorDefaultImp struct {
	GoHandler IVideoProcessor
}

func (p nativeIVideoProcessorDefaultImp) ProcessVideoFrame(srcFrame native.IVideoFrame) native.IVideoFrame {
	vf := p.GoHandler.ProcessVideoFrame(toVideoFrame(srcFrame))
	proxyImp, ok := vf.(*videoFrameImp)
	if !ok {
		panic("return value of <PERSON>VideoFrame is invalidate")
	}
	return proxyImp.native
}
