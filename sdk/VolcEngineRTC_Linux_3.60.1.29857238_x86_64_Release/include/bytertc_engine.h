/*
 * Copyright (c) 2020 The VolcEngineRTC project authors. All Rights Reserved.
 * @brief VolcEngineRTC Interface Lite
 */

#pragma once

#include "bytertc_room.h"
#include "rtc/bytertc_video_device_manager.h"
#include "rtc/bytertc_audio_frame.h"
#include "rtc/bytertc_audio_mixing_manager.h"
#include "rtc/bytertc_audio_effect_player.h"
#include "rtc/bytertc_media_player.h"
#include "rtc/bytertc_video_processor_interface.h"
#include "rtc/bytertc_camera_control_interface.h"
#include "bytertc_engine_event_handler.h"
#include "rtc/bytertc_sing_scoring_interface.h"
#include "rtc/bytertc_ktv_manager_interface.h"
#include "rtc/bytertc_wtn_stream_interface.h"

namespace bytertc {
/**
 * @locale zh
 * @type api
 * @brief 引擎 API
 * @list 
 */
/**
 * @locale en
 * @type api
 * @brief Engine API
 * @list 
 */
class IRTCEngine {
public:
    /**
     * @locale zh
     * @valid since 3.58.1
     * @type api
     * @brief 设置是否将录音信号静音（不改变本端硬件）。
     * @param index 流索引，指定调节主流/屏幕流音量，参看 StreamIndex{@link #StreamIndex}。
     * @param mute 是否静音音频采集。 <br>
     *        - True：静音（关闭麦克风）
     *        - False：（默认）开启麦克风
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。具体失败原因参看 ReturnStatus{@link #ReturnStatus}。
     * @note
     *        - 该方法用于设置是否使用静音数据替换设备采集到的音频数据进行推流，不影响 SDK 音频流的采集发布状态。对于 Windows 平台，如有需要你也可以选择静音整个系统的音频采集，具体参看 setAudioCaptureDeviceMute{@link #IAudioDeviceManager#setAudioCaptureDeviceMute} 方法说明。
     *        - 静音后通过 setCaptureVolume{@link #IRTCEngine#setCaptureVolume} 调整音量不会取消静音状态，音量状态会保存至取消静音。
     *        - 调用 startAudioCapture{@link #IRTCEngine#startAudioCapture} 开启音频采集前后，都可以使用此接口设置采集音量。
     * @list 音频管理
     */
    /**
     * @locale en
     * @valid since 3.58.1
     * @type api
     * @brief Set whether to mute the recording signal (without changing the local hardware).
     * @param index Stream index, specifying volume adjustment of the main stream or screen stream. See StreamIndex{@link #StreamIndex}.
     * @param mute Whether to mute audio capture. <br>
     *        - True: Mute (disable microphone)
     *        - False: (Default) Enable microphone
     * @return
     *        - 0: Success.
     *        - < 0 : Failure. See ReturnStatus{@link #ReturnStatus} for more details.
     * @note
     *        - Calling this API does not affect the status of SDK audio stream publishing.
     *        - Adjusting the volume by calling setCaptureVolume{@link #IRTCEngine#setCaptureVolume} after muting will not cancel the mute state. The volume state will be retained until unmuted.
     *        - You can use this interface to set the capture volume before or after calling startAudioCapture{@link #IRTCEngine#startAudioCapture} to enable audio capture.
     * @list Audio Management
     */
    virtual int muteAudioCapture(StreamIndex index, bool mute) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 调节音频采集音量
     * @param index 流索引，指定调节主流还是调节屏幕流的音量，参看 StreamIndex{@link #StreamIndex}
     * @param volume 采集的音量值和原始音量的比值，范围是 [0, 400]，单位为 %，自带溢出保护。 <br>
     *                    只改变音频数据的音量信息，不涉及本端硬件的音量调节。 <br>
     *        为保证更好的通话质量，建议将 volume 值设为 [0,100]。 <br>
     *       - 0：静音
     *       - 100：原始音量
     *       - 400: 最大可为原始音量的 4 倍(自带溢出保护)
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 在开启音频采集前后，你都可以使用此接口设定采集音量。
     * @list 音频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Adjust the audio capture volume
     * @param index Index of the stream, whose volume needs to be adjusted. Refer to StreamIndex{@link #StreamIndex} for more details.
     * @param volume Ratio of capture volume to original volume. Ranging: [0,400]. Unit: % <br>
     *                     This changes the volume property of the audio data other than the hardware volume. <br>
     *        - 0: Mute
     *        - 100: Original volume. To ensure the audio quality, we recommend [0, 100].
     *        - 400: Four times the original volume with signal-clipping protection.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note  Call this API to set the volume of the audio capture before or during the audio capture.
     * @list Audio Management
     */
    virtual int setCaptureVolume(StreamIndex index, int volume) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 调节本地播放的所有远端用户音频混音后的音量，混音内容包括远端人声、音乐、音效等。 <br>
     *        播放音频前或播放音频时，你都可以使用此接口设定播放音量。
     * @param volume 音频播放音量值和原始音量的比值，范围是 [0, 400]，单位为 %，自带溢出保护。 <br>
     *                    只改变音频数据的音量信息，不涉及本端硬件的音量调节。 <br>
     *        为保证更好的通话质量，建议将 volume 值设为 [0,100]。 <br>
     *       - 0: 静音
     *       - 100: 原始音量
     *       - 400: 最大可为原始音量的 4 倍(自带溢出保护)
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 假设某远端用户 A 始终在被调节的目标用户范围内，当该方法与 setRemoteAudioPlaybackVolume{@link #IRTCEngine#setRemoteAudioPlaybackVolume} 或 setRemoteRoomAudioPlaybackVolume{@link #IRTCRoom#setRemoteRoomAudioPlaybackVolume} 共同使用时，本地收听用户 A 的音量将为两次设置的音量效果的叠加。
     * @list 音频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Adjust the playback volume of the mixed remote audios.  You can call this API before or during the playback.
     * @param volume Ratio(%) of playback volume to original volume, in the range [0, 400], with overflow protection. <br>
     *                    This changes the volume property of the audio data other than the hardware volume. <br>
     *        To ensure the audio quality, we recommend setting the volume to `100`. <br>
     *        - 0: mute
     *        - 100: original volume
     *        - 400: Four times the original volume with signal-clipping protection.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note Suppose a remote user A is always within the range of the target user whose playback volume will be adjusted, if you use both this method and setRemoteAudioPlaybackVolume{@link #IRTCEngine#setRemoteAudioPlaybackVolume}/setRemoteRoomAudioPlaybackVolume{@link #IRTCRoom#setRemoteRoomAudioPlaybackVolume}, the volume that the local user hears from user A is the overlay of both settings.
     * @list Audio Management
     */
    virtual int setPlaybackVolume(const int volume) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 开启/关闭耳返功能
     * @param mode 是否开启耳返功能，参看 EarMonitorMode{@link #EarMonitorMode}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 耳返功能仅支持设备通过 3.5mm 接口、USB 接口、或蓝牙方式直连耳机时可以使用。对于通过 HDMI 或 USB-C 接口连接显示器，再连接，或通过连接 OTG 外接声卡再连接的耳机，不支持耳返功能。
     * @list 高级功能
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Turn on/off the earphone monitor function
     * @param mode Whether to turn on the earphone monitor function. See EarMonitorMode{@link #EarMonitorMode}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note You can use ear monitoring feature when the earpiece is directly connected to the device by 3.5mm interface, USB interface, or BlueTooth. You cannot use ear monitoring feature when the earpiece is connected to a monitor via the HDMI or USB-C interface, and then connected to the device, or connected to an OTG external sound card, and then connected to the device.
     * @list Advanced Features
     */
    virtual int setEarMonitorMode(EarMonitorMode mode) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 设置耳返的音量
     * @param volume 耳返的音量相对原始音量的比值，取值范围：[0,100]，单位：%
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 设置耳返音量前，你必须先调用 setEarMonitorMode{@link #IRTCEngine#setEarMonitorMode} 打开耳返功能。
     * @list 高级功能
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Set the volume of the earphone monitor
     * @param volume The volume of the earphone monitor, the value range: [0,100], the unit:%
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note Before setting the volume of the earphone monitor, you must first call setEarMonitorMode{@link #IRTCEngine#setEarMonitorMode} to turn on the earphone monitor function.
     * @list Advanced Features
     */
    virtual int setEarMonitorVolume(const int volume) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Linux,Android)
     * @type api
     * @brief 设置蓝牙模式
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 仅在媒体场景下生效。
     * @list 高级功能
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Linux,Android)
     * @type api
     * @brief  set bluetooh mode in media scenario
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note bluetooth mode can be setted only in media scenario.
     * @list Advanced Features
     */
    virtual int setBluetoothMode(BluetoothMode mode) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 开启内部音频采集。默认为关闭状态。 <br>
     *        内部采集是指：使用 RTC SDK 内置的音频采集机制进行音频采集。 <br>
     *        调用该方法开启后，本地用户会收到 onAudioDeviceStateChanged{@link #IRTCEngineEventHandler#onAudioDeviceStateChanged} 的回调。 <br>
     *        非隐身用户进房后调用该方法，房间中的其他用户会收到 onUserStartAudioCapture{@link #IRTCEngineEventHandler#onUserStartAudioCapture} 的回调。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 若未取得当前设备的麦克风权限，调用该方法后会触发 onAudioDeviceStateChanged{@link #IRTCEngineEventHandler#onAudioDeviceStateChanged} 回调，对应的错误码为 `MediaDeviceError.kMediaDeviceErrorDeviceNoPermission = 1`。
     *       - 调用 stopAudioCapture{@link #IRTCEngine#stopAudioCapture} 可以关闭音频采集设备，否则，SDK 只会在销毁引擎的时候自动关闭设备。
     *       - 由于不同硬件设备初始化响应时间不同，频繁调用 stopAudioCapture{@link #IRTCEngine#stopAudioCapture} 和本接口闭麦/开麦可能出现短暂无声问题，建议使用 publishStreamAudio{@link #IRTCRoom#publishStreamAudio} 实现临时闭麦和重新开麦。
     *       - 创建引擎后，无论是否发布音频数据，你都可以调用该方法开启音频采集，并且调用后方可发布音频。
     *       - 对于 Windows SDK，如果需要从自定义音频采集切换为内部音频采集，你必须先停止发布流，调用 setAudioSourceType{@link #IRTCEngine#setAudioSourceType} 关闭自定义采集，再调用此方法手动开启内部采集。
     * @list 音频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Start internal audio capture. The default is off. <br>
     *        Internal audio capture refers to: capturing audio using the built-in module. <br>
     *        The local client will be informed via onAudioDeviceStateChanged{@link #IRTCEngineEventHandler#onAudioDeviceStateChanged} after starting audio capture by calling this API. <br>
     *        The remote clients in the room will be informed of the state change via onUserStartAudioCapture{@link #IRTCEngineEventHandler#onUserStartAudioCapture} after the visible user starts audio capture by calling this API..
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - If the current device does not have permission to access the microphone, calling this method will trigger the `onAudioDeviceStateChanged` {@link #IRTCEngineEventHandler#onAudioDeviceStateChanged} callback, with the corresponding error code being `MediaDeviceError.kMediaDeviceErrorDeviceNoPermission = 1`.
     *        - Call stopAudioCapture{@link #IRTCEngine#stopAudioCapture} to stop the internal audio capture. Otherwise, the internal audio capture will sustain until you destroy the engine instance.
     *       -  To mute and unmute microphones, we recommend using publishStreamAudio{@link #IRTCRoom#publishStreamAudio}, other than stopAudioCapture{@link #IRTCEngine#stopAudioCapture} and this API. Because starting and stopping capture devices often need some time waiting for the response of the device, that may lead to a short silence during the communication.
     *        - Once you create the engine instance, you can start internal audio capture regardless of the audio publishing state. The audio stream will start publishing only after the audio capture starts.
     *        - To switch from custom to internal audio capture, stop publishing before disabling the custom audio capture module and then call this API to enable the internal audio capture.
     * @list Audio Management
     * @order 27
     */
    virtual int startAudioCapture() = 0;
    /**
     * @locale zh
     * @type api
     * @brief 立即关闭内部音频采集。默认为关闭状态。 <br>
     *        内部采集是指：使用 RTC SDK 内置的音频采集机制进行音频采集。 <br>
     *        调用该方法，本地用户会收到 onAudioDeviceStateChanged{@link #IRTCEngineEventHandler#onAudioDeviceStateChanged} 的回调。 <br>
     *        非隐身用户进房后调用该方法后，房间中的其他用户会收到 onUserStopAudioCapture{@link #IRTCEngineEventHandler#onUserStopAudioCapture} 的回调。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 调用 startAudioCapture{@link #startAudioCapture} 可以开启音频采集设备。
     *       - 如果不调用本方法停止内部视频采集，则只有当销毁引擎实例时，内部音频采集才会停止。
     * @list 音频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Stop internal audio capture. The default is off. <br>
     *        Internal audio capture refers to: capturing audio using the built-in module. <br>
     *         The local client will be informed via  onAudioDeviceStateChanged{@link #IRTCEngineEventHandler#onAudioDeviceStateChanged}  after stopping audio capture by calling this API.<br>. <br>
     *        The remote clients in the room will be informed of the state change via onUserStopAudioCapture{@link #IRTCEngineEventHandler#onUserStopAudioCapture} after the visible client stops audio capture by calling this API.<br>.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Call startAudioCapture{@link #startAudioCapture} to enable the internal audio capture.
     *        - Without calling this API the internal audio capture will sustain until you destroy the engine instance.
     * @list Audio Management
     */
    virtual int stopAudioCapture() = 0;

    /**
     * @locale zh
     * @hidden(macOS,Windows,Linux)
     * @valid since 3.60
     * @type api
     * @brief 设置音频场景类型。 <br>
     *        选择音频场景后，SDK 会自动根据场景切换对应的音量模式（通话音量/媒体音量）和改场景下的最佳音频配置。 <br>
     * @param scenario 音频场景类型，参看 AudioScenarioType{@link #AudioScenarioType}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 此接口在进房前后调用都有效。
     *        - 通话音量更适合通话、会议等对信息准确度更高的场景。通话音量会激活系统硬件信号处理，使通话声音更清晰。同时，音量无法降低到 0。
     *        - 媒体音量更适合娱乐场景，因其声音的表现力会更强。媒体音量下，最低音量可以为 0。
     * @list Audio Management
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Linux)
     * @valid since 3.60
     * @type api
     * @brief Sets the audio scenarios. <br>
     *        After selecting the audio scenario, SDK will automatically switch to the proper volume modes (the call/media volume) according to the scenarios and the best audio configurations under such scenarios.  <br>
     *        This API should not be used at the same time with the old one.
     * @param scenario  Audio scenarios. See AudioScenarioType{@link #AudioScenarioType}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - You can use this API both before and after joining the room.
     *         - Call volume is more suitable for calls, meetings and other scenarios that demand information accuracy. Call volume will activate the system hardware signal processor, making the sound clearer. The volume cannot be reduced to 0.
     *         - Media volume is more suitable for entertainment scenarios, which require musical expression. The volume can be reduced to 0.
     * @list Audio Management
     */
    virtual int setAudioScenario(AudioScenarioType scenario) = 0;

    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 设置变声特效类型
     * @param voice_changer 变声特效类型，参看 VoiceChangerType{@link #VoiceChangerType}。
     * @return 方法调用结果： <br>
     *        - 0：成功；
     *        - !0：失败。
     * @note
     *        - 如需使用该功能，需集成 SAMI 动态库，详情参看[按需集成插件](1108726)文档。
     *        - 在进房前后都可设置。
     *        - 对 RTC SDK 内部采集的音频和自定义采集的音频都生效。
     *        - 只对单声道音频生效。
     *        - 与 setVoiceReverbType{@link #setVoiceReverbType} 互斥，后设置的特效会覆盖先设置的特效。
     *        - 在 Windows 端使用此功能前，必须额外接入 [音频技术](https://www.volcengine.com/docs/6489/71986)。请联系技术支持了解详情。
     * @list 音频处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Set the sound change effect type
     * @param voice_changer The sound change effect type. See VoiceChangerType{@link #VoiceChangerType}.
     * @return API call result: <br>
     *        - 0: Success
     *        - !0: Failure
     * @note
     *         - To use this feature, you need to integrate the SAMI dynamic library. See [Integrate Plugins on Demand](docs-1108726).
     *         - You can call it before and after entering the room.
     *         - Effective for both internal and external audio source.
     *         - Only valid for mono-channel audio.
     *         - Mutually exclusive with setVoiceReverbType{@link #setVoiceReverbType}, and the effects set later will override the effects set first.
     *         - Before using the feature, you must implement [Audio SDK](https://go.byteplus.com/audio-sdk). Please contact the technical supporters.
     * @list Audio Processing
     */
    virtual int setVoiceChangerType(VoiceChangerType voice_changer) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 设置混响特效类型
     * @param voice_reverb 混响特效类型，参看 VoiceReverbType{@link #VoiceReverbType}。
     * @return 方法调用结果： <br>
     *        - 0：成功；
     *        - !0：失败。
     * @note
     *        - 在进房前后都可设置。
     *        - 对 RTC SDK 内部采集的音频和自定义采集的音频都生效。
     *        - 只对单声道音频生效。
     *        - 只在包含美声特效能力的 SDK 中有效。
     *        - 与 setVoiceChangerType{@link #setVoiceChangerType} 互斥，后设置的特效会覆盖先设置的特效。
     * @list 音频处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Set the reverb effect type
     * @param voice_reverb The reverb effect type. See VoiceReverbType{@link #VoiceReverbType}.
     * @return API call result: <br>
     *        - 0: Success
     *        - !0: Failure
     * @note
     *         - You can call it before and after entering the room.
     *         - Effective for both internal and external audio source.
     *         - Only valid for mono-channel audio.
     *         - Mutually exclusive with setVoiceChangerType{@link #setVoiceChangerType}, and the effects set later will override the effects set first.
     * @list Audio Processing
     */
    virtual int setVoiceReverbType(VoiceReverbType voice_reverb) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 设置本地采集语音的均衡效果。包含内部采集和外部采集，但不包含混音音频文件。
     * @param config 语音均衡效果，参看 VoiceEqualizationConfig{@link #VoiceEqualizationConfig}
     * @return
     *        - 0： 成功。
     *        - < 0： 失败。
     * @note 根据奈奎斯特采样率，音频采样率必须大于等于设置的中心频率的两倍，否则，设置不生效。
     * @list 音频处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Set the equalization effect for the local captured audio. The audio includes both internal captured audio and external captured voice, but not the mixing audio file.
     * @param config See VoiceEqualizationConfig{@link #VoiceEqualizationConfig}.
     * @return
     *         - 0: Success.
     *         - < 0: Failure.
     * @note According to the Nyquist acquisition rate, the audio acquisition rate must be greater than twice the set center frequency. Otherwise, the setting will not be effective.
     * @list Audio Processing
     */
    virtual int setLocalVoiceEqualization(VoiceEqualizationConfig config) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 设置本地采集音频的混响效果。包含内部采集和外部采集，但不包含混音音频文件。
     * @param param 混响效果，参看 VoiceReverbConfig{@link #VoiceReverbConfig}
     * @return
     *        - 0： 成功。
     *        - < 0： 失败。
     * @note 调用 enableLocalVoiceReverb{@link #IRTCEngine#enableLocalVoiceReverb} 开启混响效果。
     * @list 音频处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Set the reverb effect for the local captured audio. The audio includes both internal captured audio and external captured voice, but not the mixing audio file.
     * @param param See VoiceReverbConfig{@link #VoiceReverbConfig}.
     * @return
     *         - 0: Success.
     *         - < 0: Failure.
     * @note Call enableLocalVoiceReverb{@link #IRTCEngine#enableLocalVoiceReverb} to enable the reverb effect.
     * @list Audio Processing
     */
    virtual int setLocalVoiceReverbParam(VoiceReverbConfig param) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 开启本地音效混响效果
     * @param enable 是否开启
     * @return
     *        - 0： 成功。
     *        - < 0： 失败。
     * @note 调用 setLocalVoiceReverbParam{@link #IRTCEngine#setLocalVoiceReverbParam} 设置混响效果。
     * @list 音频处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Enable the reverb effect for the local captured voice.
     * @param enable
     * @return
     *         - 0: Success.
     *         - < 0: Failure.
     * @note Call setLocalVoiceReverbParam{@link #IRTCEngine#setLocalVoiceReverbParam} to set the reverb effect.
     * @list Audio Processing
     */
    virtual int enableLocalVoiceReverb(bool enable) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置音质档位。 <br>
     *        当所选的 RoomProfileType{@link #RoomProfileType} 中的音频参数无法满足你的场景需求时，调用本接口切换的音质档位。
     * @param audio_profile 音质档位，参看 AudioProfileType{@link #AudioProfileType}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 该方法在进房前后均可调用；
     *        - 支持通话过程中动态切换音质档位。
     * @list 音频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Sets the sound quality. Call this API to change the sound quality if the audio settings in the current RoomProfileType{@link #RoomProfileType} can not meet your requirements.
     * @param audio_profile Sound quality. See AudioProfileType{@link #AudioProfileType}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - You can call this API before and after entering the room.
     *         - Support dynamic switching of sound quality during a call.
     * @list Audio Management
     */
    virtual int setAudioProfile(AudioProfileType audio_profile) = 0;
    /**
     * @locale zh
     * @valid since 3.52
     * @type api
     * @brief 支持根据业务场景，设置通话中的音频降噪模式。
     * @param ans_mode 降噪模式。具体参见 AnsMode{@link #AnsMode}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 该接口进房前后均可调用，可重复调用，仅最后一次调用生效。
     *        - 降噪算法包含传统降噪和 AI 降噪。传统降噪主要是抑制平稳噪声，比如空调声、风扇声等。而 AI 降噪主要是抑制非平稳噪声，比如键盘敲击声、桌椅碰撞声等。
     *        - 只有以下 RoomProfileType{@link #RoomProfileType} 场景时，调用本接口可以开启 AI 降噪。其余场景的 AI 降噪不会生效。
     *                 -  游戏语音模式：kRoomProfileTypeGame
     *                 -  高音质游戏模式：kRoomProfileTypeGameHD
     *                 -  云游戏模式：kRoomProfileTypeCloudGame
     *                 -  1 vs 1 音视频通话：kRoomProfileTypeChat
     *                 -  多端同步播放音视频：kRoomProfileTypeLwTogether
     *                 -  云端会议中的个人设备：kRoomProfileTypeMeeting
     *                 -  课堂互动模式：kRoomProfileTypeClassroom
     *                 -  云端会议中的会议室终端：kRoomProfileTypeMeetingRoom
     * @list 高级功能
     */
    /**
     * @locale en
     * @valid since 3.52
     * @type api
     * @brief Set the Active Noise Cancellation(ANC) mode during audio and video communications.
     * @param ans_mode ANC modes. See AnsMode{@link #AnsMode}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note  - You can call this API before or after entering a room. When you repeatedly call it, only the last call takes effect. <br>
     *        The AI noise cancellation can be enabled by calling this interface only in the following RoomProfileType{@link #RoomProfileType} scenarios.
     *                - Game Voice: kRoomProfileTypeGame
     *                - High-Quality Game: kRoomProfileTypeGameHD
     *                - Cloud Gaming: kRoomProfileTypeCloudGame
     *                - 1 vs 1 Audio and Video Call: kRoomProfileTypeChat
     *                - Multi-End Synchronized Audio and Video Playback: kRoomProfileTypeLwTogether
     *                - Personal Device in Cloud Meeting: kRoomProfileTypeMeeting
     *                - Classroom Interaction: kRoomProfileTypeClassroom
     *                - Conference Room Terminals in Cloud Meetings: kRoomProfileTypeMeetingRoom
     */
    virtual int setAnsMode(AnsMode ans_mode) = 0;
    /**
     * @locale zh
     * @valid since 3.51
     * @type api
     * @brief 打开/关闭 AGC(Automatic Gain Control) 自动增益控制功能。 <br>
     *        开启该功能后，SDK 会自动调节麦克风的采集音量，确保音量稳定。
     * @param enable 是否打开 AGC 功能: <br>
     *        - true: 打开 AGC 功能。
     *        - false: 关闭 AGC 功能。
     * @return
     *        -  0: 调用成功。
     *        - -1: 调用失败。
     * @note
     *         该方法在进房前后均可调用。如果你需要在进房前使用 AGC 功能，请联系技术支持获得私有参数，传入对应 RoomProfileType{@link #RoomProfileType} 。 <br>
     *         要想在进房后开启 AGC 功能，你需要把 RoomProfileType{@link #RoomProfileType} 设置为 `kRoomProfileTypeMeeting` 、`kRoomProfileTypeMeetingRoom` 或 `kRoomProfileTypeClassroom` 。 <br>
     *         AGC 功能生效后，不建议再调用 setAudioCaptureDeviceVolume{@link #IAudioDeviceManager#setAudioCaptureDeviceVolume} 来调节设备麦克风的采集音量。
     * @list 音频管理
     */
    /**
     * @locale en
     * @valid since 3.51
     * @type api
     * @brief  Turns on/ off AGC(Analog Automatic Gain Control). <br>
     *         After AGC is enabled, SDK can automatically adjust microphone pickup volume to keep the output volume at a steady level.
     * @param enable whether to turn on AGC. <br>
     *        - true: AGC is turned on.
     *        - false: AGC is turned off.
     * @return
     *         - 0: Success.
     *         - -1: Failure.
     * @note
     *         You can call this method before and after joining the room. To turn on AGC before joining the room, you need to contact the technical support to get a private parameter to set RoomProfileType{@link #RoomProfileType}. <br>
     *         To enable AGC after joining the room, you must set RoomProfileType{@link #RoomProfileType} to `kRoomProfileTypeMeeting`,`kRoomProfileTypeMeetingRoom` or `kRoomProfileTypeClassroom` . <br>
     *         It is not recommended to call setAudioCaptureDeviceVolume{@link #IAudioDeviceManager#setAudioCaptureDeviceVolume} to adjust microphone pickup volume with AGC on.
     * @list Audio Management
     */
    virtual int enableAGC(bool enable) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 切换音频采集方式
     * @param type 音频数据源，详见 AudioSourceType{@link #AudioSourceType}。 <br>
     *             默认使用内部音频采集。音频采集和渲染方式无需对应。
     * @return 方法调用结果： <br>
     *        - >0: 切换成功。
     *        - -1：切换失败。
     * @note
     *      - 进房前后调用此方法均有效。
     *      - 如果你调用此方法由内部采集切换至自定义采集，SDK 会自动关闭内部采集。然后，调用 pushExternalAudioFrame{@link #IRTCEngine#pushExternalAudioFrame} 推送自定义采集的音频数据到 RTC SDK 用于传输。
     *      - 如果你调用此方法由自定义采集切换至内部采集，你必须再调用 startAudioCapture{@link #IRTCEngine#startAudioCapture} 手动开启内部采集。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Switch the audio capture type.
     * @param type Audio input source type. See AudioSourceType{@link #AudioSourceType} <br>
     *            Use internal audio capture by default. The audio capture type and the audio render type may be different from each other.
     * @return   Method call result: <br>
     *         - >0: Success.
     *         - -1: Failure.
     * @note
     *       - You can call this API before or after joining the room.
     *       - If you call this API to switch from internal audio capture to custom capture, the internal audio capture is automatically disabled. You must call pushExternalAudioFrame{@link #IRTCEngine#pushExternalAudioFrame} to push custom captured audio data to RTC SDK for transmission.
     *       - If you call this API to switch from custom capture to internal capture, you must then call startAudioCapture{@link #IRTCEngine#startAudioCapture} to enable internal capture.
     * @list Custom Stream Processing
     */
    virtual int setAudioSourceType (AudioSourceType type) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 切换音频渲染方式
     * @param type 音频输出类型，详见 AudioRenderType{@link #AudioRenderType} <br>
     *             默认使用内部音频渲染。音频采集和渲染方式无需对应。
     * @return 方法调用结果： <br>
     *        - >0: 切换成功。
     *        - -1：切换失败。
     * @note
     *      - 进房前后调用此方法均有效。
     *      - 如果你调用此方法切换至自定义渲染，调用 pullExternalAudioFrame{@link #IRTCEngine#pullExternalAudioFrame} 获取音频数据。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Switch the audio render type.
     * @param type Audio output source type. See AudioRenderType{@link #AudioRenderType}. <br>
     *             Use internal audio render by default. The audio capture type and the audio render type may be different from each other.
     * @return  Method call result: <br>
     *         - >0: Success.
     *         - -1: Failure.
     * @note
     *       - You can call this API before or after joining the room.
     *       - After calling this API to enable custom audio rendering, call pullExternalAudioFrame{@link #IRTCEngine#pullExternalAudioFrame} for audio data.
     * @list Custom Stream Processing
     */
    virtual int setAudioRenderType (AudioRenderType type) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 推送自定义采集的音频数据到 RTC SDK。
     * @param audio_frame 10 ms 对应的音频数据。详见 IAudioFrame{@link #IAudioFrame}。 <br>
     *        - 音频采样格式必须为 S16。音频缓冲区内的数据格式必须为 PCM，其容量大小应该为 audioFrame.samples × audioFrame.channel × 2。
     *        - 必须指定具体的采样率和声道数，不支持设置为自动。
     * @return 方法调用结果 <br>
     *        - 0：方法调用成功
     *        - < 0：方法调用失败
     * @note
     *       - 推送自定义采集的音频数据前，必须先调用 setAudioSourceType{@link #IRTCEngine#setAudioSourceType} 开启自定义采集。
     *       - 你必须每 10 ms 推送一次数据。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Push custom captured audio data to the RTC SDK.
     * @param audio_frame The audio data of 10 ms. See IAudioFrame{@link #IAudioFrame}。<br>
     *        - The audio sampling format must be S16. The data format within the audio buffer must be PCM, and its capacity should be audioFrame.samples × audioFrame.channel × 2.
     *        - A specific sample rate and the number of channels must be specified; it is not supported to set them to automatic.
     * @return
     *         - 0: Success
     *         - < 0: Failure
     * @note
     *        - Before calling this API, you must call setAudioSourceType{@link #IRTCEngine#setAudioSourceType} to enable custom audio capture.
     *        - You must push custom captured audio data every 10 ms.
     * @list Custom Stream Processing
     */
    virtual int pushExternalAudioFrame(IAudioFrame* audio_frame) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @region 自定义音频采集渲染
     * @brief 拉取下行音频数据用于自定义音频渲染。 <br>
     *        调用该方法后，SDK 会主动拉取待播放的音频数据，包括远端已解码和混音后的音频数据，用于外部播放。
     * @param audio_frame 获取的 10 ms 内的音频数据。详见 IAudioFrame{@link #IAudioFrame}。
     * @return 方法调用结果： <br>
     *        - 0：成功；
     *        - <0：失败。具体失败原因参看 ReturnStatus{@link #ReturnStatus}。
     * @note
     *       - 获取音频数据用于自定义渲染前，必须先调用 setAudioRenderType{@link #IRTCEngine#setAudioRenderType} 开启自定义渲染。
     *       - 由于 RTC SDK 的帧长为 10 毫秒，你应当每隔 10 毫秒拉取一次音频数据。确保音频采样点数（sample）x 拉取频率等于 audio_frame 的采样率 （sampleRate）。如设置采样率为 48000 时，每 10 毫秒调用本接口拉取数据，每次应拉取 480 个采样点。
     *       - 该函数运行在用户调用线程内，是一个同步函数。
     * @list 自定义流处理
     * @order 9
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @region  custom audio acquisition rendering
     * @brief  Pull remote audio data. You can use the data for audio processing or custom rendering.<br>
     *         After calling this method, the SDK will actively pull the audio data that is ready to be played, including the decoded and mixed audio data from remote end for external playback.
     * @param audio_frame The obtained audio data of 10 ms. See IAudioFrame{@link #IAudioFrame}。
     * @return API call result: <br>
     *        - 0: Success.
     *        - <0: Failure. See ReturnStatus{@link #ReturnStatus} for specific reasons.
     * @note
     *        - Before pulling the audio data, call setAudioRenderType{@link #IRTCEngine#setAudioRenderType} to enable custom rendering.
     *        - You should pull audio data every 10 milliseconds since the duration of a RTC SDK audio frame is 10 milliseconds. Samples x call frequency = audio_frame's sample rate. Assume that the sampling rate is set to 48000, call this API every 10 ms, so that 480 sampling points should be pulled each time.
     *        - This function runs in the user calling thread and is a synchronous function.
     */
    virtual int pullExternalAudioFrame(IAudioFrame* audio_frame) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 立即开启内部视频采集。默认为关闭状态。 <br>
     *        内部视频采集指：使用 RTC SDK 内置视频采集模块，进行采集。 <br>
     *        调用该方法后，本地用户会收到 onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} 的回调。 <br>
     *        本地用户在非隐身状态下调用该方法后，房间中的其他用户会收到 onUserStartVideoCapture{@link #IRTCEngineEventHandler#onUserStartVideoCapture} 的回调。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 调用 stopVideoCapture{@link #IRTCEngine#stopVideoCapture} 可以停止内部视频采集。否则，只有当销毁引擎实例时，内部视频采集才会停止。
     *       - 创建引擎后，无论是否发布视频数据，你都可以调用该方法开启内部视频采集。只有当（内部或外部）视频采集开始以后视频流才会发布。
     *       - 如果需要从自定义视频采集切换为内部视频采集，你必须先关闭自定义采集，再调用此方法手动开启内部采集。
     * @list 视频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Enable internal video capture immediately. The default is off. <br>
     *        Internal video capture refers to: use the RTC SDK built-in video capture module to capture. <br>
     *        The local client will be informed via onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} after starting video capture by calling this API. <br>
     *        The remote clients in the room will be informed of the state change via onUserStartVideoCapture{@link #IRTCEngineEventHandler#onUserStartVideoCapture} after the visible client starts video capture by calling this API.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Call stopVideoCapture{@link #IRTCEngine#stopVideoCapture} to stop the internal video capture. Otherwise, the internal video capture will sustain until you destroy the engine instance.
     *        - Once you create the engine instance, you can start internal video capture regardless of the video publishing state. The video stream will start publishing only after the video capture starts.
     *        - To switch from custom to internal video capture, stop publishing before disabling the custom video capture module and then call this API to enable the internal video capture.
     * @list Video Management
     */
    virtual int startVideoCapture() = 0;
    /**
     * @locale zh
     * @type api
     * @brief 立即关闭内部视频采集。默认为关闭状态。 <br>
     *        内部视频采集指：使用 RTC SDK 内置视频采集模块，进行采集。 <br>
     *        调用该方法后，本地用户会收到 onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} 的回调。 <br>
     *        非隐身用户进房后调用该方法，房间中的其他用户会收到 onUserStopVideoCapture{@link #IRTCEngineEventHandler#onUserStopVideoCapture} 的回调。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 调用 startVideoCapture{@link #IRTCEngine#startVideoCapture} 可以开启内部视频采集。
     *       - 如果不调用本方法停止内部视频采集，则只有当销毁引擎实例时，内部视频采集才会停止。
     * @list 视频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Disable internal video capture immediately. The default is off. <br>
     *        Internal video capture refers to: use the RTC SDK built-in video capture module to capture. <br>
     *        The local client will be informed via onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} after stopping video capture by calling this API. <br>
     *        The remote clients in the room will be informed of the state change via onUserStopVideoCapture{@link #IRTCEngineEventHandler#onUserStopVideoCapture} after the visible client stops video capture by calling this API.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Call startVideoCapture{@link #IRTCEngine#startVideoCapture} to enable the internal video capture.
     *        - Without calling this API the internal video capture will sustain until you destroy the engine instance.
     * @list Video Management
     */
    virtual int stopVideoCapture() = 0;
   /**
    * @locale zh
    * @type api
    * @brief 设置 RTC SDK 内部采集时的视频采集参数。 <br>
    *        如果你的项目使用了 SDK 内部采集模块，可以通过本接口指定视频采集参数，包括模式、分辨率、帧率。
    * @param video_capture_config 视频采集参数。参看: VideoCaptureConfig{@link #VideoCaptureConfig}。
    * @return
    *        - 0: 成功；
    *        - < 0: 失败；
    * @note
    * - 本接口在引擎创建后可调用，调用后立即生效。建议在调用 startVideoCapture{@link #IRTCEngine#startVideoCapture} 前调用本接口。
    * - 建议同一设备上的不同引擎使用相同的视频采集参数。
    * - 如果调用本接口前使用内部模块开始视频采集，采集参数默认为 Auto 模式。
    * @list 视频管理
    */
   /**
    * @locale en
    * @type api
    * @brief  Set the video capture parameters for internal capture of the RTC SDK. <br>
    *         If your project uses the SDK internal capture module, you can specify the video capture parameters including preference, resolution and frame rate through this interface.
    * @param video_capture_config  Video capture parameters. See: VideoCaptureConfig{@link #VideoCaptureConfig}.
    * @return
    *         - 0: Success;
    *         - < 0: Failure;
    * @note
    *  - This interface can be called after the engine is created and takes effect immediately after being called. It is recommended to call this interface before calling startVideoCapture{@link #IRTCEngine#startVideoCapture}.
    *  - It is recommended that different Engines on the same device use the same video capture parameters.
    *  - If you used the internal module to start video capture before calling this interface, the capture parameters default to Auto.
    * @list Video Management
    */
    virtual int setVideoCaptureConfig(const VideoCaptureConfig& video_capture_config) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置本端采集的视频帧的旋转角度。 <br>
     *        当摄像头倒置或者倾斜安装时，可调用本接口进行调整。
     * @param rotation 相机朝向角度，默认为 `VIDEO_ROTATION_0(0)`，无旋转角度。详见 VideoRotation{@link #VideoRotation}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 调用本接口也将对自定义采集视频画面生效，在原有的旋转角度基础上叠加本次设置。
     *        - 对于 Windows SDK，视频贴纸特效或通过 enableVirtualBackground{@link #IVideoEffect#enableVirtualBackground} 增加的虚拟背景，也会跟随本接口的设置进行旋转。
     * @list 视频处理
     */
    /**
     * @locale en
     * @type api
     * @brief Set the rotation of the video images captured from the local device. <br>
     *        Call this API to rotate the videos when the camera is fixed upside down or tilted.
     * @param rotation It defaults to `VIDEO_ROTATION_0(0)`, which means not to rotate. Refer to VideoRotation{@link #VideoRotation}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - For the videos captured by the internal module, the rotation will be combined with that set by calling setVideoRotationMode{@link #IRTCEngine#setVideoRotationMode}.
     *        - This API affects the external-sourced videos. The final rotation would be the original rotation angles adding up with the rotation set by calling this API.
     *        - For the Windows SDK, the elements added during the video pre-processing stage, such as video sticker and background applied using enableVirtualBackground{@link #IVideoEffect#enableVirtualBackground} will also be rotated by this API.
     * @list Video Processing
     */
    virtual int setVideoCaptureRotation(VideoRotation rotation) = 0;

    /**
     * @locale zh
     * @type api
     * @brief 发布端设置期望发布的最大分辨率视频流参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。 
     * @param encoder_config 期望发布的最大分辨率视频流参数。参看 VideoEncoderConfig{@link #VideoEncoderConfig}。
     * @return 方法调用结果：
     *        - 0：成功
     *        - !0：失败
     * @note
     *        - 自 V3.61 版本，本接口仅设置一路视频流参数。默认状态下只有单流，如需开启多路小流请使用：setLocalSimulcastMode{@link #IRTCEngine#setLocalSimulcastMode}。
     *        - 调用该方法前，SDK 默认仅发布一路视频流，分辨率为 640px × 360px @15fps，编码偏好为帧率优先。
     *        - 使用自定义采集时，必须调用该方法设置编码参数，以保证远端收到画面的完整性。
     *        - 该方法适用于摄像头采集的视频流，设置屏幕共享视频流参数参看 setScreenVideoEncoderConfig{@link #IRTCEngine#setScreenVideoEncoderConfig}（Linux 不适用）。
     * @list 视频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Sets the expected quality of the video stream by specifying the resolution, frame rate, bitrate, and the fallback strategy when the network is poor.
     * @param encoder_config See VideoEncoderConfig{@link #VideoEncoderConfig}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Since V3.61, this method can only set a single profile for the video stream. If you intend to publish the stream in multiple qualities, use setLocalSimulcastMode{@link #IRTCEngine#setLocalSimulcastMode}.
     *        - Without calling this method, only one stream will be sent with a profile of 640px × 360px @15fps. The default encoding preference is frame rate-first.
     *        - If you use an external video source, you can also use this method to set the encoding parameters.
     *        - This method can only change the quality of the camera video. If you intend to set the screen-sharing video stream, see setScreenVideoEncoderConfig{@link #IRTCEngine#setScreenVideoEncoderConfig} (not applicable for Linux).
    * @list Video Management
     */
    virtual int setVideoEncoderConfig(const VideoEncoderConfig& encoder_config) = 0;

    /**
     * @locale zh
     * @valid since 3.61
     * @type api
     * @brief 发布端进行大小流(simulcast)设置。
     * @param mode 详见 VideoSimulcastMode{@link #VideoSimulcastMode}。默认为只发送单流。你应在进房前调用修改本参数。
     * @param stream_config 小流参数。分辨率按照从小到大顺序，且每路流参数分辨率需小于大流 setVideoEncoderConfig{@link #IRTCEngine#setVideoEncoderConfig} 设置参数。否则可能会设置失败。参看 VideoEncoderConfig{@link #VideoEncoderConfig}。
     *        其余模式下，默认小流参数为 160px × 90px, 码率为 50kpbs。
     * @param num 自定义小流路数。与 stream_config 指向的数组长度一致。最大值为 3。
     * @return 方法调用结果： <br>
     *        - 0：成功
     *        - !0：失败
     * @note
     *        - 调用本方法前，SDK 默认仅发布一条分辨率为 640px × 360px @15fps 的视频流。
     *        - 本方法适用于摄像头采集的视频流。
     *        - 更多信息详见[推送多路流](https://www.volcengine.com/docs/6348/70139)文档。
     * @list 网络管理
     */
    /**
     * @locale en
     * @valid since 3.61
     * @type api
     * @brief Enable the Simulcast feature and configure the lower-quality video streams settings. 
     * @param mode Whether to publish lower-quality streams and how many of them to be published. See VideoSimulcastMode{@link #VideoSimulcastMode}. By default, it is set to Single, where the publisher sends the video in a single profile. In the other modes, the low-quality stream is set to a default resolution of 160px × 90px with a bitrate of 50Kbps.
     * @param stream_config The specification of the lower quality stream. See VideoEncoderConfig{@link #VideoEncoderConfig}. The resolution of the lower quality stream must be smaller than the standard stream set via setVideoEncoderConfig{@link #IRTCEngine#setVideoEncoderConfig}. The specifications in the array must be arranged in ascending order based on resolution.
     *        In the other modes, the low-quality stream is set to a default resolution of 160px × 90px with a bitrate of 50Kbps.
     * @param num The number of the lower quality streams, which is also the length of the stream_config array. The maximum value is 3.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details.
     * @note
     *        - The default specification of the video stream is 640px × 360px @15fps.
     *        - The method applies to the camera video only.
     *        - Refer to [Simulcasting](https://docs.byteplus.com/en/docs/byteplus-rtc/docs-70139) for more information.
     * @list Network Processing
     */
    virtual int setLocalSimulcastMode(VideoSimulcastMode mode, const VideoEncoderConfig* stream_config, int num) = 0;

     /**
     * @locale zh
     * @valid since 3.61
     * @type api
     * @brief 发布端开启大小流(simulcast)功能。
     * @param mode 详见 VideoSimulcastMode{@link #VideoSimulcastMode}。默认为只发送单流。
     *             其余模式下，默认小流参数为 160px × 90px, 码率为 50kpbs。
     * @return 方法调用结果： <br>
     *        - 0：成功
     *        - !0：失败
     * @note
     *        - 你应在进房前调用本接口。
     *        - 调用该方法前，SDK 默认仅发布一条分辨率为 640px × 360px，帧率为 15fps 的视频流。
     *        - 如需自定义小流参数请查看它的重载方法 setLocalSimulcastMode(VideoSimulcastMode mode, const VideoEncoderConfig* stream_config, int num)。
     *        - 该方法适用于摄像头采集的视频流。
     *        - 更多信息详见[推送多路流](https://www.volcengine.com/docs/6348/70139)文档。
     * @list 网络管理
     */
    /**
     * @locale en
     * @valid since 3.61
     * @type api
     * @brief Enable the Simulcast feature. 
     * @param mode Whether to publish lower-quality streams and how many of them to be published. See VideoSimulcastMode{@link #VideoSimulcastMode}. 
     * By default, it is set to Single, where the publisher sends the video in a single profile. In the other modes, the low-quality stream is set to a default resolution of 160px × 90px with a bitrate of 50Kbps.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details.
     * @note
     *        - Call this method before the user joins a room.
     *        - The default specification of the video stream is 640px × 360px @15fps.
     *        - If you intend to specify the lower-quality streams, use setLocalSimulcastMode(VideoSimulcastMode mode, const VideoEncoderConfig* stream_config, int num).
     *        - The method applies to the camera video only.
     *        - Refer to [Simulcasting](https://docs.byteplus.com/en/docs/byteplus-rtc/docs-70139) for more information.
     * @list Network Processing
     */
    virtual int setLocalSimulcastMode(VideoSimulcastMode mode) = 0;

    /**
     * @locale zh
     * @hidden for internal use only on Windows and Android
     * @type api
     * @brief 发布端设置全景视频，包括分辨率、高清视野和低清背景分辨率、Tile 大小，以及其他常规编码参数。
     * @param encoder_config 期望发布的最大分辨率视频流参数。参看 VideoEncoderConfig{@link #VideoEncoderConfig}。 <br>
     *                      支持 8K 和 4K 两种分辨率的全景视频。
     * @param parameters 全景视频的编码参数，JSON 字符串格式 <br>
     *                  - 8K: HD: 7680x3840, LD: 2560x1280, Tile: 640x640
     *                  - 4K: HD: 3840x1920, LD: 1280x640, Tile: 320x320
     * { <br>
     *  ```rtc.fov_config":{
     *      "mode":0,   //模式，只支持 `0` 等距柱状投影（Equirectangular Projection，ERP）模式
     *      "hd_width":3840,    //高清视野的宽
     *      "hd_height":1920,   //高清视野的高
     *      "ld_width":1280,    //低清背景的宽
     *      "ld_height":640,    //低清背景的高
     *      "tile_width":320,   //Tile 的宽，取值建议能被全景视频宽、高清视野宽、低清背景宽整除
     *      "tile_height":320,  //Tile 的高，取值建议能被全景视频高、高清视野高、低清背景高整除
     *      "framerate":30, //帧率
     *      "max_kbps":40000} //期望编码码率
     * }```
     * @return 方法调用结果： <br>
     *        - 0：成功
     *        - !0：失败
     * @note
     *        - 发布全景视频前，绑定自定义采集器，必须调用该方法设置编码参数。支持的视频格式包括 YUV 或者 Texture 纹理。
     *        - 通过 onFrame{@link #IVideoSink#onFrame} ,接收端获取到视频帧和解码需要的信息，传给自定义渲染器进行渲染。
     * @list 视频管理
     */
    /**
     * @locale en
     * @hidden for internal use only on Windows and Android
     * @type api
     * @brief <span id="RTCEngine-setvideoencoderconfig-3"></span> Video publishers call this API to set the parameters of the panoramic video, including resolution of the Fov, HD field, LD background, sizes of the Tiles, and the other regular configurations. <br>
     *        You can only set configuration for one stream with this API. If you want to set configuration for multiple streams, Call [setVideoEncoderConfig](#IRTCEngine-setvideoencoderconfig-2).
     * @param encoder_config The maximum video encoding parameter. Refer to VideoEncoderConfig{@link #VideoEncoderConfig} for more details.. <br>
     *                      Panoramic video of 8K or 4K is supported.
     * @param parameters JSON string of encoder configuration of the panoramic video <br>
     *                  - 8K: HD: 7680x3840, LD: 2560x1280, Tile: 640x640
     *                  - 4K: HD: 3840x1920, LD: 1280x640, Tile: 320x320
     * { <br>
     *  ```"rtc.fov_config":{
     *      "mode":0,   //Only `0` is available for now. `0` for Equirectangular Projection(ERP).
     *      "hd_width":3840,    //Width of the HD field.
     *      "hd_height":1920,   //Height of the HD field
     *      "ld_width":1280,    //Width of the background
     *      "ld_height":640,    //Height of the background
     *      "tile_width":320,   //Width of a tile
     *      "tile_height":320,  //Height of a tile
     *      "framerate":30, // Frame rate in fps
     *      "max_kbps":40000} // Expected encoding bitrate in kbps
     * }```
     * @return  API call result: <br>
     *        - 0: Success
     *        - ! 0: Failure
     * @note
     *        - Call this API to set encoding configurations for the panoramic video and designate an external video source. The format of the video can be YUV or Texture.
     *        - Receivers get the video frames and decoding configurations via onFrame{@link #IVideoSink#onFrame} and pass them to the external renderer.
     * @list Video Management
     */
    virtual int setVideoEncoderConfig(const VideoEncoderConfig& encoder_config, const char* parameters) = 0;

    /**
     * @locale zh
     * @type api
     * @brief 为发布的屏幕共享视频流设置期望的编码参数，包括分辨率、帧率、码率、网络不佳时的回退策略等。
     * @param screen_solution 屏幕共享视频流参数。参看 VideoEncoderConfig{@link #VideoEncoderConfig}。
     * @return
     *         0：成功。 <br>
     *         !0：失败。
     * @note 
     *        - 建议在采集视频前设置编码参数。若采集前未设置编码参数，则使用默认编码参数: 分辨率 1920px × 1080px，帧率 15fps，编码偏好为帧率优先。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 17
     */
    /**
     * @locale en
     * @type api
     * @brief  Set the configuration for shared-screen streams, including the resolution, frame rate, bitrate, and fallback strategies under challenging network conditions.
     * @param screen_solution The configuration for shared-screen streams. See VideoEncoderConfig{@link #VideoEncoderConfig}.
     * @return
     *        - 0: Success.
     *        - ! 0: Failure.
     * @note 
     *        - We recommend that you set the encoding configuration before video capture. Otherwise, the video will be captured based on the default configuration(1080p@15fps). The default encoding preference is frame rate-first.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int setScreenVideoEncoderConfig(const VideoEncoderConfig& screen_solution) = 0;

    /**
     * @locale zh
     * @hidden(Linux, macOS)
     * @valid since 3.58
     * @type api
     * @brief 开启自定义采集视频帧的 Alpha 通道编码功能。 <br>
     *        适用于需要分离推流端视频主体与背景，且在拉流端可自定义渲染背景的场景。
     * @param stream_index 需开启该功能的视频流类型，当前仅支持对 `StreamIndex.kStreamIndexMain` 即主流开启。
     * @param alpha_layout 分离后的 Alpha 通道相对于 RGB 通道信息的排列位置。当前仅支持 `AlphaLayout.kAlphaLayoutTop`，即置于 RGB 通道信息上方。
     * @return 方法调用结果： <br>
     *         - 0：成功；
     *         - !0：失败。
     * @note
     *        - 该接口仅作用于自定义采集的、并且使用 RGBA 色彩模型的视频帧，包括：
     *            - Windows：VideoPixelFormat.kVideoPixelFormatRGBA；
     *            - Android：VideoPixelFormat.TEXTURE_2D、VideoPixelFormat.TEXTURE_OES、VideoPixelFormat.RGBA；
     *            - iOS：ByteRTCVideoPixelFormat.ByteRTCVideoPixelFormatCVPixelBuffer。
     *        - 该接口须在发布视频流之前调用。
     *        - 调用本接口开启 Alpha 通道编码后，你需调用 pushExternalVideoFrame{@link #IRTCEngine#pushExternalVideoFrame} 把自定义采集的视频帧推送至 RTC SDK。若推送了不支持的视频帧格式，则调用 pushExternalVideoFrame{@link #IRTCEngine#pushExternalVideoFrame} 时会返回错误码 ReturnStatus.kReturnStatusParameterErr。
     * @list 视频管理
     */
    /**
    * @locale en
    * @hidden(Linux, macOS)
    * @valid since 3.58
    * @type api
    * @region Video Management
    * @brief Enables the Alpha channel encoding feature for custom capture video frames. <br>
    *        Suitable for scenarios where the video subject needs to be separated from the background at the push-streaming end, and the background can be custom rendered at the receive-streaming end.
    * @param stream_index The type of video stream for which this feature needs to be enabled. Currently, only StreamIndex.kStreamIndexMain is supported.
    * @param alpha_layout The arrangement position of the separated Alpha channel relative to the RGB channel information. Currently, only AlphaLayout.kAlphaLayoutTop is supported, which is positioned above the RGB channel information.
    * @return - 0: Success;
    *         - !0: Failure.
    * @note
    *        - This interface only applies to custom capture video frames that use the RGBA color model, including:
    *            - Windows: VideoPixelFormat.kVideoPixelFormatRGBA;
    *            - Android: VideoPixelFormat.TEXTURE_2D, VideoPixelFormat.TEXTURE_OES, VideoPixelFormat.RGBA;
    *            - iOS: ByteRTCVideoPixelFormat.ByteRTCVideoPixelFormatCVPixelBuffer.
    *        - This interface must be called before publishing the video stream.
    *        - After enabling Alpha channel encoding with this interface, you need to call pushExternalVideoFrame {@link #IRTCVideo#pushExternalVideoFrame} to push custom captured video frames to the RTC SDK. If a video frame format that is not supported is pushed, the call to pushExternalVideoFrame {@link #IRTCVideo#pushExternalVideoFrame} will return the error code ReturnStatus.kReturnStatusParameterErr.
    */
    virtual int enableAlphaChannelVideoEncode(StreamIndex stream_index, AlphaLayout alpha_layout) = 0;
    /**
     * @locale zh
     * @valid since 3.58
     * @type api
     * @brief 关闭外部采集视频帧的 Alpha 通道编码功能。
     * @param stream_index 需关闭该功能的视频流类型，当前仅支持设置为 StreamIndex.kStreamIndexMain 即主流。
     * @return 方法调用结果： <br>
     *         - 0：成功；
     *         - !0：失败。
     * @note 该接口须在停止发布视频流之后调用。
     * @list 视频管理
     */
     /**
     * @locale en
     * @valid since 3.58
     * @type api
     * @region Video Management
     * @brief Disables the Alpha channel encoding feature for custom captured video frames.
     * @param stream_index The type of video stream for which this feature needs to be disabled. Currently, only set to StreamIndex.kStreamIndexMain, i.e., the main stream, is supported.
     * @return - 0: Success;
     *         - !0: Failure.
     * @note This interface must be called after stopping publishing the video stream.
     */
    virtual int disableAlphaChannelVideoEncode(StreamIndex stream_index) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置本地视频渲染时使用的视图，并设置渲染模式。
     * @param index 视频流属性, 参看 StreamIndex{@link #StreamIndex}
     * @param canvas 视图信息和渲染模式，参看：VideoCanvas{@link #VideoCanvas}
     * @return
     *        - 0：成功
     *        - !0：失败
     * @note
     *        - 你应在加入房间前，绑定本地视图。退出房间后，此设置仍然有效。
     *        - 如果需要解除绑定，你可以调用本方法传入空视图。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 视频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Sets the view to be used for local video rendering and the rendering mode.
     * @param index Video stream type. See StreamIndex{@link #StreamIndex}.
     * @param canvas View information and rendering mode. See VideoCanvas{@link #VideoCanvas}.
     * @return
     *        - 0: Success
     *        - -1: Failure
     * @note
     *       - You should bind your stream to a view before joining the room. This setting will remain in effect after you leave the room.
     *       - If you need to unbind the local video stream from the current view, you can call this API and set the videoCanvas to `null`.
     *       - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int setLocalVideoCanvas(StreamIndex index, const VideoCanvas& canvas) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 修改本地视频渲染模式和背景色。
     * @param index 视频流属性, 参看 StreamIndex{@link #StreamIndex}
     * @param render_mode 渲染模式，参看 RenderMode{@link #RenderMode}
     * @param background_color 背景颜色，参看 VideoCanvas{@link #VideoCanvas}.background_color
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 你可以在本地视频渲染过程中，调用此接口。调用结果会实时生效。
     * @list 视频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Update the render mode and background color of local video rendering
     * @param index See StreamIndex{@link #StreamIndex}.
     * @param render_mode See RenderMode{@link #RenderMode}.
     * @param background_color See VideoCanvas{@link #VideoCanvas}.background_color.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note Calling this API during local video rendering will be effective immediately.
     * @list Video Management
     */
    virtual int updateLocalVideoCanvas(StreamIndex index, const enum RenderMode render_mode, const uint32_t background_color) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置渲染指定视频流 `stream_key` 时，使用的视图和渲染模式。 <br>
     *        如果需要解除某个用户的绑定视图，你可以把 `canvas` 设置为空。
     * @param stream_key 参看 RemoteStreamKey{@link #RemoteStreamKey}
     * @param canvas 视图信息和渲染模式，参看：VideoCanvas{@link #VideoCanvas}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 
     *        - 本地用户离开房间时，会解除调用此 API 建立的绑定关系；远端用户离开房间则不会影响。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 视频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Set the view and mode for rendering the specific video stream. <br>
     *        If you need to unbind the canvas for the video stream, set the canvas to `Null`.
     * @param stream_key See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param canvas canvas and rendering mode. See VideoCanvas{@link #VideoCanvas}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note 
     *        - When the local user leaves the room, the setting will be invalid. The remote user leaving the room does not affect the setting.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int setRemoteVideoCanvas(RemoteStreamKey stream_key, const VideoCanvas& canvas) = 0;
    /**
     * @locale zh
     * @deprecated since 3.56 on iOS and Android, and will be deleted in 3.62.
     * @type api
     * @brief 修改远端视频帧的渲染设置，包括渲染模式和背景颜色。
     * @param stream_key 远端流信息。参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param render_mode 渲染模式，参看 RenderMode{@link #RenderMode}。
     * @param background_color 背景颜色，参看 VideoCanvas{@link #VideoCanvas}.background_color
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 你可以在远端视频渲染过程中，调用此接口。调用结果会实时生效。
     * @list 视频管理
     */
    /**
     * @locale en
     * @deprecated since 3.56 on iOS and Android, and will be deleted in 3.62.
     * @type api
     * @brief Modifies remote video frame rendering settings, including render mode and background color.
     * @param stream_key See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param render_mode See RenderMode{@link #RenderMode}.
     * @param background_color See VideoCanvas{@link #VideoCanvas}.background_color.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note Calling this API during remote video rendering will be effective immediately.
     * @list Video Management
     */
    virtual int updateRemoteStreamVideoCanvas(RemoteStreamKey stream_key, const enum RenderMode render_mode, const uint32_t background_color) = 0;
    /**
     * @locale zh
     * @hidden(Windows, macOS, Linux)
     * @valid since 3.56
     * @type api
     * @brief 使用 SDK 内部渲染时，修改远端视频帧的渲染设置，包括渲染模式、背景颜色和旋转角度。
     * @param stream_key 远端流信息。参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param remote_video_render_config 视频帧渲染设置。具体参看 RemoteVideoRenderConfig{@link #RemoteVideoRenderConfig}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 调用 setRemoteVideoCanvas{@link #IRTCEngine#setRemoteVideoCanvas} 设置了远端视频渲染模式后，你可以调用此接口更新渲染模式、背景颜色、旋转角度的设置。
     *        - 该接口可以在远端视频渲染过程中调用，调用结果会实时生效。
     * @list 视频管理
     */
    /**
     * @locale en
     * @hidden(Windows, macOS, Linux)
     * @valid since 3.56
     * @type api
     * @brief Modifies remote video frame rendering settings, including render mode, background color, and rotation angle, while using the internal rendering of the SDK.
     * @param stream_key Information about the remote stream. See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param remote_video_render_config Video rendering settings. See RemoteVideoRenderConfig{@link #RemoteVideoRenderConfig}.
     * @return
     *        - 0: Success.
     *        - < 0 : Failure. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - After setting the rendering configuration for the remote video frame with setRemoteVideoCanvas{@link #IRTCEngine#setRemoteVideoCanvas}, you can call this API to update settings including render mode, background color, and rotation angle.
     *        - Calling this API during remote video rendering will be effective immediately.
     * @list Video Management
     */
    virtual int updateRemoteStreamVideoCanvas(RemoteStreamKey stream_key, const RemoteVideoRenderConfig& remote_video_render_config) = 0;
    /**
     * @locale zh
     * @deprecated since 3.57, use setLocalVideoRender{@link #IRTCEngine#setLocalVideoRender} instead.
     * @type api
     * @brief 将本地视频流与自定义渲染器绑定。
     * @param index 视频流属性。采集的视频流/屏幕视频流，参看 StreamIndex{@link #StreamIndex}
     * @param video_sink 自定义视频渲染器，参看 IVideoSink{@link #IVideoSink}。
     * @param required_format video_sink 适用的视频帧编码格式，参看 PixelFormat{@link #PixelFormat}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - RTC SDK 默认使用 RTC SDK 自带的渲染器（内部渲染器）进行视频渲染。
     *        - 如果需要解除绑定，必须将 video_sink 设置为 null。退房时将清除绑定状态。
     *        - 一般在收到 onFirstLocalVideoFrameCaptured{@link #IRTCEngineEventHandler#onFirstLocalVideoFrameCaptured} 回调通知完成本地视频首帧采集后，调用此方法为视频流绑定自定义渲染器；然后加入房间。
     *        - 本方法获取的是前处理后的视频帧，如需获取其他位置的视频帧（如采集后的视频帧），请调用 setLocalVideoRender{@link #IRTCEngine#setLocalVideoRender}。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @deprecated since 3.57, use setLocalVideoRender{@link #IRTCVideo#setLocalVideoRender} instead.
     * @region Custom Video Capturing & Rendering
     * @deprecated since 3.57, use setLocalVideoSink{@link #IRTCEngine#setLocalVideoSink} instead.
     * @brief Binds the local video stream to a custom renderer.
     * @param index Video stream type. See StreamIndex{@link #StreamIndex}.
     * @param video_sink Custom video renderer. See IVideoSink{@link #IVideoSink}.
     * @param required_format Video frame encoding format that applies to custom rendering. See PixelFormat{@link #PixelFormat}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - RTC SDK uses its own renderer (internal renderer) for video rendering by default.
     *        - Joining or leaving the room will not affect the binding state.
     *        - If you need to unbind the video stream from the custom renderer, you must set video_sink to `null`.
     *        - You should call this API before joining the room, and after receiving onFirstLocalVideoFrameCaptured{@link #IRTCEngineEventHandler#onFirstLocalVideoFrameCaptured} which reports that the first local video frame has been successfully captured.
     *        - This method gets video frames that have undergone preprocessing. If you need to obtain video frames from other positions, such as after capture, you should call setLocalVideoSink{@link #IRTCEngine#setLocalVideoSink} instead.
     */
    BYTERTC_DEPRECATED virtual int setLocalVideoSink(StreamIndex index, IVideoSink* video_sink, IVideoSink::PixelFormat required_format) = 0;

    /**
     * @locale zh
     * @valid since 3.57
     * @type api
     * @brief 将本地视频流与自定义渲染器绑定。你可以通过参数设置返回指定位置和格式的视频帧数据。
     * @param index 视频流属性。采集的视频流/屏幕视频流，参看 StreamIndex{@link #StreamIndex}。
     * @param video_sink 自定义视频渲染器，参看 IVideoSink{@link #IVideoSink}。
     * @param render_config 本地视频帧回调配置，参看 LocalVideoSinkConfig{@link #LocalVideoSinkConfig}。
     * @return
     *        - 0: 调用成功。
     *        - < 0: 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明。
     * @note
     *        - RTC SDK 默认使用自带的渲染器（内部渲染器）进行视频渲染。
     *        - 退房时将清除绑定状态。
     *        - 如果需要解除绑定，你必须将 video_sink 设置为 null。
     *        - 一般在收到 onFirstLocalVideoFrameCaptured{@link #IRTCEngineEventHandler#onFirstLocalVideoFrameCaptured} 回调通知完成本地视频首帧采集后，调用此方法为视频流绑定自定义渲染器；然后加入房间。
     * @list 自定义流处理
     */
    /**
     * @locale zh
     * @valid since 3.57
     * @type api
     * @brief Binds the local video stream to a custom renderer.You can get video frame data at specified positions and formats through parameter settings.
     * @param index Video stream type. See StreamIndex{@link #StreamIndex}.
     * @param video_sink Custom video renderer. See IVideoSink{@link #IVideoSink}.
     * @param render_config Local video frame callback configuration, see LocalVideoSinkConfig{@link #LocalVideoSinkConfig}。
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - RTC SDK uses its own renderer (internal renderer) for video rendering by default.
     *        - Leaving the room will clear the binding status.
     *        - If you need to unbind the video stream from the custom renderer, you must set video_sink to `null`.
     *        - Generally, after receiving the onFirstLocalVideoFrameCaptured{@link #IRTCEngineEventHandler#onFirstLocalVideoFrameCaptured} callback notification that the first local video frame has been captured, call this method to bind a custom renderer to a video stream and join the room.
     * @list Custom Stream Processing
     * @order 2
     */
    virtual int setLocalVideoSink(StreamIndex index, IVideoSink* video_sink, LocalVideoSinkConfig& config) = 0;

    /**
     * @locale zh
     * @type api
     * @deprecated since 3.57, use setRemoteVideoSink{@link #IRTCEngine#setRemoteVideoSink} instead.
     * @region 自定义视频采集渲染
     * @brief 将远端视频流与自定义渲染器绑定。
     * @param stream_key 远端流信息，用于指定需要渲染的视频流来源及属性，参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param video_sink 自定义视频渲染器，参看 IVideoSink{@link #IVideoSink}。
     * @param required_format video_sink 适用的视频帧编码格式，参看 PixelFormat{@link #PixelFormat}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - RTC SDK 默认使用 RTC SDK 自带的渲染器（内部渲染器）进行视频渲染。
     *        - 该方法进房前后均可以调用。若想在进房前调用，你需要在加入房间前获取远端流信息；若无法预先获取远端流信息，你可以在加入房间并通过 onUserPublishStreamVideo{@link #IRTCRoomEventHandler#onUserPublishStreamVideo} 回调获取到远端流信息之后，再调用该方法。
     *        - 如果需要解除绑定，必须将 video_sink 设置为 null。退房时将清除绑定状态。
     *        - 本方法获取的是后处理后的视频帧，如需获取其他位置的视频帧（如解码后的视频帧），请调用 setRemoteVideoSink{@link #IRTCEngine#setRemoteVideoSink}。
     */
    /**
     * @locale en
     * @type api
     * @deprecated since 3.57, use setRemoteVideoRender{@link #IRTCEngine#setRemoteVideoRender} instead.
     * @region Custom Video Capturing & Rendering
     * @brief Binds the remote video stream to a custom renderer.
     * @param stream_key Remote stream information which specifies the source and type of the video stream to be rendered. See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param video_sink Custom video renderer. See IVideoSink{@link #IVideoSink}.
     * @param required_format Encoding format which applies to the custom renderer. See PixelFormat{@link #PixelFormat}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - RTC SDK uses its own renderer (internal renderer) for video rendering by default.
     *        - Joining or leaving the room will not affect the binding state.
     *         - This API can be called before and after entering the room. To call before entering the room, you need to get the remote stream information before joining the room; if you cannot get the remote stream information in advance, you can call the API after joining the room and getting the remote stream information via onUserPublishStreamVideo{@link #IRTCRoomEventHandler#onUserPublishStreamVideo}.
     *         - If you need to unbind, you must set videoSink to null.
     *        - This method gets video frames that have undergone processing. If you need to obtain video frames from other positions, such as after decoded, you should call setLocalVideoRender{@link #IRTCVideo#setLocalVideoRender} instead.
     */
    BYTERTC_DEPRECATED virtual int setRemoteVideoSink(RemoteStreamKey stream_key, IVideoSink* video_sink, IVideoSink::PixelFormat required_format) = 0;
    
    /**
     * @locale zh
     * @valid since 3.57
     * @type api
     * @region 自定义视频帧回调
     * @brief 将远端视频流与自定义渲染器绑定。你可以通过参数设置返回指定位置和格式的视频帧数据。
     * @param stream_key 远端流信息，用于指定需要渲染的视频流来源及属性，参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param video_sink 自定义视频渲染器，参看 IVideoSink{@link #IVideoSink}。
     * @param config 远端视频帧回调配置，参看 RemoteVideoSinkConfig{@link #RemoteVideoSinkConfig}。
     * @return
     *        - 0: 调用成功。
     *        - < 0: 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明。
     * @note
     *        - RTC SDK 默认使用自带的渲染器（内部渲染器）进行视频渲染。
     *        - 该方法进房前后均可以调用。若想在进房前调用，你需要在加入房间前获取远端流信息；若无法预先获取远端流信息，你可以在加入房间并通过 onUserPublishStreamVideo{@link #IRTCRoomEventHandler#onUserPublishStreamVideo} 回调获取到远端流信息之后，再调用该方法。
     *        - 退房时将清除绑定状态。
     *        - 如果需要解除绑定，你必须将 video_sink 设置为 null。
     * @list 自定义流处理
     * @order 3
     */
    /**
     * @locale en
     * @valid since 3.57
     * @type api
     * @brief Binds the remote video stream to a custom renderer.You can get video frame data at specified positions and formats through parameter settings.
     * @param stream_key Video stream info, used to specify the source and attributes of the video stream that needs to be rendered. See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param video_sink Custom video renderer. See IVideoSink{@link #IVideoSink}.
     * @param config remote video frame callback configuration, see RemoteVideoSinkConfig{@link #RemoteVideoSinkConfig}。
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - RTC SDK uses its own renderer (internal renderer) for video rendering by default.
     *        - This method can be called both before and after entering the room. If you are unable to obtain remote stream information in advance, you can call this method after joining the room and receiving the remote stream information through the onUserPublishStream{@link #IRTCRoomEventHandler#onUserPublishStream} callback.
     *        - Leaving the room will clear the binding status.
     *        - If you need to unbind the video stream from the custom renderer, you must set video_sink to `null`.
     * @list Custom Stream Processing
     * @order 2
     */

    virtual int setRemoteVideoSink(RemoteStreamKey stream_key, IVideoSink* video_sink, RemoteVideoSinkConfig& config) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief 切换视频内部采集时使用的前置/后置摄像头 <br>
     *        调用此接口后，在本地会触发 onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} 回调。
     * @param camera_id 参看 CameraID {@link #CameraID}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 默认使用前置摄像头。
     *       - 如果你正在使用相机进行视频采集，切换操作当即生效；如果相机未启动，后续开启内部采集时，会打开设定的摄像头。
     *       - 如果本地有多个摄像头且想选择特定工作摄像头可通过 IVideoDeviceManager{@link #IVideoDeviceManager} 来控制。
     * @list 视频管理
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief Toggle the front/postcondition camera used for internal video capture <br>
     *        After calling this interface, onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} callback will be triggered locally.
     * @param camera_id See CameraID{@link #CameraID}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Default uses front-facing camera.
     *        - If you are using the camera for video capture, the toggle operation will take effect immediately; if the camera is not activated, the set camera will be turned on when the internal capture is turned on later.
     *        - If you have multiple cameras locally and want to select a specific working camera, you can control it through IVideoDeviceManager{@link #IVideoDeviceManager}.
     * @list Video Management
     */
    virtual int switchCamera(CameraID camera_id) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 推送屏幕视频帧
     * @param frame 设置屏幕视频帧，详见：IVideoFrame{@link #IVideoFrame}。
     * @return 方法调用结果： <br>
     *        - 0：成功；
     *        - <0：失败。具体失败原因参看 ReturnStatus{@link #ReturnStatus}。
     * @note
     *       - 支持格式：I420, NV12, RGBA, BGRA, ARGB。不支持 RGB24。
     *       - 该函数运行在用户调用线程内，即将销毁 IRTCEngine 实例前，请停止调用该函数推送屏幕共享数据
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @type api
     * @brief Push screen video frame.
     * @param frame Set the screen video frame, see: IVideoFrame{@link #IVideoFrame}.
     * @return API call result: <br>
     *        - 0: Success.
     *        - <0: Failure. See ReturnStatus{@link #ReturnStatus} for specific reasons.
     * @note
     *        - Only video frames in YUV420P format are supported for the time being.
     *        - This function runs in the user calling thread. Before destroying the IRTCEngine instance, please stop calling this function to push screen shared data
     * @list Screen Sharing
     */
    virtual int pushScreenVideoFrame(const VideoFrameData& frame) = 0;

    /**
     * @locale zh
     * @hidden(Linux,iOS,Android)
     * @type api
     * @brief 外部采集时，当屏幕或待采集窗口大小发生改变，为了使 RTC 更好地决策合适的帧率和分辨率积，调用此接口设置改变前的分辨率。
     * @param original_capture_width 首次采集屏幕流的宽度。
     * @param original_capture_height 首次采集屏幕流的高度。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 调用此接口之前，建议调用 setScreenVideoEncoderConfig{@link #IRTCEngine#setScreenVideoEncoderConfig} 设置屏幕流编码相关参数：编码模式设置为智能模式，屏幕帧宽高设置为 0，最大码率设置为-1，最小码率设置为 0。
     *        - 调用此接口后，你将收到回调 onExternalScreenFrameUpdate{@link #IRTCEngineEventHandler#onExternalScreenFrameUpdate}，根据推荐的帧率和分辨率积重新采集。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 16
     */
    /**
     * @locale en
     * @hidden(Linux,iOS,Android)
     * @type api
     * @brief When the resolution of the external shared-screen stream changes, you can call this API to set the original pixel and framerate to help RTC(in the automatic mode) recommend these configurations.
     * @param original_capture_width The original width of shared-screen streams.
     * @param original_capture_height The original height of shared-screen streams.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Before calling this API，you are advised to call setScreenVideoEncoderConfig{@link #IRTCEngine#setScreenVideoEncoderConfig} to set the encoding configurations: set the encoding mode to the automatic mode, the width and height to 0, the maximum bitrate to -1, and the minimum bitrate to 0.
     *        - After calling this API，you will receive onExternalScreenFrameUpdate{@link #IRTCEngineEventHandler#onExternalScreenFrameUpdate} to re-capture the stream based on the recommended pixel and framerate by RTC.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int setOriginalScreenVideoInfo(int original_capture_width, int original_capture_height) = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @type api
     * @brief 通过 RTC SDK 提供的采集模块采集屏幕视频流时，更新采集区域。仅用于采集源为显示器屏幕时。
     * @param region_rect 采集区域。参见 Rectangle{@link #Rectangle} <br>
     *                         此参数描述了调用此接口后的采集区域，和 startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture} 中 `source_info` 设定区域的相对关系。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 
     *        - 调用此接口前，必须已通过调用 startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture} 开启了内部屏幕流采集。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 5
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @type api
     * @brief Update the capture area when capturing screen video streams through the capture module provided by the RTC SDK. Only used when the capture source is the monitor screen.
     * @param region_rect Region to be shared. See Rectangle{@link #Rectangle} <br>
     *                         This parameter describes the region to be shared after calling this API, and the relative relationship between the `source_info` setting area in startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note 
     *        - Before calling this interface, internal screen stream capture must have been turned on by calling startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int updateScreenCaptureRegion(const Rectangle& region_rect) = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @type api
     * @brief 采集屏幕视频流，用于共享。屏幕视频流包括：屏幕上显示的内容，应用窗口中显示的内容，或虚拟屏幕中显示的内容。 <br>
     *        其中，虚拟屏幕中显示的内容仅在 Windows 平台上支持。
     * @param source_info 待共享的屏幕源，参看 ScreenCaptureSourceInfo{@link #ScreenCaptureSourceInfo}。 <br>
     *                         你可以调用 getScreenCaptureSourceList{@link #IRTCEngine#getScreenCaptureSourceList} 获得所有可以共享的屏幕源。
     * @param capture_params 共享参数。参看 ScreenCaptureParameters{@link #ScreenCaptureParameters}。
     * @return
     *        - 0: 成功
     *        - -1: 失败
     * @note
     *       - 调用本接口时，采集模式应为内部模式。在外部采集模式下调用无效，并将触发 onVideoDeviceWarning{@link #IRTCEngineEventHandler#onVideoDeviceWarning} 回调。
     *       - 调用此方法仅开启屏幕流视频采集，不会发布采集到的视频。发布屏幕流视频需要调用 publishScreenVideo{@link #IRTCRoom#publishScreenVideo}。
     *       - 调用 stopScreenVideoCapture{@link #IRTCEngine#stopScreenVideoCapture} 关闭屏幕视频源采集。
     *       - 本地用户通过 onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} 的回调获取屏幕采集状态，包括开始、暂停、恢复、错误等。
     *       - 调用成功后，本端会收到 onFirstLocalVideoFrameCaptured{@link #IRTCEngineEventHandler#onFirstLocalVideoFrameCaptured} 回调。
     *       - 调用此接口前，你可以调用 setScreenVideoEncoderConfig{@link #IRTCEngine#setScreenVideoEncoderConfig} 设置屏幕视频流的采集帧率和编码分辨率。
     *       - 在收到 onFirstLocalVideoFrameCaptured{@link #IRTCEngineEventHandler#onFirstLocalVideoFrameCaptured} 回调后通过调用 setLocalVideoCanvas{@link #IRTCEngine#setLocalVideoCanvas} 或 setLocalVideoSink{@link #setLocalVideoSink} 函数设置本地屏幕共享视图。
     *       - 可以调用 setLocalVideoSink{@link #IRTCEngine#setLocalVideoSink} 将本地视频流与自定义渲染器绑定，通过回调 onFrame{@link #IVideoSink#onFrame} 获取采集成功的本地视频帧。
     *       - 对于 Windows SDK，再开启采集屏幕视频流后，你可以调用 updateScreenCaptureHighlightConfig{@link #IRTCEngine#updateScreenCaptureHighlightConfig} 更新边框高亮设置，调用 updateScreenCaptureMouseCursor{@link #IRTCEngine#updateScreenCaptureMouseCursor} 更新对鼠标的处理设置，调用 updateScreenCaptureFilterConfig{@link #IRTCEngine#updateScreenCaptureFilterConfig} 设置需要过滤的窗口。
     *       - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 1
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @type api
     * @brief  Capture screen video stream for sharing. Screen video stream includes: content displayed on the screen, in the application window, or in the virtual screen. <br>
     *        Displaying contents in virtual screens is only available on Windows.
     * @param source_info Screen capture source information. See ScreenCaptureSourceInfo{@link #ScreenCaptureSourceInfo}. <br>
     *                          Call getScreenCaptureSourceList{@link #IRTCEngine#getScreenCaptureSourceList} to get all the screen sources that can be shared.
     * @param capture_params Screen capture parameters. See ScreenCaptureParameters{@link #ScreenCaptureParameters}.
     * @return
     *         - 0: Success;
     *         - -1: Failure;
     * @note
     *        - The call of this API takes effects only when you are using RTC SDK to record screen. You will get a warning by onVideoDeviceWarning{@link #IRTCEngineEventHandler#onVideoDeviceWarning} after calling this API when the video source is set to an external recorder.
     *        - This API only starts screen capturing but does not publish the captured video. Call publishScreenVideo{@link #IRTCRoom#publishScreenVideo} to publish the captured video.
     *        - To turn off screen video capture, call stopScreenVideoCapture{@link #IRTCEngine#stopScreenVideoCapture}.
     *        - Local users will receive onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} on the state of screen capturing such as start, pause, resume, and error.
     *        - After successfully calling this API, local users will receive onFirstLocalVideoFrameCaptured{@link #IRTCEngineEventHandler#onFirstLocalVideoFrameCaptured}.
     *        - Before calling this API, you can call setScreenVideoEncoderConfig{@link #IRTCEngine#setScreenVideoEncoderConfig} to set the frame rate and encoding resolution of the screen video stream.
     *        - After receiving onFirstLocalVideoFrameCaptured{@link #IRTCEngineEventHandler#onFirstLocalVideoFrameCaptured}, you can set the local screen sharing view by calling setLocalVideoCanvas{@link #IRTCEngine#setLocalVideoCanvas} or setLocalVideoSink {@link #setLocalVideoSink}.
     *        - You can also register an observer by calling setLocalVideoSink{@link #IRTCEngine#setLocalVideoSink} and receive the captured screen video frame through onFrame{@link #IVideoSink#onFrame}.
     *        - For Windows SDK, after you start capturing screen video stream for sharing，you can call updateScreenCaptureHighlightConfig{@link #IRTCEngine#updateScreenCaptureHighlightConfig} to update border highlighting settings, updateScreenCaptureMouseCursor{@link #IRTCEngine#updateScreenCaptureMouseCursor} to update the processing settings for the mouse, and updateScreenCaptureFilterConfig{@link #IRTCEngine#updateScreenCaptureFilterConfig} to set the window that needs to be filtered.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int startScreenVideoCapture(const ScreenCaptureSourceInfo& source_info, const ScreenCaptureParameters& capture_params) = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @type api
     * @brief 停止屏幕视频流采集。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 调用本接口时，采集模式应为内部模式。在外部采集模式下调用无效，并将触发 onVideoDeviceWarning{@link #IRTCEngineEventHandler#onVideoDeviceWarning} 回调。
     *       - 要开启屏幕视频流采集，调用 startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}。
     *       - 调用后，本地用户会收到 onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} 的回调。
     *       - 调用此接口不影响屏幕视频流发布。
     *       - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 6
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @type api
     * @brief Stop screen video streaming.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - This API can only stop the screen capture by the RTC SDK. If the video source has been set to external recorder, the call of this API will fail. You need to stop it in the external recorder.
     *        - The call of this API takes effects only when you are using RTC SDK to record screen. You will get a warning by onVideoDeviceWarning{@link #IRTCEngineEventHandler#onVideoDeviceWarning} after calling this API when the video source is set to an external recorder.
     *        - To turn on screen video stream capture, calling startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}
     *        - Local users will receive the onVideoDeviceStateChanged{@link #IRTCEngineEventHandler#onVideoDeviceStateChanged} callback.
     *        - Calling this interface does not affect screen video stream publishing.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int stopScreenVideoCapture() = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @type api
     * @brief 通过 RTC SDK 提供的采集模块采集屏幕视频流时，更新边框高亮设置。默认展示表框。
     * @param highlight_config 边框高亮设置。参见 HighlightConfig{@link #HighlightConfig}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 调用此接口前，必须已通过调用 startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture} 开启了内部屏幕流采集。
     *        - 对 Linux 系统，采用 X11 协议时可用；采用 Wayland 协议时不可用。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 2
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @type api
     * @brief Update border highlighting settings when capturing screen video streams through the capture module provided by the RTC SDK. The default display table box.
     * @param highlight_config Border highlighting settings. See HighlightConfig{@link #HighlightConfig}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Before calling this interface, you must have turned on internal screen flow collection by calling startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}.
     *        - For Linux with X11 protocal, the API is valid; for Linux with Wayland, the API is not valid.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int updateScreenCaptureHighlightConfig(const HighlightConfig& highlight_config) = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @type api
     * @brief 通过 RTC SDK 提供的采集模块采集屏幕视频流时，更新对鼠标的处理设置。默认采集鼠标。
     * @param capture_mouse_cursor 参看 MouseCursorCaptureState{@link #MouseCursorCaptureState}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 
     *        - 调用此接口前，必须已通过调用 startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture} 开启了内部屏幕流采集。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 3
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @type api
     * @brief Update the processing settings for the mouse when capturing screen video streams through the capture module provided by the RTC SDK. Default acquisition mouse.
     * @param capture_mouse_cursor See MouseCursorCaptureState{@link #MouseCursorCaptureState}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note  
     *        - Before calling this interface, internal screen stream capture must have been turned on by calling startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int updateScreenCaptureMouseCursor(MouseCursorCaptureState capture_mouse_cursor) = 0;
    /**
     * @locale zh
     * @hidden(Linux,iOS,Android)
     * @type api
     * @brief 通过 RTC SDK 提供的采集模块采集屏幕视频流时，设置需要过滤的窗口。
     * @param filter_config 窗口过滤设置，参看 ScreenFilterConfig{@link #ScreenFilterConfig}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 调用此接口前，必须已通过调用 startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture} 开启了内部屏幕流采集。
     *       - 本函数在屏幕源类别是屏幕而非应用窗体时才起作用。详见：ScreenCaptureSourceType{@link #ScreenCaptureSourceType}。
     *       - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 4
     */
    /**
     * @locale en
     * @hidden(Linux,iOS,Android)
     * @type api
     * @brief When capturing screen video streams through the capture module provided by the RTC SDK, set the window that needs to be filtered.
     * @param filter_config Window filtering settings. See ScreenFilterConfig{@link #ScreenFilterConfig}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Before calling this interface, internal screen stream capture must have been turned on by calling startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}.
     *        - This function only works when the screen source category is a screen rather than an application form. See ScreenCaptureSourceType{@link #ScreenCaptureSourceType}.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual int updateScreenCaptureFilterConfig(const ScreenFilterConfig& filter_config) = 0;

    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @brief 获取共享对象列表。
     * @return 共享对象列表，包括应用窗口和屏幕。详见 IScreenCaptureSourceList{@link #IScreenCaptureSourceList}。用户选择后，可调用 startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture} 进行共享。
     * @note
     *        - 对 Linux 系统，采用 X11 协议时可用；采用 Wayland 协议时不可用。采用 Wayland 协议，调用 startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture} 时，系统会通过弹窗供用户选择共享对象。
     *        - 使用完之后需要调用 release{@link #IScreenCaptureSourceList#release} 接口释放。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 0
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @brief Get the item list for screen sharing.
     * @return List of the screen-sharing objects, including application window and screens. See IScreenCaptureSourceList{@link #IScreenCaptureSourceList}. After deciding which item to be shared, the user can share the object with startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}.
     * @note
     *        - For Linux with X11 protocal, the API is valid; for Linux with Wayland, the API is not valid. For Linux with Wayland, when you call startScreenVideoCapture{@link #IRTCEngine#startScreenVideoCapture}, the system pops a windows for the user to choose the object to share.
     *        - Call release{@link #IScreenCaptureSourceList#release} to release the resources.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual IScreenCaptureSourceList* getScreenCaptureSourceList() = 0;

    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @brief 获取共享对象缩略图
     * @region 屏幕共享
     * @param type 屏幕采集对象的类型。详见 ScreenCaptureSourceType{@link #ScreenCaptureSourceType}。
     * @param source_id 屏幕共享对象的 ID，可通过 getScreenCaptureSourceList{@link #IRTCEngine#getScreenCaptureSourceList} 枚举共享对象列表中获取。详见 [view_t](70098#view-t)。
     * @param max_width 最大宽度。保持采集对象本身的宽高比不变，将缩略图缩放到指定范围内的最大宽高。如果给出的尺寸与共享对象比例不同，得到的缩略图会有黑边。
     * @param max_height 最大高度。参见 max_width 的说明。
     * @return 共享对象缩略图，详见 IVideoFrame{@link #IVideoFrame}。
     * @note 
     *        - 对 Linux 系统，采用 X11 协议时可用；采用 Wayland 协议时不可用。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 9
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @brief Get the preview thumbnail of the screen-sharing object
     * @region  screen share
     * @param type Type of the screen capture object. Refer to ScreenCaptureSourceType{@link #ScreenCaptureSourceType} for more details.
     * @param source_id ID of the screen sharing object, which can be obtained from the list of shared objects enumerated by `getScreenCaptureSourceList` {@link #IRTCVideo#getScreenCaptureSourceList}. For more details, see view_t{@link #view-t}.
     * @param max_width Maximum width of the preview thumbnail. RTC will scale the thumbnail to fit the given size while maintaining the original aspect ratio. If the aspect ratio of the given size does not match the sharing object, the thumbnail will have blank borders.
     * @param max_height Maximum height of the preview thumbnail. Refer to the note for `max_width`.
     * @return Preview thumbnail of the screen-sharing object Refer to IVideoFrame{@link #IVideoFrame} for more details.
     * @note 
     *        - For Linux with X11 protocal, the API is valid; for Linux with Wayland, the API is not valid.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual IVideoFrame* getThumbnail(
            ScreenCaptureSourceType type, view_t source_id, int max_width, int max_height) = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @region 屏幕共享
     * @brief 获取应用窗体所属应用的图标。
     * @param source_id 屏幕共享对象的 ID，可通过 getScreenCaptureSourceList{@link #IRTCEngine#getScreenCaptureSourceList} 枚举共享对象列表中获取。详见 [view_t](70098#view-t)。
     * @param max_width 最大宽度。返回的图标将是宽高相等的，输入宽高不等时，取二者较小值。宽高范围为 [32,256]，超出该范围将返回 nullptr，默认输出 100 x 100 的图像。
     * @param max_height 最大高度。参见 max_width 的说明。
     * @return 详见 IVideoFrame{@link #IVideoFrame}。当屏幕共享对象为应用窗体，且有图标时有效，否则返回 nullptr。
     * @note 
     *        - 对 Linux 系统，采用 X11 协议时可用；采用 Wayland 协议时不可用。
     *        - 对 Linux SDK：仅在 Client 版本有，在 Server 版本上没有。
     * @list 屏幕分享
     * @order 10
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @brief Get application window preview thumbnail for screen sharing.
     * @param source_id ID of the screen-sharing object. You can get the ID by calling getScreenCaptureSourceList{@link #IRTCEngine#getScreenCaptureSourceList}. Refer to view_t{@link #view_t} for more details.
     * @param max_width Maximum width of the App icon. The width is always equal to the height. SDK will set the height and width to the smaller value if the given values are unequal. RTC will return nullptr if you set the value with a number out of the valid range, [32, 256]. The default size is 100 x 100.
     * @param max_height Maximum height of the app icon. Refer to the note for  `max_width`
     * @return Refer to IVideoFrame{@link #IVideoFrame} for more details. You can get the icon when the item to be shared is an application and the application is assigned with an icon. If not, the return value will be nullptr.
     * @note 
     *        - For Linux with X11 protocal, the API is valid; for Linux with Wayland, the API is not valid.
     *        - For Linux SDK: Only available in Client version, not available in Server version.
     */
    virtual IVideoFrame* getWindowAppIcon(view_t source_id, int max_width = 100, int max_height = 100) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置向 SDK 输入的视频源，包括屏幕流 <br>
     *        默认使用内部采集。内部采集指：使用 RTC SDK 内置的视频采集机制进行视频采集。
     * @param stream_index 视频流的属性，参看 StreamIndex{@link #StreamIndex}
     * @param type 视频输入源类型，参看 VideoSourceType{@link #VideoSourceType}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 该方法进房前后均可调用。
     *        - 当你已调用 startVideoCapture{@link #IRTCEngine#startVideoCapture} 开启内部采集后，再调用此方法切换至自定义采集时，SDK 会自动关闭内部采集。
     *        - 当你调用此方法开启自定义采集后，想要切换至内部采集，你必须先调用此方法关闭自定义采集，然后调用 startVideoCapture{@link #IRTCEngine#startVideoCapture} 手动开启内部采集。
     *        - 当你需要向 SDK 推送自定义编码后的视频帧，你需调用该方法将视频源切换至自定义编码视频源。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Set the video source, including the screen recordings. <br>
     *        The internal video capture is the default, which refers to capturing video using the built-in module.
     * @param stream_index Stream index. Refer to StreamIndex{@link #StreamIndex} for more details.
     * @param type Video source type. Refer to VideoSourceType{@link #VideoSourceType} for more details.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - You can call this API whether the user is in a room or not.
     *         - Calling this API to switch to the custom video source will stop the enabled internal video capture.
     *         - To switch to internal video capture, call this API to stop custom capture and then call startVideoCapture{@link #IRTCEngine#startVideoCapture} to enable internal video capture.
     *         - To push custom encoded video frames to the SDK, call this API to switch `VideoSourceType` to `VideoSourceTypeEncodedWithAutoSimulcast` or `VideoSourceTypeEncodedWithoutAutoSimulcast`.
     * @list Custom Stream Processing
     */
    virtual int setVideoSourceType(StreamIndex stream_index, VideoSourceType type) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 推送外部视频帧。
     * @param frame 设置视频帧，参看 IVideoFrame{@link #IVideoFrame}。
     * @return 方法调用结果： <br>
     *        - 0：成功；
     *        - <0：失败。具体失败原因参看 ReturnStatus{@link #ReturnStatus}。
     * @note
     *       - 支持格式：I420, NV12, RGBA, BGRA, ARGB。不支持 RGB24。
     *       - 该函数运行在用户调用线程内
     *       - 推送外部视频帧前，必须调用 setVideoSourceType{@link #setVideoSourceType} 开启外部视频源采集。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Pushes external video frames.
     * @param frame Set the video frame. See IVideoFrame{@link #IVideoFrame}.
     * @return API call result: <br>
     *        - 0: Success.
     *        - <0: Failure. See ReturnStatus{@link #ReturnStatus} for specific reasons.
     * @note
     *        - Support for I420, NV12, RGBA, BGRA, and ARGB.
     *        - This function runs in the user calling thread.
     *        - Before pushing external video frames, you must call setVideoSourceType{@link #setVideoSourceType} to turn on external video source capture.
     * @list Custom Stream Processing
     */
    virtual int pushExternalVideoFrame(const VideoFrameData& frame) = 0;

    /**
     * @locale zh
     * @hidden(Windows,Linux,macOS)
     * @type api
     * @brief 设置音频播放设备。默认使用通过 setDefaultAudioRoute{@link #IRTCEngine#setDefaultAudioRoute} 设置的默认音频路由。 <br>
     *        音频播放设备发生变化时，会收到 onAudioRouteChanged{@link #IRTCEngineEventHandler#onAudioRouteChanged} 回调。
     * @param device 音频播放设备。参看 AudioRoute{@link #AudioRoute}。仅支持听筒或者扬声器。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 对于绝大多数音频场景，使用 setDefaultAudioRoute{@link #IRTCEngine#setDefaultAudioRoute} 设置默认音频路由，并借助 RTC SDK 的音频路由自动切换逻辑即可完成。切换逻辑参见[移动端设置音频路由](https://www.volcengine.com/docs/6348/117836)。你应仅在例外的场景下，使用此接口，比如在接入外接音频设备时，手动切换音频路由。
     *       - 本接口仅支持在 `AUDIO_SCENARIO_COMMUNICATION` 音频场景下使用。你可以通过调用 setAudioScenario{@link #IRTCEngine#setAudioScenario} 切换音频场景。
     * @list 音频管理
     */
    /**
     * @locale en
     * @hidden(Windows,Linux,macOS)
     * @type api
     * @brief Set the audio playback device. The audio route set by setDefaultAudioRoute{@link #IRTCEngine#setDefaultAudioRoute} by default. <br>
     *         When the audio playback device changes, you will receive an onAudioRouteChanged{@link #IRTCEngineEventHandler#onAudioRouteChanged} Callback.
     * @param device Audio playback device. See AudioRoute{@link #AudioRoute}. You can only set to earpiece or speakerphone.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - You can implement most scenarios by calling setDefaultAudioRoute{@link #IRTCEngine#setDefaultAudioRoute} and the default audio route switching strategy of the RTC SDK. For details about the strategy, see [Set the Audio Route](https://docs.byteplus.com/byteplus-rtc/docs/117836). You should use this API only in a few exceptional scenarios like manually switching audio route with external audio device connected.
     *       - This interface is only supported in the `AUDIO_SCENARIO_COMMUNICATION` audio scenario. Call setAudioScenario{@link #IRTCEngine#setAudioScenario} to switch between different audio scenarios.
     * @list Audio Management
     */
    virtual int setAudioRoute(AudioRoute route) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief 将默认的音频播放设备设置为听筒或扬声器。
     * @param route 音频播放设备。参看 AudioRoute{@link #AudioRoute}
     * @return 方法调用结果 <br>
     *        - 0: 方法调用成功。立即生效。当所有音频外设移除后，音频路由将被切换到默认设备。
     *        - < 0: 方法调用失败。指定除扬声器和听筒以外的设备将会失败。
     * @note 对于音频路由切换逻辑，参见[移动端设置音频路由](https://www.volcengine.com/docs/6348/117836)。
     * @list 音频管理
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief Set the speaker or earpiece as the default audio playback device.
     * @param route Audio playback device. Refer to AudioRoute{@link #AudioRoute}
     * @return
     *         - 0: Success. The setting takes effect immediately. However, the audio route will not switch to the default device until all the audio peripheral devices are disconnected.
     *         - < 0: failure. It fails when the device designated is neither a speaker nor an earpiece.
     * @note For the audio route switching strategy of the RTC SDK, see [Set the Audio Route](https://docs.byteplus.com/byteplus-rtc/docs/117836).
     * @list Audio Management
     */
    virtual int setDefaultAudioRoute(AudioRoute route) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief 获取当前音频播放设备 <br>
     *        音频播放设备发生变化时，会收到 onAudioRouteChanged{@link #IRTCEngineEventHandler#onAudioRouteChanged} 回调。
     * @return device 当前音频播放设备。参看 AudioRoute{@link #AudioRoute}
     * @note
     *       - 1. 该接口仅适用于移动设备。
     *       - 2. 通话前和通话中都可以调用该方法。
     * @list 音频管理
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief get current using audio playback device. <br>
     *         When the audio playback device changes, you will receive an onAudioRouteChanged{@link #IRTCEngineEventHandler#onAudioRouteChanged} Callback.
     * @return  device current using Audio playback device. See AudioRoute{@link #AudioRoute}
     * @note
     *        - 1. This interface is only available for mobile devices.
     *        - 2. This method can be called before and during a call.
     * @list Audio Management
     */
    virtual AudioRoute getAudioRoute() = 0;
    /**
     * @locale zh
     * @type api
     * @brief 创建房间实例。 <br>
     *        调用此方法仅返回一个房间实例，你仍需调用 joinRoom{@link #IRTCRoom#joinRoom} 才能真正地创建/加入房间。 <br>
     *        多次调用此方法以创建多个 IRTCRoom{@link #IRTCRoom} 实例。分别调用各 IRTCRoom 实例中的 joinRoom{@link #IRTCRoom#joinRoom} 方法，同时加入多个房间。 <br>
     *        多房间模式下，用户可以同时订阅各房间的音视频流。
     * @param room_id 标识通话房间的房间 ID。该字符串符合正则表达式：`[a-zA-Z0-9_@\-\.]{1,128}`。
     * @return 创建的 IRTCRoom{@link #IRTCRoom} 房间实例。
     *         返回 NULL 时，请确认指定房间是否已经存在或 roomId 格式错误。
     * @note
     *        - 如果需要加入的房间已存在，你仍需先调用本方法来获取 IRTCRoom 实例，再调用 joinRoom{@link #IRTCRoom#joinRoom} 加入房间。
     *        - 请勿使用同样的 roomId 创建多个房间，否则后创建的房间实例会替换先创建的房间实例。
     *        - 如果你需要在多个房间发布音视频流，无须创建多房间，直接调用 startForwardStreamToRooms{@link #IRTCRoom#startForwardStreamToRooms} 开始跨房间转发媒体流。
     * @list 房间管理
     */
    /**
     * @locale en
     * @type api
     * @brief Create a room instance. <br>
     *        This API only returns a room instance. You still need to call joinRoom{@link #IRTCRoom#joinRoom} to actually create/join the room. <br>
     *        Each call of this API creates one IRTCRoom{@link #IRTCRoom} instance. Call this API as many times as the number of rooms you need, and then call joinRoom{@link #IRTCRoom#joinRoom} of each IRTCRoom instance to join multiple rooms at the same time. <br>
     *        In multi-room mode, a user can subscribe to media streams in the joined rooms at the same time.
     * @param room_id The string matches the regular expression: `[a-zA-Z0-9_@\-\.]{1,128}`.
     * @return IRTCRoom{@link #IRTCRoom} instance.
     *         If you get NULL instead of an RTCRoom instance, please ensure the roomId is valid. And the specified room is not yet created.
     * @note
     *       - If the room that you wish to join already exists, you still need to call this API first to create the IRTCRoom instance, and then call joinRoom{@link #IRTCRoom#joinRoom}.
     *       - Do not create multiple rooms with the same roomId, otherwise the newly created room instance will replace the old one.
     *       - To forward streams to the other rooms, call startForwardStreamToRooms{@link #IRTCRoom#startForwardStreamToRooms} instead of enabling Multi-room mode.
     * @list Room Management
     */
    virtual IRTCRoom* createRTCRoom(const char* room_id) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置发布的音视频流的回退选项。 <br>
     *        你可以调用该接口设置网络不佳或设备性能不足时从大流起进行降级处理，以保证通话质量。
     * @param option 本地发布的音视频流回退选项，参看 PublishFallbackOption{@link #PublishFallbackOption}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 该方法仅在调用 setLocalSimulcastMode{@link #IRTCEngine#setLocalSimulcastMode} 开启了发送多路视频流的情况下生效。
     *        - 该方法必须在进房前设置，进房后设置或更改设置无效。
     *        - 调用该方法后，如因性能或网络不佳产生发布性能回退或恢复，本端会提前收到 onPerformanceAlarms{@link #IRTCEngineEventHandler#onPerformanceAlarms} 回调发出的告警，以便采集设备配合调整。
     *        - 设置回退后，本地发布的音视频流发生回退或从回退中恢复后，远端会收到 onSimulcastSubscribeFallback{@link #IRTCEngineEventHandler#onSimulcastSubscribeFallback} 回调，通知该情况。
     *        - 你可以调用客户端 API 或者在服务端下发策略设置回退。当使用服务端下发配置实现时，下发配置优先级高于在客户端使用 API 设定的配置。
     * @list 网络管理
     */
    /**
     * @locale en
     * @type api
     * @brief Sets the fallback option for published audio & video streams. <br>
     *        You can call this API to set whether to automatically lower the resolution you set of the published streams under limited network conditions.
     * @param option Fallback option, see PublishFallbackOption{@link #PublishFallbackOption}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - This API only works after you call setLocalSimulcastMode{@link #IRTCEngine#setLocalSimulcastMode} to enable the mode of publishing multiple streams.
     *         - You must call this API before entering the room.
     *         - After calling this method, if there is a performance degradation or recovery due to poor performance or network conditions, the local end will receive early warnings through the onPerformanceAlarms{@link #IRTCEngineEventHandler#onPerformanceAlarms} callback to adjust the capture device.
     *         - After you allow video stream to fallback, your stream subscribers will receive onSimulcastSubscribeFallback{@link #IRTCEngineEventHandler#onSimulcastSubscribeFallback} when the resolution of your published stream are lowered or restored.
     *         - You can alternatively set fallback options with distrubutions from server side, which is of higher priority.
     * @list Network Processing
     */
    virtual int setPublishFallbackOption(PublishFallbackOption option) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置订阅的音视频流的回退选项。 <br>
     *        你可调用该接口设置网络不佳或设备性能不足时允许订阅流进行降级或只订阅音频流，以保证通话流畅。
     * @param option 订阅的音视频流回退选项，参看 SubscribeFallbackOption{@link #SubscribeFallbackOption}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 你必须在进房前设置，进房后设置或更改设置无效。
     *        - 设置回退选项后，订阅的音视频流发生回退或从回退中恢复时，会收到 onSimulcastSubscribeFallback{@link #IRTCEngineEventHandler#onSimulcastSubscribeFallback} 和 onRemoteVideoSizeChanged{@link #IRTCEngineEventHandler#onRemoteVideoSizeChanged} 回调通知。
     *        - 你可以调用 API 或者在服务端下发策略设置回退。当使用服务端下发配置实现时，下发配置优先级高于在客户端使用 API 设定的配置。
     * @list 网络管理
     */
    /**
     * @locale en
     * @type api
     * @brief Sets the fallback option for subscribed RTC streams. <br>
     *        You can call this API to set whether to lower the resolution of currently subscribed stream under limited network conditions.
     * @param option Fallback option, see SubscribeFallbackOption{@link #SubscribeFallbackOption} for more details.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - You must call this API before enterting the room.
     *         - After you enables the fallback, you will receive onSimulcastSubscribeFallback{@link #IRTCEngineEventHandler#onSimulcastSubscribeFallback} and onRemoteVideoSizeChanged{@link #IRTCEngineEventHandler#onRemoteVideoSizeChanged} when the resolution of your subscribed stream is lowered or restored.
     *         - You can alternatively set fallback options with distrubutions from server side, which is of higher priority.
     * @list Network Processing
     */
    virtual int setSubscribeFallbackOption(SubscribeFallbackOption option) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置用户优先级。
     * @param room_id 房间 ID
     * @param user_id 远端用户的 ID
     * @param priority 远端用户的需求优先级，详见枚举类型 RemoteUserPriority{@link #RemoteUserPriority}
     * @return 0: 方法调用成功 <br>
     *        - < 0: 方法调用失败
     * @note
     *        - 1. 该方法与 setSubscribeFallbackOption{@link #IRTCEngine#setSubscribeFallbackOption} 搭配使用。
     *        - 2. 如果开启了订阅流回退选项，弱网或性能不足时会优先保证收到的高优先级用户的流的质量。
     *        - 3. 该方法在进房前后都可以使用，可以修改远端用户的优先级。
     * @list 网络管理
     */
    /**
     * @locale en
     * @type api
     * @brief  Set user priority
     * @param room_id Room ID
     * @param user_id Remote user's ID
     * @param priority Remote user's requirement priority. See enumeration type RemoteUserPriority{@link #RemoteUserPriority}
     * @return 0:  Success. <br>
     *         - < 0: failure
     * @note
     *         - 1. This method is used with setSubscribeFallbackOption{@link #IRTCEngine#setSubscribeFallbackOption}.
     *         - 2. If the subscription flow fallback option is turned on, weak connections or insufficient performance will give priority to ensuring the quality of the flow received by high-priority users.
     *         - 3. This method can be used before and after entering the room, and the priority of the remote user can be modified.
     * @list Network Processing
     */
    virtual int setRemoteUserPriority(const char* room_id, const char* user_id, RemoteUserPriority priority) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置业务标识参数 <br>
     *        可通过 businessId 区分不同的业务场景。businessId 由客户自定义，相当于一个“标签”，可以分担和细化现在 AppId 的逻辑划分的功能，但不需要鉴权。
     * @param business_id <br>
     *        用户设置的自己的 businessId 值 <br>
     *        businessId 只是一个标签，颗粒度需要用户自定义。
     * @return
     *        - 0： 成功
     *        - < 0： 失败，具体原因参照 BusinessCheckCode{@link #BusinessCheckCode} 。
     *        - -6001： 用户已经在房间中。
     *        - -6002： 输入非法，合法字符包括所有小写字母、大写字母和数字，除此外还包括四个独立字符，分别是：英文句号，短横线，下划线和 @ 。
     * @note
     *        - 需要在调用 joinRoom{@link #IRTCRoom#joinRoom} 之前调用，joinRoom{@link #IRTCRoom#joinRoom} 之后调用该方法无效。
     * @list 引擎管理
     */
    /**
     * @locale en
     * @type api
     * @brief  Sets the business ID <br>
     *         You can use businessId to distinguish different business scenarios. You can customize your businessId to serve as a sub AppId, which can share and refine the function of the AppId, but it does not need authentication.
     * @param business_id <br>
     *         Your customized businessId <br>
     *         BusinessId is a tag, and you can customize its granularity.
     * @return
     *         - 0: Success
     *         - < 0: Failure. See BusinessCheckCode{@link #BusinessCheckCode} for specific reasons.
     *         - -6001: The user is already in the room.
     *         - -6002: The input is invalid. Legal characters include all lowercase letters, uppercase letters, numbers, and four other symbols, including '.', '-','_', and '@'.
     * @note
     *         - You must call this API before the joinRoom{@link #IRTCRoom#joinRoom} API, otherwise it will be invalid.
     * @list Engine Management
     */
    virtual int setBusinessId(const char* business_id) = 0;
    /**
     * @locale zh
     * @hidden(Windows,macOS,Linux)
     * @type api
     * @brief 设置采集视频的旋转模式。默认以 App 方向为旋转参考系。 <br>
     *        接收端渲染视频时，将按照和发送端相同的方式进行旋转。
     * @param rotation_mode 视频旋转参考系为 App 方向或重力方向，参看 VideoRotationMode{@link #VideoRotationMode}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 旋转仅对内部视频采集生效，不适用于外部视频源和屏幕源。
     *        - 调用该接口时已开启视频采集，将立即生效；调用该接口时未开启视频采集，则将在采集开启后生效。
     *        - 更多信息请参考[视频采集方向](https://www.volcengine.com/docs/6348/106458)。
     * @list Engine Management
     */
    /**
     * @locale en
     * @hidden(Windows,macOS,Linux)
     * @type api
     * @brief Set the orientation of the video capture. By default, the App direction is used as the orientation reference. <br>
     *        During rendering, the receiving client rotates the video in the same way as the sending client did.
     * @param rotation_mode Rotation reference can be the orientation of the App or gravity. Refer to VideoRotationMode{@link #VideoRotationMode} for details.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - The orientation setting is effective for internal video capture only. That is, the orientation setting is not effective to the custom video source or the screen-sharing stream.
     *        - If the video capture is on, the setting will be effective once you call this API. If the video capture is off, the setting will be effective on when capture starts.
     * @list Engine Management
     */
    virtual int setVideoRotationMode(VideoRotationMode rotation_mode) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 为采集到的视频流开启镜像
     * @param mirror_type 镜像类型，参看 MirrorType{@link #MirrorType}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 切换视频源不影响镜像设置。
     *        - 屏幕视频流始终不受镜像设置影响。
     *        - 使用外部渲染器时，`mirrorType` 支持设置为 `0`（无镜像）和 `3`（本地预览和编码传输镜像），不支持设置为 `1`（本地预览镜像）。
     *        - 该接口调用前，各视频源的初始状态如下：
     *        <table>
     *           <tr><th></th><th>前置摄像头</th><th>后置摄像头</th><th>自定义采集视频源</th> <th>桌面端摄像头</th> </tr>
     *           <tr><td>移动端</td><td>本地预览镜像，编码传输不镜像</td><td> 本地预览不镜像，编码传输不镜像 </td><td> 本地预览不镜像，编码传输不镜像 </td><td>/</td></tr>
     *           <tr><td>桌面端</td><td>/</td><td>/</td><td> 本地预览不镜像，编码传输不镜像 </td><td> 本地预览镜像，编码传输不镜像 </td></tr>
     *        </table>
     * @list 视频处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Sets the mirror mode for the captured video stream.
     * @param mirror_type Mirror type. See MirrorType{@link #MirrorType}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Switching video streams does not affect the settings of the mirror type.
     *        - This API is not applicable to screen-sharing streams.
     *        - When using an external renderer, you can set `mirrorType` to `0` and `3`, but you cannot set it to `1`.
     *        - Before you call this API, the initial states of each video stream are as follows:
     *        <table>
     *           <tr><th></th><th>Front-facing camera</th><th>Back-facing camera</th><th>Custom capturing</th><th>Built-in camera</th></tr>
     *           <tr><td>Mobile device</td><td>The preview is mirrored. The published video stream is not mirrored.</td><td>The preview and the published video stream are not mirrored.</td><td>The preview and the published video stream are not mirrored.</td><td>/</td></tr>
     *           <tr><td>PC</td><td>/</td><td>/</td><td>The preview and the published video stream are not mirrored.</td><td>The preview is mirrored. The published video stream is not mirrored.</td></tr>
     *        </table>
     * @list Video Processing
     */
    virtual int setLocalVideoMirrorType(MirrorType mirror_type) = 0;
    /**
     * @locale zh
     * @valid since 3.57
     * @type api
     * @brief 使用内部渲染时，为远端流开启镜像。
     * @param stream_key 远端流信息，用于指定需要镜像的视频流来源及属性，参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param mirror_type 远端流的镜像类型，参看 RemoteMirrorType{@link #RemoteMirrorType}。
     * @return
     *        - 0: 调用成功。
     *        - < 0: 调用失败，参看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明。
     * @list 视频处理
     */
    /**
     * @locale en
     * @type api
     * @valid since 3.57
     * @region Video Management
     * @brief When using internal rendering, enable mirroring for the remote stream.
     * @param stream_key Information about the remote stream, used to specify the source and attributes of the video stream that need to be mirrored, see RemoteStreamKey {@link #RemoteStreamKey}.
     * @param mirror_type The mirror type for the remote stream, see RemoteMirrorType {@link #RemoteMirrorType}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Video Processing
     * @order 1
    */
    virtual int setRemoteVideoMirrorType(RemoteStreamKey stream_key, RemoteMirrorType mirror_type) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 获取视频特效接口。
     * @return 视频特效接口，参看 IVideoEffect{@link #IVideoEffect}。
     * @list 视频处理
     */
    /**
     * @locale en
     * @type api
     * @brief Gets video effect interfaces.
     * @return Video effect interfaces. See IVideoEffect{@link #IVideoEffect}.
     * @list Video Processing
     */
    virtual IVideoEffect* getVideoEffectInterface() = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 开启/关闭基础美颜。
     * @param enable 基础美颜开关 <br>
     *        - true: 开启基础美颜
     *        - false: 关闭基础美颜（默认）
     * @return
     *        - 0: 调用成功。
     *        - –1000: 未集成特效 SDK。
     *        - –1001: RTC SDK 版本不支持此功能。
     *        - –1002: 特效 SDK 当前版本不支持此功能，建议使用特效 SDK v4.4.2+ 版本。
     *        - –1003: 联系技术支持人员。
     *        - –1004: 正在下载相关资源，下载完成后生效。
     *        - <0: 调用失败，特效 SDK 内部错误，具体错误码请参考[错误码表](https://www.volcengine.com/docs/6705/102042)。
     * @note
     *        - 本方法不能与高级视频特效接口共用。如已购买高级视频特效，建议参看[集成指南](https://www.volcengine.com/docs/6348/114717)使用高级美颜、特效、贴纸功能等。
     *        - 使用此功能需要集成特效 SDK，建议使用特效 SDK v4.4.2+ 版本。更多信息参看 [Native 端基础美颜](https://www.volcengine.com/docs/6348/372605)。
     *        - 调用 setBeautyIntensity{@link #IRTCEngine#setBeautyIntensity} 设置基础美颜强度。若在调用本方法前没有设置美颜强度，则使用默认强度。各基础美颜模式的强度默认值分别为：美白 0.7，磨皮 0.8，锐化 0.5，清晰 0.7。
     *        - 本方法仅适用于视频源，不适用于屏幕源。
     * @list 视频处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Enables/Disables basic beauty effects.
     * @param enable Whether to enable basic beauty effects. <br>
     *        - true: Enables basic beauty effects.
     *        - false: (Default) Disables basic beauty effects.
     * @return
     *        - 0: Success.
     *        - –1000: The Effect SDK is not integrated.
     *        - –1001: This API is not available for your current RTC SDK.
     *        - –1002: This API is not available for your current Effect SDK. You can upgrade your Effect SDK to v4.4.2+.
     *        - –1003: Contact our technical support team for further instructions.
     *        - –1004: Downloading related resources. The beauty effects will take effect after downloading.
     *        - <0: Failure. Effect SDK internal error. For specific error code, see [Error Code Table](https://docs.byteplus.com/effects/docs/error-code-table).
     * @note
     *        - You cannot use the basic beauty effects and the advanced effect features at the same time. See [how to use advanced effect features](https://docs.byteplus.com/byteplus-rtc/docs/114717) for more information.
     *        - You need to integrate Effect SDK before calling this API. Effect SDK v4.4.2+ is recommended.
     *        - Call setBeautyIntensity{@link #IRTCEngine#setBeautyIntensity} to set the beauty effect intensity. If you do not set the intensity before calling this API, the default intensity will be enabled. The default values for the intensity of each beauty mode are as follows: 0.7 for brightning, 0.8 for smoothing, 0.5 for sharpening, and 0.7 for clarity.
     *        - This API is not applicable to screen capturing.
     * @list Video Processing
     */
    virtual int enableEffectBeauty(bool enable) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 调整基础美颜强度。
     * @param beauty_mode 基础美颜模式，参看 EffectBeautyMode{@link #EffectBeautyMode}。
     * @param intensity 美颜强度，取值范围为 [0,1]。强度为 0 表示关闭。 <br>
     *                       各基础美颜模式的强度默认值分别为：美白 0.7，磨皮 0.8，锐化 0.5，清晰 0.7。
     * @return
     *        - 0: 调用成功。
     *        - –1000: 未集成特效 SDK。
     *        - –1001: RTC SDK 版本不支持此功能。
     *        - <0: 调用失败，特效 SDK 内部错误，具体错误码请参考[错误码表](https://www.volcengine.com/docs/6705/102042)。
     * @note
     *        - 若在调用 enableEffectBeauty{@link #IRTCEngine#enableEffectBeauty} 前设置美颜强度，则对应美颜功能的强度初始值会根据设置更新。
     *        - 销毁引擎后，美颜功能强度恢复默认值。
     * @list 视频处理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Sets the beauty effect intensity.
     * @param beauty_mode Basic beauty effect. See EffectBeautyMode{@link #EffectBeautyMode}.
     * @param intensity Beauty effect intensity in range of [0,1]. When you set it to 0, the beauty effect will be turned off. <br>
     *                       The default values for the intensity of each beauty mode are as follows: 0.7 for brightning, 0.8 for smoothing, 0.5 for sharpening, and 0.7 for clarity.
     * @return
     *        - 0: Success.
     *        - –1000: The Effect SDK is not integrated.
     *        - –1001: This API is not available for your current RTC SDK.
     *        - <0: Failure. Effect SDK internal error. For specific error code, see [error codes](https://docs.byteplus.com/effects/docs/error-code-table).
     * @note
     *        - If you call this API before calling enableEffectBeauty{@link #IRTCEngine#enableEffectBeauty}, the default settings of beauty effect intensity will adjust accordingly.
     *        - If you destroy the engine, the beauty effect settings will be invalid.
     * @list Video Processing
     */
    virtual int setBeautyIntensity(EffectBeautyMode beauty_mode, float intensity) = 0;
    /**
     * @locale zh
     * @hidden for internal use only
     * @valid since 3.54
     * @type api
     * @brief 设置远端视频超分模式。
     * @param stream_key 远端流信息，用于指定需要设置超分的视频流来源及属性，参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param mode 超分模式，参看 VideoSuperResolutionMode{@link #VideoSuperResolutionMode}。
     * @return
     *        - 0: kReturnStatusSuccess，SDK 调用成功，并不代表超分模式实际状态，需要根据回调 onRemoteVideoSuperResolutionModeChanged{@link #IRTCEngineEventHandler#onRemoteVideoSuperResolutionModeChanged} 判断实际状态。
     *        - -1: kReturnStatusNativeInvalid，native library 未加载。
     *        - -2: kReturnStatusParameterErr，参数非法，指针为空或字符串为空。
     *        - -9: kReturnStatusScreenNotSupport，不支持对屏幕流开启超分。
     * @note
     *        - 该方法须进房后调用。
     *        - 远端用户视频流的原始分辨率不能超过 640 × 360 px。
     *        - 支持对一路远端流开启超分，不支持对多路流开启超分。
     * @list 视频处理
     */
    /**
     * @locale en
     * @hidden not available
     * @type api
     * @brief Sets the super resolution mode for remote video stream.
     * @param stream_key Remote stream information that specifies the source and type of the video stream. See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param mode Super resolution mode. See VideoSuperResolutionMode{@link #VideoSuperResolutionMode}.
     * @return. <br>
     *        - 0: kReturnStatusSuccess. It does not indicate the actual status of the super resolution mode, you should refer to onRemoteVideoSuperResolutionModeChanged{@link #IRTCEngineEventHandler#onRemoteVideoSuperResolutionModeChanged} callback.
     *        - -1: kReturnStatusNativeInValid. Native library is not loaded.
     *        - -2: kReturnStatusParameterErr. Invalid parameter.
     *        - -9: kReturnStatusScreenNotSupport. Failure. Screen stream is not supported.
     *        See ReturnStatus{@link #ReturnStatus} for more return value indications.
     * @note
     *        - Call this API after joining room.
     *        - The original resolution of the remote video stream should not exceed 640 × 360 pixels.
     *        - You can only turn on super-resolution mode for one stream.
     * @list Video Processing
     */
    virtual int setRemoteVideoSuperResolution(RemoteStreamKey stream_key, VideoSuperResolutionMode mode) = 0;
    /**
     * @locale zh
     * @hidden for internal use only
     * @valid since 3.54
     * @type api
     * @brief 设置视频降噪模式。
     * @param mode 视频降噪模式，启用后能够增强视频画质，但同时会增加性能负载。参看 VideoDenoiseMode{@link #VideoDenoiseMode}。
     * @return
     *        - 0: API 调用成功。 用户可以根据回调函数 onVideoDenoiseModeChanged{@link #IRTCEngineEventHandler#onVideoDenoiseModeChanged} 判断视频降噪是否开启。
     *        - < 0：API 调用失败。
     * @list 视频处理
     */
    /**
     * @locale en
     * @hidden not available
     * @type api
     * @brief Sets the video noise reduction mode.
     * @param mode Video noise reduction mode which helps enhance video quality but will increase CPU utilization. <br>
     * Refer to VideoDenoiseMode{@link #VideoDenoiseMode} for more details.
     * @return
     *        - 0: Success. Please refer to onVideoDenoiseModeChanged{@link #IRTCEngineEventHandler#onVideoDenoiseModeChanged} callback for the actual state of video noise reduction mode.
     *        - < 0：Failure.
     * @list Video Processing
     */
    virtual int setVideoDenoiser(VideoDenoiseMode mode) = 0;
    /**
     * @locale zh
     * @hidden(Windows, Linux, macOS)
     * @type api
     * @brief 在自定义视频前处理及编码前，设置 RTC 链路中的视频帧朝向，默认为 Adaptive 模式。 <br>
     *        移动端开启视频特效贴纸，或使用自定义视频前处理时，建议固定视频帧朝向为 Portrait 模式。单流转推场景下，建议根据业务需要固定视频帧朝向为 Portrait 或 Landscape 模式。不同模式的具体显示效果参看[视频帧朝向](https://www.volcengine.com/docs/6348/128787)。
     * @param orientation 视频帧朝向，参看 VideoOrientation{@link #VideoOrientation}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 视频帧朝向设置仅适用于内部采集视频源。对于自定义采集视频源，设置视频帧朝向可能会导致错误，例如宽高对调。屏幕源不支持设置视频帧朝向。
     *        - 编码分辨率的更新与视频帧处理是异步操作，进房后切换视频帧朝向可能导致画面出现短暂的裁切异常，因此建议在进房前设置视频帧朝向，且不在进房后进行切换。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @hidden(Windows,Linux,macOS)
     * @type api
     * @brief Sets the orientation of the video frame before custom video processing and encoding. The default value is `Adaptive`. <br>
     *        You should set the orientation to `Portrait` when using video effects or custom processing. <br>
     *        You should set the orientation to `Portrait` or `Landscape` when pushing a single stream to the CDN.
     * @param orientation Orientation of the video frame. See VideoOrientation{@link #VideoOrientation}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - The orientation setting is only applicable to internal captured video sources. For custom captured video sources, setting the video frame orientation may result in errors, such as swapping width and height. Screen sources do not support video frame orientation setting.
     *        - We recommend setting the orientation before joining room. The updates of encoding configurations and the orientation are asynchronous, therefore can cause a brief malfunction in preview if you change the orientation after joining room.
     * @list Custom Stream Processing
     */
    virtual int setVideoOrientation(VideoOrientation orientation) = 0;
    /**
     * @locale zh
     * @hidden(macOS, Windows,Linux)
     * @type api
     * @brief 获取相机控制接口
     * @return 相机控制接口指针，参看 ICameraControl{@link #ICameraControl}
     * @list 视频管理
     */
    /**
     * @locale en
     * @hidden(macOS, Windows,Linux)
     * @type api
     * @brief  Get camera control interface
     * @return  Camera control interface pointer, see ICameraControl{@link #ICameraControl}.
     * @list Video Management
     */
    virtual ICameraControl* getCameraControl() = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置传输时使用内置加密的方式。
     * @param encrypt_type 内置加密算法，详见 EncryptType{@link #EncryptType}
     * @param encrypt_type 加密类型，详见 EncryptType{@link #EncryptType}
     * @param key 加密密钥，长度限制为 36 位，超出部分将会被截断
     * @param key_size 参数 key 的长度
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 使用传输时内置加密时，使用此方法；如果需要使用传输时自定义加密，参看 onEncryptData{@link #IEncryptHandler#onEncryptData}。 内置加密和自定义加密互斥，根据最后一个调用的方法确定传输是加密的方案。
     *       - 该方法必须在进房之前调用，可重复调用，以最后调用的参数作为生效参数。
     * @list 通话加密
     */
    /**
     * @locale en
     * @type api
     * @brief Sets the way to use built-in encryption when transmitting.
     * @param encrypt_type Built-in encryption algorithm. See EncryptType{@link #EncryptType}
     * @param encrypt_type Encryption type. See EncryptType{@link #EncryptType}
     * @param key Encryption key, the length is limited to 36 bits, and the excess will be Truncate
     * @param key_size The length of the parameter key
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Use this method when using the built-in encryption on transfer; if you need to use custom encryption on transfer. See onEncryptData {@link #IEncryptHandler#onEncryptData}. Built-in encryption and custom encryption are mutually exclusive, and the transmission is determined to be encrypted according to the last method called.
     *        - This method must be called before entering the room, and can be called repeatedly, taking the last called parameter as the effective parameter.
     * @list Encryption
     */
    virtual int setEncryptInfo(EncryptType encrypt_type, const char* key, int key_size) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置自定义加密和解密方式。
     * @param handler 自定义加密 handler，需要实现 handler 的加密和解密方法。参看 IEncryptHandler{@link #IEncryptHandler}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 该方法与 setEncryptInfo{@link #IRTCEngine#setEncryptInfo} 为互斥关系，只能选择自定义加密方式或者默认加密方式。最终生效的加密方式取决于最后一个调用的方法。
     *       - 该方法必须在进房之前调用，可重复调用，以最后调用的参数作为生效参数。
     *       - 无论加密或者解密，其对原始数据的长度修改，需要控制在 180% 之间，即如果输入数据为 100 字节，则处理完成后的数据必须不超过 180 字节，如果加密或解密结果超出该长度限制，则该音视频帧可能会被丢弃。
     *       - 数据加密/解密为串行执行，因而视实现方式不同，可能会影响到最终渲染效率。是否使用该方法，需要由使用方谨慎评估。
     * @list 通话加密
     */
    /**
     * @locale en
     * @type api
     * @brief Sets custom encryption and decryption methods.
     * @param handler Custom encryption handler, you need to implement the handler's encryption and decryption method. See IEncryptHandler{@link #IEncryptHandler}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - This method is mutually exclusive with setEncryptInfo{@link #IRTCEngine#setEncryptInfo}, you can only select custom encryption method or default encryption method. The encryption method that takes effect depends on the last method called.
     *        - This method must be called before entering the room, and can be called repeatedly, taking the last called parameter as the effective parameter.
     *        - Whether encrypted or decrypted, the length of the modified data needs to be controlled under 180%. That is, if the input data is 100 bytes, the processed data must be less than 180 bytes. If the encryption or decryption result exceeds the limit, the audio & video frame may be discarded.
     *        - Data encryption/decryption is performed serially, so depending on the implementation, it may affect the final rendering efficiency. Whether to use this method needs to be carefully evaluated by users.
     * @list Encryption
     */
    virtual int setCustomizeEncryptHandler(IEncryptHandler* handler) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置并开启指定的音频数据帧回调
     * @param method 音频回调方法，参看 AudioFrameCallbackMethod{@link #AudioFrameCallbackMethod}。 <br>
     *               当音频回调方法设置为 `kRecord`、`kPlayback`、`kMixed`、`kRecordScreen`、`kCaptureMixed`时，你需要在参数 `format` 中指定准确的采样率和声道，暂不支持设置为自动。 <br>
     *               当音频回调方法设置为 `kRemoteUser`时，将 `format` 中的各个字段设置为默认值。
     * @param format 音频参数格式，参看 AudioFormat{@link #AudioFormat}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 开启音频回调并调用 registerAudioFrameObserver{@link #IRTCEngine#registerAudioFrameObserver} 后，IAudioFrameObserver{@link #IAudioFrameObserver} 会收到对应的音频回调。两者调用顺序没有限制且相互独立。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Enable audio frames callback and set the format for the specified type of audio frames.
     * @param method Audio data callback method. See AudioFrameCallbackMethod{@link #AudioFrameCallbackMethod}. <br>
     *               If `method` is set as `kRecord`, `kPlayback`, `kMixed`, `kRecordScreen`, `kCaptureMixed`, set `format` to the accurate value listed in the audio parameters format. <br>
     *               If `method` is set as `kRemoteUser`, set `format` to `auto`.
     * @param format Audio parameters format. See AudioFormat{@link #AudioFormat}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note After calling this API and registerAudioFrameObserver{@link #IRTCEngine#registerAudioFrameObserver}, IAudioFrameObserver{@link #IAudioFrameObserver} will receive the corresponding audio data callback. However, these two APIs are independent of each other and the calling order is not restricted.
     * @list Custom Stream Processing
     */
    virtual int enableAudioFrameCallback(AudioFrameCallbackMethod method, AudioFormat format) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 关闭音频回调
     * @param method 音频回调方法，参看 AudioFrameCallbackMethod{@link #AudioFrameCallbackMethod}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 该方法需要在调用 enableAudioFrameCallback{@link #IRTCEngine#enableAudioFrameCallback} 之后调用。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Disables audio data callback.
     * @param method Audio data callback method. See AudioFrameCallbackMethod{@link #AudioFrameCallbackMethod}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note Call this API after calling enableAudioFrameCallback{@link #IRTCEngine#enableAudioFrameCallback}.
     * @list Custom Stream Processing
     */
    virtual int disableAudioFrameCallback(AudioFrameCallbackMethod method) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 注册音频数据回调观察者。
     * @param observer 音频数据观察者，参看 IAudioFrameObserver{@link #IAudioFrameObserver}。如果传入 null，则取消注册。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 注册音频数据回调观察者并调用 enableAudioFrameCallback{@link #IRTCEngine#enableAudioFrameCallback} 后，IAudioFrameObserver{@link #IAudioFrameObserver} 会收到对应的音频回调。对回调中收到的音频数据进行处理，不会影响 RTC 的编码发送或渲染。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Register an audio frame observer.
     * @param observer Audio data callback observer. See IAudioFrameObserver{@link #IAudioFrameObserver}. Use `null` to cancel the registration.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note After calling this API and enableAudioFrameCallback{@link #IRTCEngine#enableAudioFrameCallback}, IAudioFrameObserver{@link #IAudioFrameObserver} receives the corresponding audio data callback. You can retrieve the audio data and perform processing on it without affecting the audio that RTC SDK uses to encode or render.
     * @list Custom Stream Processing
     */
    virtual int registerAudioFrameObserver(IAudioFrameObserver* observer) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 注册自定义音频处理器。 <br>
     *        注册完成后，你可以调用 enableAudioProcessor{@link #IRTCEngine#enableAudioProcessor}，对本地采集到的音频进行处理，RTC SDK 将对处理后的音频进行编码和发送。也可以对接收到的远端音频进行自定义处理，RTC SDK 将对处理后的音频进行渲染。
     * @param processor 自定义音频处理器，详见 IAudioFrameProcessor{@link #IAudioFrameProcessor}。 <br>
     *        SDK 只持有 processor 的弱引用，你应保证其生命周期。需要取消注册时，设置此参数为 nullptr。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     * - 重复调用此接口时，仅最后一次调用生效。
     * - 更多相关信息，详见[音频自定义处理](https://www.volcengine.com/docs/6348/80635)。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Register a custom audio preprocessor. <br>
     *        After that, you can call enableAudioProcessor{@link #IRTCEngine#enableAudioProcessor} to process the audio streams that either captured locally or received from the remote side. RTC SDK then encodes or renders the processed data.
     * @param processor Custom audio processor. See IAudioFrameProcessor{@link #IAudioFrameProcessor}。 <br>
     *        SDK only holds weak references to the processor, you should guarantee its Life Time. To cancel registration, set the parameter to nullptr.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     * - When this interface is repeatedly called, only the last call takes effect.
     * - Refer to [Custom Audio Processing](https://docs.byteplus.com/en/byteplus-rtc/docs/80635) for more information.
     * @list Custom Stream Processing
     */
    virtual int registerAudioProcessor(IAudioFrameProcessor* processor) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置并开启指定的音频帧回调，进行自定义处理。
     * @param method 音频帧类型，参看 AudioProcessorMethod{@link #AudioProcessorMethod}。可多次调用此接口，处理不同类型的音频帧。 <br>
     *        选择不同类型的音频帧将收到对应的回调： <br>
     *        - 选择本地采集的音频时，会收到 onProcessRecordAudioFrame{@link #IAudioFrameProcessor#onProcessRecordAudioFrame}。
     *        - 选择远端音频流的混音音频时，会收到 onProcessPlayBackAudioFrame{@link #IAudioFrameProcessor#onProcessPlayBackAudioFrame}。
     *        - 选择远端音频流时，会收到 onProcessRemoteUserAudioFrame{@link #IAudioFrameProcessor#onProcessRemoteUserAudioFrame}。
     *        - 选择屏幕共享音频流时，会收到 onProcessScreenAudioFrame{@link #IAudioFrameProcessor#onProcessScreenAudioFrame}。（Linux 不适用）
     * @param format 设定自定义处理时获取的音频帧格式，参看 AudioFormat{@link #AudioFormat}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 在调用此接口前，你需要调用 registerAudioProcessor{@link #IRTCEngine#registerAudioProcessor} 注册自定义音频处理器。
     *        - 要关闭音频自定义处理，调用 disableAudioProcessor{@link #IRTCEngine#disableAudioProcessor}。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Enable audio frames callback for custom processing and set the format for the specified type of audio frames.
     * @param method The types of audio frames. See AudioProcessorMethod{@link #AudioProcessorMethod}. Set this parameter to process multiple types of audio. <br>
     *        With different values, you will receive the corresponding callback: <br>
     *        - For locally captured audio, you will receive onProcessRecordAudioFrame{@link #IAudioFrameProcessor#onProcessRecordAudioFrame}.
     *        - For mixed remote audio, you will receive onProcessPlayBackAudioFrame{@link #IAudioFrameProcessor#onProcessPlayBackAudioFrame}.
     *        - For audio from remote users, you will receive onProcessRemoteUserAudioFrame{@link #IAudioFrameProcessor#onProcessRemoteUserAudioFrame}.
     *        - For shared-screen audio, you will receive onProcessScreenAudioFrame{@link #IAudioFrameProcessor#onProcessScreenAudioFrame}. (Only on Windows)
     * @param format The format of audio frames. See AudioFormat{@link #AudioFormat}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Before calling this API, call registerAudioProcessor{@link #IRTCEngine#registerAudioProcessor} to register a processor.
     *        - To disable custom audio processing, call disableAudioProcessor{@link #IRTCEngine#disableAudioProcessor}.
     * @list Custom Stream Processing
     */
    virtual int enableAudioProcessor(AudioProcessorMethod method, AudioFormat format) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 关闭自定义音频处理。
     * @param method 音频帧类型，参看 AudioProcessorMethod{@link #AudioProcessorMethod}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Disable custom audio processing.
     * @param method Audio Frame type. See AudioProcessorMethod{@link #AudioProcessorMethod}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Custom Stream Processing
     */
    virtual int disableAudioProcessor(AudioProcessorMethod method) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置自定义视频前处理器。 <br>
     *        使用这个视频前处理器，你可以调用 processVideoFrame{@link #IVideoProcessor#processVideoFrame} 对 RTC SDK 采集得到的视频帧进行前处理，并将处理后的视频帧用于 RTC 音视频通信。
     * @param processor 自定义视频处理器，详见 IVideoProcessor{@link #IVideoProcessor}。如果传入 null，则不对 RTC SDK 采集得到的视频帧进行前处理。 <br>
     *        SDK 只持有 processor 的弱引用，你应保证其生命周期。
     * @param config 自定义视频前处理器适用的设置，详见 VideoPreprocessorConfig{@link #VideoPreprocessorConfig}。 <br>
     *               当前，`config` 中的 `required_pixel_format` 仅支持：`kVideoPixelFormatI420` 和 `kVideoPixelFormatUnknown`： <br>
     *               - 设置为 `kVideoPixelFormatUnknown` 时，RTC SDK 给出供 processor 处理的视频帧格式即采集的格式。
     *                 你可以通过 VideoFrameType{@link #VideoFrameType} 和 VideoPixelFormat{@link #VideoPixelFormat} 获取实际采集的视频帧格式和像素类型。 <br>
     *               - 设置为 `kVideoPixelFormatI420` 时，RTC SDK 会将采集得到的视频帧转变为对应的格式，供前处理使用。
     *               - 设置为其他值时，此方法调用失败。
     * @return
     *         - 0：方法调用成功
     *         - !0：方法调用失败
     * @note
     *        - 对于 Windows 平台，经前处理返回的视频帧格式仅支持 `kVideoPixelFormatI420`
     *        - 对于 Windows 平台，将 `config` 中的 required_pixel_format 设置为 `kVideoPixelFormatI420`，可以通过避免格式转换带来一些性能优化。
     *        - 重复调用此接口时，仅最后一次调用生效。效果不会叠加。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Set up a custom video preprocessor. <br>
     *        Using this video preprocessor, you can call processVideoFrame{@link #IVideoProcessor#processVideoFrame} to preprocess the video frames collected by the RTC SDK, and use the processed video frames for RTC audio & video communication.
     * @param processor Custom video processor. See IVideoProcessor{@link #IVideoProcessor}. If null is passed in, the video frames captured by the RTC SDK are not preprocessed. <br>
     *        SDK only holds weak references to the processor, you should guarantee its Life Time.
     * @param config Customize the settings applicable to the video pre-processor. See VideoPreprocessorConfig{@link #VideoPreprocessorConfig}. <br>
     *             Currently, the 'required_pixel_format 'in'config' only supports: 'kVideoPixelFormatI420' and'kVideoPixelFormatUnknown ': <br>
     *             - When set to'kVideoPixelFormatUnknown', the RTC SDK gives the format of the video frame for processing by the processor, that is, the format of the acquisition.
     *               You can get the actual captured video frame format and pixel type by VideoFrameType{@link #VideoFrameType} and VideoPixelFormat{@link #VideoPixelFormat}. <br>
     *             - When set to'kVideoPixelFormatI420 ', the RTC SDK converts the captured video frames into the corresponding format for pre-processing. This method call fails when
     *             - Is set to another value.
     * @return
     *         - 0: Success.
     *         +! 0: failure
     * @note
     *        - For Windows platforms, the preprocessed video frame format returned only supports'kVideoPixelFormatI420 '
     *        - For Windows platforms, setting the required_pixel_format in'config' to'kVideoPixelFormatI420 'can bring some performance optimization by avoiding format conversion.
     *        - When this interface is repeatedly called, only the last call takes effect. The effects do not stack.
     * @list Custom Stream Processing
     */
    virtual int registerLocalVideoProcessor(IVideoProcessor* processor, VideoPreprocessorConfig config) = 0;
    /**
     * @locale zh
     * @valid since 3.51
     * @type api
     * @brief 设置本地摄像头数码变焦参数，包括缩放倍数，移动步长。
     * @param type 数码变焦参数类型，缩放系数或移动步长。参看 ZoomConfigType{@link #ZoomConfigType}。必填。
     * @param size 缩放系数或移动步长，保留到小数点后三位。默认值为 0。必填。 <br>
     *                  选择不同 `type` 时有不同的取值范围。当计算后的结果超过缩放和移动边界时，取临界值。 <br>
     *                  - `kZoomConfigTypeFocusOffset`：缩放系数增量，范围为 [0, 7]。例如，设置为 0.5 时，如果调用 setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl} 选择 Zoom in，则缩放系数增加 0.5。缩放系数范围 [1，8]，默认为 `1`，原始大小。
     *                  - `kZoomConfigTypeMoveOffset`：移动百分比，范围为 [0, 0.5]，默认为 0，不移动。如果调用 setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl} 选择的是左右移动，则移动距离为 size x 原始视频宽度；如果选择的是上下移动，则移动距离为 size x 原始视频高度。例如，视频帧边长为 1080 px，设置为 0.5 时，实际移动距离为 0.5 x 1080 px = 540 px。
     * @return
     *        - 0：成功。
     *        - !0：失败。
     * @note
     *        - 每次调用本接口只能设置一种参数。如果缩放系数和移动步长都需要设置，分别调用本接口传入相应参数。
     *        - 由于移动步长的默认值为 `0` ，在调用 setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl} 或 startVideoDigitalZoomControl{@link #IRTCEngine#startVideoDigitalZoomControl} 进行数码变焦操作前，应先调用本接口。
     * @list 视频处理
     */
    /**
     * @locale en
     * @valid since 3.51
     * @type api
     * @brief Set the step size for each digital zooming control to the local videos.
     * @param type Required. Identifying which type the `size` is referring to. Refer to ZoomConfigType{@link #ZoomConfigType}.
     * @param size Required. Reserved to three decimal places. It defaults to `0`. <br>
     *                  The meaning and range vary from different `type`s. If the scale or moving distance exceeds the range, the limit is taken as the result. <br>
     *                  - `kZoomConfigTypeFocusOffset`: Increasement or decrease to the scaling factor. Range: [0, 7]. For example, when it is set to 0.5 and setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl} is called to zoom in, the scale will increase `0.5`. The scale ranges [1，8] and defaults to `1`, which means an original size.
     *                  - `kZoomConfigTypeMoveOffset`：Ratio of the distance to the border of video images. It ranges [0, 0.5] and defaults to `0`, which means no offset. When you call setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl} and choose `CAMERA_MOVE_LEFT`, the moving distance is size x original width. While for the `CAMERA_MOVE_UP`, the moving distance is size x original height. Suppose that a video spans 1080 px and the `size` is set to `0.5` so that the distance would be 0.5 x 1080 px = 540 px.
     * @return
     *        - 0: Success.
     *        - ! 0: Failure.
     * @note
     *        - Only one size can be set for a single call. You must call this API to pass values respectively if you intend to set multiple `size`s.
     *        - As the default `size` is `0`, you must call this API before performing any digital zoom control by calling setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl} or startVideoDigitalZoomControl{@link #IRTCEngine#startVideoDigitalZoomControl}.
     * @list Video Processing
     */
    virtual int setVideoDigitalZoomConfig(ZoomConfigType type, float size) = 0;
    /**
     * @locale zh
     * @valid since 3.51
     * @type api
     * @brief 控制本地摄像头数码变焦，缩放或移动一次。设置对本地预览画面和发布到远端的视频都生效。
     * @param direction 数码变焦操作类型，参看 ZoomDirectionType{@link #ZoomDirectionType}。
     * @return
     *        - 0：成功。
     *        - !0：失败。
     * @note
     *        - 由于默认步长为 `0`，调用该方法前需通过 setVideoDigitalZoomConfig{@link #IRTCEngine#setVideoDigitalZoomConfig} 设置参数。
     *        - 调用该方法进行移动前，应先使用本方法或 startVideoDigitalZoomControl{@link #IRTCEngine#startVideoDigitalZoomControl} 进行放大，否则无法移动。
     *        - 当数码变焦操作超出范围时，将置为临界值。例如，移动到了图片边界、放大到了 8 倍、缩小到原图大小。
     *        - 如果你希望实现持续数码变焦操作，调用 startVideoDigitalZoomControl{@link #IRTCEngine#startVideoDigitalZoomControl}。
     *        - 移动端可对摄像头进行光学变焦控制，参看 `setCameraZoomRatio`。
     * @list 视频处理
     */
    /**
     * @locale en
     * @valid since 3.51
     * @type api
     * @brief Digital zoom or move the local video image once. This action affects both the video preview locally and the stream published.
     * @param direction Action of the digital zoom control. Refer to ZoomDirectionType{@link #ZoomDirectionType}.
     * @return
     *        - 0: Success.
     *        - ! 0: Failure.
     * @note
     *        - As the default offset is `0`, you must call setVideoDigitalZoomConfig{@link #IRTCEngine#setVideoDigitalZoomConfig} before this API.
     *        - You can only move video images after they are magnified via this API or startVideoDigitalZoomControl{@link #IRTCEngine#startVideoDigitalZoomControl}.
     *        - When you request an out-of-range scale or movement, SDK will execute it with the limits. For example, when the image has been moved to the border, the image cannot be zoomed out, or has been magnified to 8x.
     *        - Call startVideoDigitalZoomControl{@link #IRTCEngine#startVideoDigitalZoomControl} to have a continuous and repeatedly digital zoom control.
     *        - Mobile devices can control the optical zoom of the camera, see `setCameraZoomRatio`.
     */
    virtual int setVideoDigitalZoomControl(ZoomDirectionType direction) = 0;
    /**
     * @locale zh
     * @valid since 3.51
     * @type api
     * @brief 开启本地摄像头持续数码变焦，缩放或移动。设置对本地预览画面和发布到远端的视频都生效。
     * @param direction 数码变焦操作类型，参看 ZoomDirectionType{@link #ZoomDirectionType}。
     * @return
     *        - 0：成功。
     *        - !0：失败。
     * @note
     *        - 由于默认步长为 `0`，调用该方法前需通过 setVideoDigitalZoomConfig{@link #IRTCEngine#setVideoDigitalZoomConfig} 设置参数。
     *        - 调用该方法进行移动前，应先使用本方法或 setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl} 进行放大，否则无法移动。
     *        - 当数码变焦操作超出范围时，将置为临界值并停止操作。例如，移动到了图片边界、放大到了 8 倍、缩小到原图大小。
     *        - 你也可以调用 stopVideoDigitalZoomControl{@link #IRTCEngine#stopVideoDigitalZoomControl} 手动停止控制。
     *        - 如果你希望实现单次数码变焦操作，调用 setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl}。
     *        - 移动端可对摄像头进行光学变焦控制，参看 `setCameraZoomRatio`。
     * @list 视频处理
     */
    /**
     * @locale en
     * @valid since 3.51
     * @type api
     * @brief Continuous and repeatedly digital zoom control. This action effect both the video preview locally and the stream published.
     * @param direction Action of the digital zoom control. Refer to ZoomDirectionType{@link #ZoomDirectionType}.
     * @return
     *        - 0: Success.
     *        - ! 0: Failure.
     * @note
     *        - As the default offset is `0`, you must call setVideoDigitalZoomConfig{@link #IRTCEngine#setVideoDigitalZoomConfig} before this API.
     *        - You can only move video images after they are magnified via this API or setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl}.
     *        - The control process stops when the scale reaches the limit, or the images have been moved to the border. if the next action exceeds the scale or movement range, SDK will execute it with the limits.
     *        - Call stopVideoDigitalZoomControl{@link #IRTCEngine#stopVideoDigitalZoomControl} to stop the ongoing zoom control.
     *        - Call setVideoDigitalZoomControl{@link #IRTCEngine#setVideoDigitalZoomControl} to have a one-time digital zoom control.
     *        - Mobile devices can control the optical zoom of the camera, see `setCameraZoomRatio`.
     */
    virtual int startVideoDigitalZoomControl(ZoomDirectionType direction) = 0;
    /**
     * @locale zh
     * @valid since 3.51
     * @type api
     * @brief 停止本地摄像头持续数码变焦。
     * @return
     *        - 0：成功。
     *        - !0：失败。
     * @note 关于开始数码变焦，参看 startVideoDigitalZoomControl{@link #IRTCEngine#startVideoDigitalZoomControl}。
     * @list 视频处理
     */
    /**
     * @locale en
     * @valid since 3.51
     * @type api
     * @brief Stop the ongoing digital zoom control instantly.
     * @return
     *        - 0: Success.
     *        - ! 0: Failure.
     * @note Refer to startVideoDigitalZoomControl{@link #IRTCEngine#startVideoDigitalZoomControl} for starting digital zooming.
     * @list Video Processing
     */
    virtual int stopVideoDigitalZoomControl() = 0;
    /**
     * @locale zh
     * @type api
     * @brief 通过视频帧发送 SEI 数据。 <br>
     *        在视频通话场景下，SEI 数据会随视频帧发送；在语音通话场景下，SDK 会自动生成一路 16px × 16px 的黑帧视频流用来发送 SEI 数据。
     * @param stream_index 指定携带 SEI 数据的媒体流类型，参看 StreamIndex{@link #StreamIndex}。 <br>
     *        语音通话场景下，该值需设为 `kStreamIndexMain`，否则 SEI 数据会被丢弃从而无法送达远端。
     * @param message SEI 消息。
     * @param length SEI 消息长度，建议每帧 SEI 数据总长度长度不超过 4 KB。
     * @param repeat_count 消息发送重复次数。取值范围是 [0, max{29, %{视频帧率}-1}]。推荐范围 [2,4]。 <br>
     *                    调用此接口后，SEI 数据会添加到从当前视频帧开始的连续 `repeat_count+1` 个视频帧中。
     * @param mode SEI 发送模式，参看 SEICountPerFrame{@link #SEICountPerFrame}。
     * @return
     *        - >= 0: 将被添加到视频帧中的 SEI 的数量。
     *        - < 0: 发送失败。
     * @note
     *        - 每秒发送的 SEI 消息数量建议不超过当前的视频帧率。在语音通话场景下，黑帧帧率为 15 fps。
     *        - 语音通话场景中，仅支持在内部采集模式下调用该接口发送 SEI 数据。
     *        - 视频通话场景中，使用自定义采集并通过 pushExternalVideoFrame{@link #IRTCEngine#pushExternalVideoFrame} 推送至 SDK 的视频帧，若本身未携带 SEI 数据，也可通过本接口发送 SEI 数据；若原视频帧中已添加了 SEI 数据，则调用此方法不生效。
     *        - 视频帧仅携带前后 2s 内收到的 SEI 数据；语音通话场景下，若调用此接口后 1min 内未有 SEI 数据发送，则 SDK 会自动取消发布视频黑帧。
     *        - 消息发送成功后，远端会收到 onSEIMessageReceived{@link #IRTCEngineEventHandler#onSEIMessageReceived} 回调。
     *        - 语音通话切换至视频通话时，会停止使用黑帧发送 SEI 数据，自动转为用采集到的正常视频帧发送 SEI 数据。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief <span id="IRTCEngine-sendseimessage-2"></span> Sends SEI data. <br>
     *        In a video call scenario, SEI is sent with the video frame, while in a voice call scenario, SDK will automatically publish a black frame with a resolution of 16 × 16 pixels to carry SEI data.
     * @param stream_index Specifys the type of media stream that carries SEI data. See StreamIndex{@link #StreamIndex}. <br>
     *        In a voice call, you should set this parameter to `kStreamIndexMain`, otherwise the SEI data is discarded and cannot be sent to the remote user.
     * @param message SEI data.
     * @param length SEI data length. No more than 4 KB SEI data per frame is recommended.
     * @param repeat_count Number of times a message is sent repeatedly. The value range is [0, max{29, %{video frame rate}-1}]. Recommended range: [2,4]. <br>
     *                    After calling this API, the SEI data will be added to a consecutive `repeat_count+1` number of video frames starting from the current frame.
     * @param mode SEI sending mode. See SEICountPerFrame{@link #SEICountPerFrame}.
     * @return
     *         - >= 0: The number of SEIs to be added to the video frame
     *         - < 0: Failure
     * @note
     *         - We recommend the number of SEI messages per second should not exceed the current video frame rate. In a voice call, the blank-frame rate is 15 fps.
     *         - In a voice call, this API can be called to send SEI data only in internal capture mode.
     *         - In a video call, the custom captured video frame can also be used for sending SEI data if the original video frame contains no SEI data, otherwise calling this method will not take effect.
     *         - Each video frame carrys only the SEI data received within 2s before and after. In a voice call scenario, if no SEI data is sent within 1min after calling this API, SDK will automatically cancel publishing black frames.
     *         - After the message is sent successfully, the remote user who subscribed your video stream will receive onSEIMessageReceived{@link #IRTCEngineEventHandler#onSEIMessageReceived}.
     *         - When you switch from a voice call to a video call, SEI data will automatically start to be sent with normally captured video frames instead of black frames.
     * @list Messaging
     */
    virtual int sendSEIMessage(StreamIndex stream_index, const uint8_t* message, int length, int repeat_count, SEICountPerFrame mode) = 0;
    /**
     * @locale zh
     * @hidden for internal use only
     * @valid since 3.56
     * @type api
     * @brief WTN 流视频帧发送 SEI 数据。
     * @param stream_index 指定携带 SEI 数据的媒体流类型，参看 StreamIndex{@link #StreamIndex}。
     * @param channel_id SEI 消息传输通道，取值范围 [0 - 255]。通过此参数，你可以为不同接受方设置不同的 ChannelID，这样不同接收方可以根据回调中的 ChannelID 选择应关注的 SEI 信息。
     * @param message SEI 消息。
     * @param length SEI 消息长度，建议每帧 SEI 数据总长度长度不超过 4 KB。
     * @param repeat_count 消息发送重复次数。取值范围是 [0, max{29, %{视频帧率}-1}]。推荐范围 [2,4]。 <br>
     *                    调用此接口后，SEI 数据会添加到从当前视频帧开始的连续 `repeat_count+1` 个视频帧中。
     * @param mode SEI 发送模式，参看 SEICountPerFrame{@link #SEICountPerFrame}。
     * @return
     *        - < 0：说明调用失败
     *        - = 0：说明当前发送队列已满，无法发送
     *        - > 0: 说明调用成功，该数值为已经发送 SEI 的数量
     * @note
     *        - 每秒发送的 SEI 消息数量建议不超过当前的视频帧率
     *        - 视频通话场景中，使用自定义采集并通过 pushExternalVideoFrame{@link #IRTCEngine#pushExternalVideoFrame} 推送至 SDK 的视频帧，若本身未携带 SEI 数据，也可通过本接口发送 SEI 数据；若原视频帧中已添加了 SEI 数据，则调用此方法不生效。
     *        - 视频帧仅携带前后 2s 内收到的 SEI 数据
     *        - 消息发送成功后，远端会收到 onPublicStreamSEIMessageReceivedWithChannel{@link #IRTCEngineEventHandler#onPublicStreamSEIMessageReceivedWithChannel} 回调。
     *        - 调用失败时，本地及远端都不会收到回调。
     * @list 消息
     */
    /**
     * @locale en
     * @hidden for internal use only
     * @valid since 3.56
     * @type api
     * @brief <span id="IRTCEngine-sendseimessage-2"></span> WTN stream sends SEI data.
     * @param stream_index Specifys the type of media stream that carries SEI data. See StreamIndex{@link #StreamIndex}.
     * @param channel_id SEI message channel id. The value range is [0 - 255]. With this parameter, you can set different ChannelIDs for different recipients. In this way, different recipients can choose the SEI information  based on the ChannelID received in the callback.
     * @param message SEI data.
     * @param length SEI data length. No more than 4 KB SEI data per frame is recommended.
     * @param repeat_count Number of times a message is sent repeatedly. The value range is [0, max{29, %{video frame rate}-1}]. Recommended range: [2,4]. <br>
     *                    After calling this API, the SEI data will be added to a consecutive `repeat_count+1` number of video frames starting from the current frame.
     * @param mode SEI sending mode. See SEICountPerFrame{@link #SEICountPerFrame}.
     * @return
     *         - < 0：Failure
     *         - = 0: You are unable to send SEI as the current send queue is full.
     *         - > 0: Success, and the value represents the amount of sent SEI.
     * @note
     *         - We recommend the number of SEI messages per second should not exceed the current video frame rate.
     *         - In a video call, the custom captured video frame can also be used for sending SEI data if the original video frame contains no SEI data, otherwise calling this method will not take effect.
     *         - Each video frame carrys only the SEI data received within 2s before and after. In a voice call scenario, if no SEI data is sent within 1min after calling this API, SDK will automatically cancel publishing black frames.
     *         - After the message is sent successfully, the remote user who subscribed your video stream will receive onPublicStreamSEIMessageReceivedWithChannel{@link #IRTCEngineEventHandler#onPublicStreamSEIMessageReceivedWithChannel}.
     *         - When the call fails, neither the local nor the remote side will receive a callback.
     * @list Messaging
     */
    virtual int sendPublicStreamSEIMessage(StreamIndex stream_index, int channel_id, const uint8_t* message, int length,
                                           int repeat_count, SEICountPerFrame mode) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 创建视频设备管理实例
     * @return 视频设备管理实例，详见 IVideoDeviceManager{@link #IVideoDeviceManager}
     * @list 视频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Create a video Facility Management instance
     * @return Video Facility Management instance. See IVideoDeviceManager{@link #IVideoDeviceManager}
     * @list Video Management
     */
    virtual IVideoDeviceManager* getVideoDeviceManager() = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @type api
     * @brief 设备音频管理接口创建
     * @return 音频设备管理接口，详见 IAudioDeviceManager{@link #IAudioDeviceManager}
     * @list 音频管理
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @type api
     * @brief Device audio management interface creation
     * @return Audio Facility Management interface
     * @list Audio Management
     */
    virtual IAudioDeviceManager* getAudioDeviceManager() = 0;
    /**
     * @locale zh
     * @type api
     * @brief 录制通话过程中的音视频数据到本地的文件中。
     * @param type 流属性，指定录制主流还是屏幕流，参看 StreamIndex{@link #StreamIndex}
     * @param config 本地录制参数配置，参看 RecordingConfig{@link #RecordingConfig}
     * @param recording_type 本地录制的媒体类型，参看 RecordingType{@link #RecordingType}
     * @return
     *        - 0: 正常
     *        - -1: 参数设置异常
     *        - -2: 当前版本 SDK 不支持该特性，请联系技术支持人员
     * @note
     *        - 该方法需在进房后调用。
     *        - 调用该方法后，你会收到 onRecordingStateUpdate{@link #IRTCEngineEventHandler#onRecordingStateUpdate} 回调。
     *        - 如果录制正常，系统每秒钟会通过 onRecordingProgressUpdate{@link #IRTCEngineEventHandler#onRecordingProgressUpdate} 回调通知录制进度。
     * @list 高级功能
     */
    /**
     * @locale en
     * @type api
     * @brief This method records the audio & video data during the call to a local file.
     * @param type Stream attribute, specify whether to record mainstream or screen stream. See StreamIndex{@link #StreamIndex}
     * @param config Local recording parameter configuration. See RecordingConfig{@link #RecordingConfig}
     * @param recording_type Local recording media type. See RecordingType{@link #RecordingType}.
     * @return
     *         - 0: normal
     *         - -1: Parameter setting exception
     *         - -2: The current version of the SDK does not support this Feature, please contact technical support staff
     * @note
     *         - You must join a room before calling this method.
     *         - After calling this method, you will receive an onRecordingStateUpdate{@link #IRTCEngineEventHandler#onRecordingStateUpdate} callback.
     *         - If the recording is normal, the system will notify the recording progress through onRecordingProgressUpdate{@link #IRTCEngineEventHandler#onRecordingProgressUpdate} Callback every second.
     * @list Advanced Features
     */
    virtual int startFileRecording(StreamIndex type, RecordingConfig config, RecordingType recording_type) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 停止本地录制
     * @param type 流属性，指定停止主流或者屏幕流录制，参看 StreamIndex{@link #StreamIndex}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 调用 startFileRecording{@link #IRTCEngine#startFileRecording} 开启本地录制后，你必须调用该方法停止录制。
     *        - 调用该方法后，你会收到 onRecordingStateUpdate{@link #IRTCEngineEventHandler#onRecordingStateUpdate} 回调提示录制结果。
     * @list 高级功能
     */
    /**
     * @locale en
     * @type api
     * @brief Stop local recording
     * @param type Stream attribute, specify to stop mainstream or screen stream recording. See StreamIndex{@link #StreamIndex}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - Call startFileRecording{@link #IRTCEngine#startFileRecording} After starting local recording, you must call this method to stop recording.
     *         - After calling this method, you will receive an onRecordingStateUpdate{@link #IRTCEngineEventHandler#onRecordingStateUpdate} callback prompting you to record the result.
     * @list Advanced Features
     */
    virtual int stopFileRecording(StreamIndex type) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 开启录制语音通话，生成本地文件。 <br>
     *        在进房前后开启录制，如果未打开麦克风采集，录制任务正常进行，只是不会将数据写入生成的本地文件；只有调用 startAudioCapture{@link #IRTCEngine#startAudioCapture} 接口打开麦克风采集后，才会将录制数据写入本地文件。
     * @param config 参看 AudioRecordingConfig{@link #AudioRecordingConfig}
     * @return
     *        - 0: 正常
     *        - -2: 参数设置异常
     *        - -3: 当前版本 SDK 不支持该特性，请联系技术支持人员
     * @note
     *        - 录制包含各种音频效果。但不包含背景音乐。
     *        - 调用 stopAudioRecording{@link #IRTCEngine#stopAudioRecording} 关闭录制。
     *        - 加入房间前后均可调用。在进房前调用该方法，退房之后，录制任务不会自动停止，需调用 stopAudioRecording{@link #IRTCEngine#stopAudioRecording} 关闭录制。在进房后调用该方法，退房之后，录制任务会自动被停止。如果加入了多个房间，录制的文件中会包含各个房间的音频。
     *        - 调用该方法后，你会收到 onAudioRecordingStateUpdate{@link #IRTCEngineEventHandler#onAudioRecordingStateUpdate} 回调。
     * @list 高级功能
     */
    /**
     * @locale en
     * @type api
     * @brief Starts recording audio communication, and generate the local file. <br>
     *        If you call this API before or after joining the room without internal audio capture, then the recording task can still begin but the data will not be recorded in the local files. Only when you call startAudioCapture{@link #IRTCEngine#startAudioCapture} to enable internal audio capture, the data will be recorded in the local files.
     * @param config See AudioRecordingConfig{@link #AudioRecordingConfig}.
     * @return
     *        - 0: `kReturnStatusSuccess`: Success
     *        - -2: `kReturnStatusParameterErr`: Invalid parameters
     *        - -3: `kReturnStatusWrongState`: Not valid in this SDK. Please contact the technical support.
     * @note
     *        - All audio effects are valid in the file. Mixed audio file is not included in the file.
     *        - Call stopAudioRecording{@link #IRTCEngine#stopAudioRecording} to stop recording.
     *        - You can call this API before and after joining the room. If this API is called before you join the room, you need to call stopAudioRecording{@link #IRTCEngine#stopAudioRecording} to stop recording. If this API is called after you join the room, the recording task ends automatically. If you join multiple rooms, audio from all rooms are recorded in one file.
     *        - After calling the API, you'll receive onAudioRecordingStateUpdate{@link #IRTCEngineEventHandler#onAudioRecordingStateUpdate}.
     * @list Advanced Features
     */
    virtual int startAudioRecording(AudioRecordingConfig& config) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 停止音频文件录制
     * @return
     *         - 0: 正常
     *         - -3: 当前版本 SDK 不支持该特性，请联系技术支持人员
     * @note 调用 startAudioRecording{@link #IRTCEngine#startAudioRecording} 开启本地录制。
     * @list 高级功能
     */
     /**
      * @locale en
      * @type api
      * @brief Stop audio recording.
      * @return
      *         - 0: Success
      *         - <0: Failure
      * @note Call startAudioRecording{@link #IRTCEngine#startAudioRecording} to start the recording task.
      * @list Advanced Features
      */
    virtual int stopAudioRecording() = 0;
    /**
     * @locale zh
     * @type api
     * @brief 启用匹配外置声卡的音频处理模式
     * @param enable <br>
     *        - true: 开启
     *        - false: 不开启(默认)
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 当采用外接声卡进行音频采集时，建议开启此模式，以获得更好的音质。
     *        - 开启此模式时，仅支持耳机播放。如果需要使用扬声器或者外置音箱播放，关闭此模式。
     * @list 音频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Enable the audio process mode for external sound card.
     * @param enable <br>
     *        - true: enable
     *        - false: disable (by default)
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - When you use external sound card for audio capture, enable this mode for better audio quality.
     *         - When using the mode, you can only use earphones. If you need to use internal or external speaker, disable this mode.
     * @list Audio Management
     */
    virtual int enableExternalSoundCard(bool enable) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置运行时的参数
     * @param json_string  json 序列化之后的字符串
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 该接口需在 joinRoom{@link #IRTCRoom#joinRoom} 和 startAudioCapture{@link #IRTCEngine#startAudioCapture} 之前调用。
     * @list 引擎管理
     */
    /**
     * @locale en
     * @type api
     * @brief Set runtime parameters
     * @param json_string String after json serialization
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note Call this API before joinRoom{@link #IRTCRoom#joinRoom} and startAudioCapture{@link #IRTCEngine#startAudioCapture}.
     * @list Engine Management
     */
    virtual int setRuntimeParameters(const char * json_string) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 将用户反馈的问题上报到 RTC。
     * @param type 预设问题列表，参看 ProblemFeedbackOption{@link #ProblemFeedbackOption}
     * @param info 预设问题以外的其他问题的具体描述，房间信息。 参看 ProblemFeedbackInfo{@link #ProblemFeedbackInfo}
     * @return
     *         - 0: 上报成功
     *         - -1: 上报失败，还没加入过房间
     *         - -2: 上报失败，数据解析错误
     *         - -3: 上报失败，字段缺失
     * @note
     *         - 你可以在 [RTC 控制台](https://console.volcengine.com/rtc/callQualityRTC/feedback)上查看用户通过此接口提交的反馈详情和整体趋势。
     *         - 如果用户上报时在房间内，那么问题会定位到用户当前所在的一个或多个房间；如果用户上报时不在房间内，那么问题会定位到引擎此前退出的房间。
     * @list 引擎管理
     */
    /**
     * @locale en
     * @type api
     * @brief Report the user feedback to RTC.
     * @param type List of preset problems. See ProblemFeedbackOption{@link #ProblemFeedbackOption}
     * @param info Specific description of other problems other than the preset problem, and room's information. See ProblemFeedbackInfo{@link #ProblemFeedbackInfo}
     * @return
     *          - 0: Report successfully
     *          - -1: Failed to report, not yet joined the room
     *          - -2: Failed to report, data analysis error
     *          - -3: Failed to report, missing fields
     * @note If the user is in the room when reporting, the report leads to the room / rooms where the user is currently located; <br>
     *        If the user is not in the room when reporting, the report leads to the previously exited Room.
     * @list Engine Management
     */
    virtual int feedback(uint64_t type, const ProblemFeedbackInfo* info) = 0;
    /**
     * @locale zh
     * @valid since 3.53
     * @type api
     * @brief 创建音效播放器实例。
     * @return 音效播放器。详见 IAudioEffectPlayer{@link #IAudioEffectPlayer}。
     * @list 混音
     */
    /**
     * @locale en
     * @valid since 3.53
     * @type api
     * @brief Create an instance for audio effect player.
     * @return See IAudioEffectPlayer{@link #IAudioEffectPlayer}.
     * @list Audio Mixing
     */
    virtual IAudioEffectPlayer* getAudioEffectPlayer() = 0;
    /**
     * @locale zh
     * @valid since 3.53
     * @type api
     * @brief 创建音乐播放器实例。
     * @param player_id 音乐播放器实例 id。取值范围为 `[0, 3]`。最多同时存在 4 个实例，超出取值范围时返回 nullptr。
     * @return 音乐播放器实例，详见 IMediaPlayer{@link #IMediaPlayer}
     * @list 混音
     */
    /**
     * @locale en
     * @valid since 3.53
     * @type api
     * @brief Create a media player instance.
     * @param player_id Media player id. The range is `[0, 3]`. You can create up to 4 instances at the same time. If it exceeds the range, nullptr will be returned.
     * @return Media player instance. See IMediaPlayer{@link #IMediaPlayer}.
     * @list Audio Mixing
     */
    virtual IMediaPlayer* getMediaPlayer(int player_id) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 登陆 RTS 服务器。 <br>
     *        必须先登录，才能调用 sendUserMessageOutsideRoom{@link #IRTCEngine#sendUserMessageOutsideRoom} 和 sendServerMessage{@link #IRTCEngine#sendServerMessage} 发送房间外点对点消息和向应用服务器发送消息 <br>
     *        在调用本接口登录后，如果想要登出，需要调用 logout{@link #IRTCEngine#logout}。
     * @param token 用户登录必须携带的 Token，用于鉴权验证。 <br>
     *               测试时可使用[控制台](https://console.volcengine.com/rtc/listRTC)生成临时 Token，`roomId` 填任意值。 <br>
     *               正式上线需要使用密钥 SDK 在你的服务端生成并下发 Token，`roomId` 置空，Token 有效期及生成方式参看[使用 Token 完成鉴权](70121)。
     * @param uid <br>
     *        用户 ID <br>
     *        用户 ID 在 appid 的维度下是唯一的。
     * @return
     *        - 0：成功；
     *        - <0：失败。具体失败原因参看 ReturnStatus{@link #ReturnStatus}。
     * @note 本地用户调用此方法登录后，会收到 onLoginResult{@link #IRTCEngineEventHandler#onLoginResult} 回调通知登录结果，远端用户不会收到通知。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief Log in to RTS server. 
     * Log in to call sendUserMessageOutsideRoom{@link #IRTCEngine#sendUserMessageOutsideRoom} and sendServerMessage{@link #IRTCEngine#sendServerMessage} to send P2P messages or send messages to a server without joining the RTC room. <br>
     *        To log out, call logout{@link #IRTCEngine#logout}.
     * @param token <br>
     *        Token is required during login for authentication. <br>
     *        This Token is different from that required by calling joinRoom. You can assign any value even null to `roomId` to generate a login token. During developing and testing, you can use temporary tokens generated on the console. Deploy the token generating application on your server.
     * @param uid <br>
     *        User ID <br>
     *        User ID is unique within one appid.
     * @return
     *        - 0: Success.
     *        - <0: Failure. See ReturnStatus{@link #ReturnStatus} for specific reasons.
     * @note  You will receive onLoginResult{@link #IRTCEngineEventHandler#onLoginResult} after calling this API and log in successfully. But remote users will not receive notification about that.
     * @list Messaging
     */
    virtual int login(const char* token, const char* uid) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 登出 RTS 服务器。 <br>
     *        调用本接口登出后，无法调用房间外消息以及端到服务器消息相关的方法或收到相关回调。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 调用本接口登出前，必须先调用 login{@link #IRTCEngine#login} 登录。
     *       - 本地用户调用此方法登出后，会收到 onLogout{@link #IRTCEngineEventHandler#onLogout} 回调通知结果，远端用户不会收到通知。
     * @list 消息
     */
    /**
     * @locale en
     * @type After api
     * @brief Log out of RTS server. <br>
     *        Calls this interface to log out, it is impossible to call methods related to out-of-room messages and end-to-server messages or receive related callbacks.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Before calling this interface to log out, you must call login{@link #IRTCEngine#login} to log in.
     *        - After local users call this method to log out, they will receive the result of the onLogout{@link #IRTCEngineEventHandler#onLogout} callback notification, and remote users will not receive the notification.
     * @list Messaging
     */
    virtual int logout() = 0;
    /**
     * @locale zh
     * @type api
     * @brief 更新用户用于登录的 Token <br>
     *        Token 有一定的有效期，当 Token 过期时，需调用此方法更新登录的 Token 信息。 <br>
     *        调用 login{@link #IRTCEngine#login} 方法登录时，如果使用了过期的 Token 将导致登录失败，并会收到 onLoginResult{@link #IRTCEngineEventHandler#onLoginResult} 回调通知，错误码为 kLoginErrorCodeInvalidToken。此时需要重新获取 Token，并调用此方法更新 Token。
     * @param token <br>
     *        更新的动态密钥
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 如果 Token 无效导致登录失败，则调用此方法更新 Token 后，SDK 会自动重新登录，而用户不需要自己调用 login{@link #IRTCEngine#login} 方法。
     *       - Token 过期时，如果已经成功登录，则不会受到影响。Token 过期的错误会在下一次使用过期 Token 登录时，或因本地网络状况不佳导致断网重新登录时通知给用户。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief Update the Token <br>
     *        Token used by the user to log in has a certain valid period. When the Token expires, you need to call this method to update the login Token information. <br>
     *         When calling the login{@link #IRTCEngine#login} method to log in, if an expired token is used, the login will fail and you will receive an onLoginResult{@link #IRTCEngineEventHandler#onLoginResult} callback notification with an error code of kLoginErrorCodeInvalidToken. You need to reacquire the token and call this method to update the token.
     * @param token <br>
     *        Updated dynamic key
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - If the token is invalid and the login fails, call this method. After updating the token, the SDK will automatically log back in, and the user does not need to call the login{@link #IRTCEngine#login} method.
     *        - Token expires, if you have successfully logged in, it will not be affected. An expired Token error will be notified to the user the next time you log in with an expired Token, or when you log in again due to a disconnection due to poor local network conditions.
     * @list Messaging
     */
    virtual int updateLoginToken(const char* token) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置应用服务器参数 <br>
     *        客户端调用 sendServerMessage{@link #IRTCEngine#sendServerMessage} 或 sendServerBinaryMessage{@link #IRTCEngine#sendServerBinaryMessage} 发送消息给应用服务器之前，必须设置有效签名和应用服务器地址。
     * @param signature 动态签名，应用服务器可使用该签名验证消息来源。 <br>
     *        签名需自行定义，可传入任意非空字符串，建议将 uid 等信息编码为签名。 <br>
     *        设置的签名会以 post 形式发送至通过本方法中 url 参数设置的应用服务器地址。
     * @param url 应用服务器的地址
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 用户必须调用 login{@link #IRTCEngine#login} 登录后，才能调用本接口。
     *       - 调用本接口后，SDK 会使用 onServerParamsSetResult{@link #IRTCEngineEventHandler#onServerParamsSetResult} 返回相应结果。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief Set application server parameters <br>
     *        Client side calls sendServerMessage{@link #IRTCEngine#sendServerMessage} or sendServerBinaryMessage{@link #IRTCEngine#sendServerBinaryMessage} Before sending a message to the application server, a valid signature and application server address must be set.
     * @param signature Dynamic signature. The App server may use the signature to verify the source of messages. <br>
     *        You need to define the signature yourself. It can be any non-empty string. It is recommended to encode information such as UID into the signature. <br>
     *        The signature will be sent to the address set through the "url" parameter in the form of a POST request.
     * @param url The address of the application server
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - The user must call login{@link #IRTCEngine#login} to log in before calling this interface.
     *        - After calling this interface, the SDK will use onServerParamsSetResult{@link #IRTCEngineEventHandler#onServerParamsSetResult} to return the corresponding result.
     * @list Messaging
     */
    virtual int setServerParams(const char* signature, const char* url) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 查询对端用户或本端用户的登录状态
     * @param peer_user_id <br>
     *        需要查询的用户 ID
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 必须调用 login{@link #IRTCEngine#login} 登录后，才能调用本接口。
     *       - 调用本接口后，SDK 会使用 onGetPeerOnlineStatus{@link #IRTCEngineEventHandler#onGetPeerOnlineStatus} 回调通知查询结果。
     *       - 在发送房间外消息之前，用户可以通过本接口了解对端用户是否登录，从而决定是否发送消息。也可以通过本接口查询自己查看自己的登录状态。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief Query the login status of the opposite or local user
     * @param peer_user_id <br>
     *        User ID to be queried
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - login{@link #IRTCEngine#login} must be called before this interface can be called.
     *        - After calling this interface, the SDK notifies the query result using the onGetPeerOnlineStatus{@link #IRTCEngineEventHandler#onGetPeerOnlineStatus} callback.
     *        - Before sending an out-of-room message, the user can know whether the peer user is logged in through this interface to decide whether to send a message. You can also check your login status through this interface.
     * @list Messaging
     */
    virtual int getPeerOnlineStatus(const char* peer_user_id) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 给房间外指定的用户发送文本消息（P2P）
     * @param uid <br>
     *        消息接收用户的 ID
     * @param message <br>
     *        发送的文本消息内容 <br>
     *        消息不超过 64 KB。
     * @param config 消息类型，参看 MessageConfig{@link #MessageConfig}。
     * @return
     *        - >0：发送成功，返回这次发送消息的编号，从 1 开始递增
     *        - -1：发送失败，IRTCEngine 实例未创建
     *        - -2：发送失败，uid 为空
     * @note
     *       - 在发送房间外文本消息前，必须先调用 login{@link #IRTCEngine#login} 完成登录。
     *       - 用户调用本接口发送文本信息后，会收到一次 onUserMessageSendResultOutsideRoom{@link #IRTCEngineEventHandler#onUserMessageSendResultOutsideRoom} 回调，得知消息是否成功发送。
     *       - 若文本消息发送成功，则 uid 所指定的用户会通过 onUserMessageReceivedOutsideRoom{@link #IRTCEngineEventHandler#onUserMessageReceivedOutsideRoom} 回调收到该消息。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief Send a text message (P2P) to a specified user outside the room
     * @param uid User ID of the message receiver
     * @param message <br>
     *        Text message content sent <br>
     *        Message does not exceed 64 KB.
     * @param config Message type, see MessageConfig{@link #MessageConfig}.
     * @return
     *         - > 0: Sent successfully, return the number of the sent message, increment from 1
     *         - -1: Sent failed, IRTCEngine instance not created
     *         - -2: Sent failed, uid is empty
     * @note
     *        - You must call login{@link #IRTCEngine#login} to complete the login before you can send a message in a foreign text of the room.
     *        - After the user calls this interface to send a text message, he will receive an onUserMessageSendResultOutsideRoom{@link #IRTCEngineEventHandler#onUserMessageSendResultOutsideRoom} callback to know whether the message was successfully sent.
     *        - If the text message is sent successfully, the user specified by uid receives the message via the onUserMessageReceivedOutsideRoom{@link #IRTCEngineEventHandler#onUserMessageReceivedOutsideRoom} callback.
     * @list Messaging
     */
    virtual int64_t sendUserMessageOutsideRoom(const char* uid, const char* message, MessageConfig config = kMessageConfigReliableOrdered) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 给房间外指定的用户发送二进制消息（P2P）
     * @param uid <br>
     *        消息接收用户的 ID
     * @param length <br>
     *        二进制字符串的长度
     * @param message <br>
     *        发送的二进制消息内容 <br>
     *        消息不超过 46KB。
     * @param config 消息类型，参看 MessageConfig{@link #MessageConfig}。
     * @return
     *        - >0：发送成功，返回这次发送消息的编号，从 1 开始递增
     *        - -1：发送失败，IRTCEngine 实例未创建
     *        - -2：发送失败，uid 为空
     * @note
     *       - 在发送房间外二进制消息前，必须先调用 login{@link #IRTCEngine#login} 完成登录。
     *       - 用户调用本接口发送二进制消息后，会收到一次 onUserMessageSendResultOutsideRoom{@link #IRTCEngineEventHandler#onUserMessageSendResultOutsideRoom} 回调，通知消息是否发送成功。
     *       - 若二进制消息发送成功，则 uid 所指定的用户会通过 onUserBinaryMessageReceivedOutsideRoom{@link #IRTCEngineEventHandler#onUserBinaryMessageReceivedOutsideRoom} 回调收到该条消息。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief Send binary messages (P2P) to a specified user outside the room
     * @param uid <br>
     *        ID of the user receiving the message
     * @param length <br>
     *        Length of the binary string
     * @param message <br>
     *        Content of the binary message sent <br>
     *        Message does not exceed 46KB.
     * @param config Message type, see MessageConfig{@link #MessageConfig}.
     * @return
     *         - > 0: sent successfully, return the number of the sent message, increment from 1
     *         - -1: Sent failed, IRTCEngine instance not created
     *         - -2: Sent failed, uid is empty
     * @note
     *        - login{@link #IRTCEngine#login} must be called before sending out-of-room binary messages.
     *        - After the user calls this interface to send a binary message, he will receive an onUserMessageSendResultOutsideRoom{@link #IRTCEngineEventHandler#onUserMessageSendResultOutsideRoom} callback to notify whether the message was sent successfully.
     *        - If the binary message is sent successfully, the user specified by uid will receive the message through the onUserBinaryMessageReceivedOutsideRoom{@link #IRTCEngineEventHandler#onUserBinaryMessageReceivedOutsideRoom} callback.
     * @list Messaging
     */
    virtual int64_t sendUserBinaryMessageOutsideRoom(const char* uid, int length, const uint8_t* message, MessageConfig config = kMessageConfigReliableOrdered) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 客户端给应用服务器发送文本消息（P2Server）
     * @param message <br>
     *        发送的文本消息内容 <br>
     *        消息不超过 64 KB。
     * @return
     *        - >0：发送成功，返回这次发送消息的编号，从 1 开始递增
     *        - -1：发送失败，IRTCEngine 实例未创建
     * @note
     *       - 在向应用服务器发送文本消息前，必须先调用 login{@link #IRTCEngine#login} 完成登录，随后调用 setServerParams{@link #IRTCEngine#setServerParams} 设置应用服务器。
     *       - 调用本接口后，会收到一次 onServerMessageSendResult{@link #IRTCEngineEventHandler#onServerMessageSendResult} 回调，通知消息发送方是否发送成功。
     *       - 若文本消息发送成功，则之前调用 setServerParams{@link #IRTCEngine#setServerParams} 设置的应用服务器会收到该条消息。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief The client side sends a text message to the application server (P2Server)
     * @param message <br>
     *        The content of the text message sent <br>
     *        The message does not exceed 64 KB.
     * @return
     *         - > 0: Sent successfully, return the number of the sent message, increment from 1
     *         - -1: Sent failed, IRTCEngine instance not created
     * @note
     *        - Before sending a text message to the application server, you must first call login{@link #IRTCEngine#login} to complete the login, and then call setServerParams{@link #IRTCEngine#setServerParams} Set up the application server.
     *        - After calling this interface, you will receive an onServerMessageSendResult{@link #IRTCEngineEventHandler#onServerMessageSendResult} callback to inform the message sender whether the message was sent successfully.
     *        - If the text message is sent successfully, the application server that previously called setServerParams{@link #IRTCEngine#setServerParams} will receive the message.
     * @list Messaging
     */
    virtual int64_t sendServerMessage(const char* message) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 客户端给应用服务器发送二进制消息（P2Server）
     * @param length <br>
     *        二进制字符串的长度
     * @param message <br>
     *        发送的二进制消息内容 <br>
     *        消息不超过 46KB。
     * @return
     *        - >0：发送成功，返回这次发送消息的编号，从 1 开始递增
     *        - -1：发送失败，IRTCEngine 实例未创建
     * @note
     *       - 在向应用服务器发送二进制消息前，必须先调用 login{@link #IRTCEngine#login} 完成登录，随后调用 setServerParams{@link #IRTCEngine#setServerParams} 设置应用服务器。
     *       - 调用本接口后，会收到一次 onServerMessageSendResult{@link #IRTCEngineEventHandler#onServerMessageSendResult} 回调，通知消息发送方发送成功或失败。
     *       - 若二进制消息发送成功，则之前调用 setServerParams{@link #IRTCEngine#setServerParams} 设置的应用服务器会收到该条消息。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief Client side sends binary messages to the application server (P2Server)
     * @param length <br>
     *        Length of binary string
     * @param message <br>
     *        Binary message content sent <br>
     *        Message does not exceed 46KB.
     * @return
     *         - > 0: Sent successfully, return the number of the sent message, increment from 1
     *         - -1: Sent failed, IRTCEngine instance not created
     * @note
     *        - Before sending a binary message to the application server, you must first call login{@link #IRTCEngine#login} to complete the login, and then call setServerParams{@link #IRTCEngine#setServerParams} Set up the application server.
     *        - After calling this interface, you will receive an onServerMessageSendResult{@link #IRTCEngineEventHandler#onServerMessageSendResult} callback to inform the message sender that the sending succeeded or failed.
     *        - If the binary message is sent successfully, the application server that previously called setServerParams{@link #IRTCEngine#setServerParams} will receive the message.
     * @list Messaging
     */
    virtual int64_t sendServerBinaryMessage(int length, const uint8_t* message) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 开启通话前网络探测
     * @param is_test_uplink 是否探测上行带宽
     * @param expected_uplink_bitrate 期望上行带宽，单位：kbps<br>范围为 {0, [100-10000]}，其中， `0` 表示由 SDK 指定最高码率。
     * @param is_test_downlink 是否探测下行带宽
     * @param expected_downlink_biterate 期望下行带宽，单位：kbps<br>范围为 {0, [100-10000]}，其中， `0` 表示由 SDK 指定最高码率。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 成功调用本接口后，会在 3s 内收到一次 onNetworkDetectionResult{@link #IRTCEngineEventHandler#onNetworkDetectionResult} 回调，此后每 2s 会收到一次该回调，通知探测结果；
     *       - 若探测停止，则会收到一次 onNetworkDetectionStopped{@link #IRTCEngineEventHandler#onNetworkDetectionStopped} 通知探测停止。
     * @list 网络管理
     */
    /**
     * @locale en
     * @type api
     * @brief Pre-call network detection
     * @param is_test_uplink Whether to detect uplink bandwidth
     * @param expected_uplink_bitrate Expected uplink bandwidth, unit: kbps<br>Range: {0, [100-10000]}, `0`: Auto, that RTC will set the highest bite rate.
     * @param is_test_downlink Whether to detect downlink bandwidth
     * @param expected_downlink_biterate Expected downlink bandwidth, unit: kbps<br>Range: {0, [100-10000]}, `0`: Auto, that RTC will set the highest bite rate.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - After calling this interface, you will receive onNetworkDetectionResult{@link #IRTCEngineEventHandler#onNetworkDetectionResult} within 3s and every 2s thereafter notifying the probe result;
     *        - If the probe stops, you will receive onNetworkDetectionStopped{@link #IRTCEngineEventHandler#onNetworkDetectionStopped} notify that probing has stopped.
     * @list Network Processing
     */
    virtual int startNetworkDetection(bool is_test_uplink, int expected_uplink_bitrate, bool is_test_downlink, int expected_downlink_biterate) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 停止通话前网络探测
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 调用本接口后，会收到一次 onNetworkDetectionStopped{@link #IRTCEngineEventHandler#onNetworkDetectionStopped} 回调通知探测停止。
     * @list 网络管理
     */
    /**
     * @locale en
     * @type api
     * @brief Stop pre-call network probe
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - After calling this interface, you will receive onNetworkDetectionStopped{@link #IRTCEngineEventHandler#onNetworkDetectionStopped} notifying that the the probing has stopped.
     * @list Network Processing
     */
    virtual int stopNetworkDetection() = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 在屏幕共享时，设置屏幕音频的采集方式（内部采集/自定义采集）
     * @param source_type 屏幕音频输入源类型, 参看 AudioSourceType{@link #AudioSourceType}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *      - 默认采集方式是 RTC SDK 内部采集。
     *      - 你应该在 publishScreenAudio{@link #IRTCRoom#publishScreenAudio} 前，调用此方法。否则，你将收到 onWarning{@link #IRTCEngineEventHandler#onWarning} 的报错：`kWarningCodeSetScreenAudioSourceTypeFailed`
     *      - 如果设定为内部采集，你必须再调用 startScreenAudioCapture{@link #IRTCEngine#startScreenAudioCapture} 开始采集；
     *      - 如果设定为自定义采集，你必须再调用 pushScreenAudioFrame{@link #IRTCEngine#pushScreenAudioFrame} 将自定义采集到的屏幕音频帧推送到 RTC SDK。
     *      - 无论是内部采集还是自定义采集，你都必须调用 publishScreenAudio{@link #IRTCRoom#publishScreenAudio} 将采集到的屏幕音频推送到远端。
     * @list 屏幕分享
     * @order 11
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Set the screen audio acquisition method (internal acquisition/custom acquisition)
     * @param source_type Screen audio input source type. See AudioSourceType{@link #AudioSourceType}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *       - The default acquisition method is internal to the RTC SDK Collect.
     *       - You should call this method before publishScreenAudio{@link #IRTCRoom#publishScreenAudio}. Otherwise, you will receive an error from onWarning{@link #IRTCEngineEventHandler#onWarning}: 'kWarningCodeSetScreenAudioSourceTypeFailed'
     *       - If it is set to internal collection, you must call startScreenAudioCapture{@link #IRTCEngine#startScreenAudioCapture} to start collection;
     *       - If it is set to custom collection, you must call pushScreenAudioFrame{@link #IRTCEngine#pushScreenAudioFrame} to customize the collected screen audio frame Push to the RTC SDK.
     *       - Whether it is an internal capture or a custom capture, you must call publishScreenAudio{@link #IRTCRoom#publishScreenAudio} to push the captured screen audio to the remote end.
     */
    virtual int setScreenAudioSourceType(AudioSourceType source_type) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 在屏幕共享时，设置屏幕音频流和麦克风采集到的音频流的混流方式
     * @param index 混流方式，参看 StreamIndex{@link #StreamIndex} <br>
     *        - `kStreamIndexMain`: 将屏幕音频流和麦克风采集到的音频流混流
     *        - `kStreamIndexScreen`: 默认值， 将屏幕音频流和麦克风采集到的音频流分为两路音频流
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 你应该在 publishScreenAudio{@link #IRTCRoom#publishScreenAudio} 之前，调用此方法。否则，你将收到 onWarning{@link #IRTCEngineEventHandler#onWarning} 的报错：`kWarningCodeSetScreenAudioStreamIndexFailed`
     * @list 屏幕分享
     * @order 12
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Set the mixing mode of the screen audio stream and the audio stream collected by the microphone when the screen is shared
     * @param index The mixing mode. See StreamIndex{@link #StreamIndex} <br>
     *         - `kStreamIndexMain`: Mix the audio stream collected by the screen audio stream and the microphone
     *         - `KStreamIndexScreen`: By default, it divides  the screen audio stream and the audio stream collected by the microphone into two audio streams
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note You should call this method before publishScreenAudio{@link #IRTCRoom#publishScreenAudio}. Otherwise, you will receive an error from onWarning{@link #IRTCEngineEventHandler#onWarning}: `kWarningCodeSetScreenAudioStreamIndexFailed`.
     */
    virtual int setScreenAudioStreamIndex(StreamIndex index) = 0;
    /**
     * @locale zh
     * @hidden(iOS,Android,Linux)
     * @type api
     * @brief 在屏幕共享时，设置屏幕音频流的声道数
     * @param channel 声道数，参看 AudioChannel{@link #AudioChannel}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 当你调用 setScreenAudioStreamIndex{@link #IRTCEngine#setScreenAudioStreamIndex} 并设置屏幕音频流和麦克风音频流混流时，此接口不生效，音频通道数由 setAudioProfile{@link #IRTCEngine#setAudioProfile} 控制。
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @hidden(iOS,Android,Linux)
     * @type api
     * @brief Set the audio channel of the screen-sharing audio stream
     * @param channel The number of Audio channels. See AudioChannel{@link #AudioChannel}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note When you call setScreenAudioStreamIndex{@link #IRTCEngine#setScreenAudioStreamIndex} to mix the microphone audio stream and the screen-sharing audio stream, the audio channel is set by setAudioProfile{@link #IRTCEngine#setAudioProfile} rather than this API.
     * @list Screen Sharing
     */
    virtual int setScreenAudioChannel(AudioChannel channel) = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS,macOS,Windows)
     * @deprecated on Windows since 3.59, use `startScreenAudioCapture` with the "device_id" parameter as a replacement.
     * @type api
     * @brief 在屏幕共享时，开始使用 RTC SDK 内部采集方式，采集屏幕音频
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 本接口仅对内部采集生效，RTC SDK 默认使用内部采集模块采集屏幕音频。对于 Windows SDK，若已调用 setScreenAudioSourceType{@link #IRTCEngine#setScreenAudioSourceType} 将音频输入源设置为 `kAudioSourceTypeExternal` 自定义采集，需先切换为 `kAudioSourceTypeInternal` 内部采集，否则该接口调用无效，并将触发 onAudioDeviceWarning{@link #IRTCEngineEventHandler#onAudioDeviceWarning} 回调。
     *        - 采集后，你还需要调用 publishScreenAudio{@link #IRTCRoom#publishScreenAudio} 将采集到的屏幕音频推送到远端。
     *        - 要关闭屏幕音频内部采集，调用 stopScreenAudioCapture{@link #IRTCEngine#stopScreenAudioCapture}。
     * @list 音频管理
     */
    /**
     * @locale en
     * @hidden(Android,iOS,macOS,Windows)
     * @deprecated on Windows since 3.59, use `startScreenAudioCapture` with the "device_id" parameter as a replacement.
     * @type api
     * @brief When sharing the screen, start using RTC SDK internal collection method to collect screen audio
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - The call of this API takes effects only when you are using RTC SDK to record screen. You will get a warning by onAudioDeviceWarning{@link #IRTCEngineEventHandler#onAudioDeviceWarning} after calling this API when the video source is set to an external recorder.
     *         - After collection, you also need to call publishScreenAudio{@link #IRTCRoom#publishScreenAudio} to collect the screen audio Push to the far end.
     *         - To turn off screen audio internal capture, call stopScreenAudioCapture{@link #IRTCEngine#stopScreenAudioCapture}.
     * @list Audio Management
     */
    virtual int startScreenAudioCapture() = 0;
    /**
     * @locale zh
     * @hidden(Linux,Android,iOS)
     * @valid since 3.59
     * @type api
     * @brief 使用 RTC SDK 内部采集方式，采集桌面端系统声音，用于屏幕共享。 <br>
     *        该接口会采集桌面端系统声卡的音频数据，采集到的音频会混入本地音频流中。
     * @param device_id <br>
     *        - 对于 Windows 平台，该参数用于设置采集本地指定音频进程的音频。例如可将该参数设为某应用程序可执行文件的文件名（如 QQMusic.exe），此时 SDK 只会采集该应用程序的音频。传入 nullptr 或置空时即代表采集整个系统所有应用程序的音频。当前，应用程序仅支持 QQ 音乐，网易云音乐，和酷狗音乐，采集其他进程可能会导致未知行为。
     *        - 对于 macOS 平台，该参数表示虚拟设备 ID。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 本接口仅对内部采集生效，RTC SDK 默认使用内部采集模块采集屏幕音频。若已调用 setScreenAudioSourceType{@link #IRTCEngine#setScreenAudioSourceType} 将音频输入源设置为 `kAudioSourceTypeExternal` 自定义采集，需先切换为 `kAudioSourceTypeInternal` 内部采集，否则该接口调用无效，并将触发 onAudioDeviceWarning{@link #IRTCEngineEventHandler#onAudioDeviceWarning} 回调。
     *        - 采集后，你还需要调用 publishScreenAudio{@link #IRTCRoom#publishScreenAudio} 将采集到的屏幕音频推送到远端。
     *        - 要关闭屏幕音频内部采集，调用 stopScreenAudioCapture{@link #IRTCEngine#stopScreenAudioCapture}。
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @hidden(Linux,Android,iOS)
     * @valid since 3.59
     * @type api
     * @brief When sharing the screen, start using RTC SDK internal collection method to collect screen audio
     * @param device_id For macOS, device_id means ID of the virtual device; For Windows, device_id means process name that share(For Example: QQMusic.exe)
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - The call of this API takes effects only when you are using RTC SDK to record screen. You will get a warning by onAudioDeviceWarning{@link #IRTCEngineEventHandler#onAudioDeviceWarning} after calling this API when the video source is set to an external recorder. 
     *        - After collection, you also need to call publishScreen{@link #IRTCRoom#publishScreen} to collect the screen audio Push to the far end.
     *        - To turn off screen audio internal capture, call stopScreenAudioCapture{@link #IRTCEngine#stopScreenAudioCapture}.
     * @list Screen Sharing
     */
    virtual int startScreenAudioCapture(const char device_id[MAX_DEVICE_ID_LENGTH]) = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @type api
     * @brief 在屏幕共享时，停止使用 RTC SDK 内部采集方式，采集屏幕音频。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 调用本接口时，采集模式应为内部模式。在外部采集模式下调用无效，并将触发 onAudioDeviceWarning{@link #IRTCEngineEventHandler#onAudioDeviceWarning} 回调。
     *        - 本接口仅对内部采集生效。如果为外部采集模式，调用本接口失败。你需要在外部采集器中实现停止采集的逻辑。
     *        - 要开始屏幕音频内部采集，调用 startScreenAudioCapture{@link #IRTCEngine#startScreenAudioCapture}。
     * @list 音频管理
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @type api
     * @brief Stop RTC SDK's device audio recorder.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - The call of this API takes effects only when you are using RTC SDK to record screen. You will get a warning by onAudioDeviceWarning{@link #IRTCEngineEventHandler#onAudioDeviceWarning} after calling this API when the video source is set to an external recorder.
     *        - This API can only stop the screen capture by the RTC SDK. If the video source has been set to external recorder, the call of this API will fail with a warning message. You need to stop it in the external recorder.
     *        - To start the device audio recording, call startScreenAudioCapture{@link #IRTCEngine#startScreenAudioCapture}.
     * @list Audio Management
     */
    virtual int stopScreenAudioCapture() = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 使用自定义采集方式，采集屏幕共享时的屏幕音频时，将音频帧推送至 RTC SDK 处进行编码等处理。
     * @param frame 音频数据帧，参见 IAudioFrame{@link #IAudioFrame} <br>
     *                   - 音频采样格式为 S16。音频缓冲区内的数据格式必须为 PCM 数据，其容量大小应该为 samples × frame.channel × 2。
     *                   - 必须指定具体的采样率和声道数，不支持设置为自动。
     * @return 方法调用结果 <br>
     *        - 0: 设置成功
     *        - < 0: 设置失败
     * @note
     *        - 调用此接口推送屏幕共享时的自定义采集的音频数据前，必须调用 setScreenAudioSourceType{@link #IRTCEngine#setScreenAudioSourceType} 开启屏幕音频自定义采集。
     *        - 你应每隔 10 毫秒，调用一次此方法推送一次自定义采集的音频帧。一次推送的音频帧中应包含 frame.sample_rate / 100 个音频采样点。比如，假如采样率为 48000Hz，则每次应该推送 480 个采样点。
     *        - 调用此接口将自定义采集的音频帧推送到 RTC SDK 后，你必须调用 publishScreenAudio{@link #IRTCRoom#publishScreenAudio} 将采集到的屏幕音频推送到远端。在调用 publishScreenAudio{@link #IRTCRoom#publishScreenAudio} 前，推送到 RTC SDK 的音频帧信息会丢失。
     * @list 屏幕分享
     * @order 14
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Using a custom capture method, when capturing screen audio during screen sharing, push the audio frame to the RTC SDK for encoding and other processing.
     * @param frame Audio data frame. See IAudioFrame{@link #IAudioFrame}
     *              - The audio sampling format is S16. The data format within the audio buffer must be PCM data, and its capacity should be samples × frame.channel × 2.
     *              - A specific sample rate and the number of channels must be specified; it is not supported to set them to automatic.
     * @return  Method call result <br>
     *         - 0: Setup succeeded
     *         - < 0: Setup failed
     * @note
     *         - Before calling this interface to push custom collected audio data during screen sharing, you must call setScreenAudioSourceType{@link #IRTCEngine#setScreenAudioSourceType} Enable custom capture of screen audio.
     *         - You should call this method every 10 milliseconds to push a custom captured audio frame. A push audio frame should contain frame.sample _rate/100 audio sample points. For example, if the sampling rate is 48000Hz, 480 sampling points should be pushed each time.
     *         - After calling this interface to push the custom captured audio frame to the RTC SDK, you must call publishScreenAudio{@link #IRTCRoom#publishScreenAudio} to push the captured screen audio to the remote end. Audio frame information pushed to the RTC SDK is lost before calling publishScreenAudio{@link #IRTCRoom#publishScreenAudio}.
     */
    virtual int pushScreenAudioFrame(IAudioFrame* frame) = 0;
    /**
     * @locale zh
     * @type api
     * @hidden internal use only
     * @brief 在听众端，设置订阅的所有远端音频流精准对齐后播放。
     * @param stream_key 作为对齐基准的远端音频流。参看 RemoteStreamKey{@link #RemoteStreamKey}。 <br>
     *                  一般选择主唱的音频流。 <br>
     *                  你必须在收到 onUserPublishStreamAudio{@link #IRTCRoomEventHandler#onUserPublishStreamAudio}，确认此音频流已发布后，调用此 API。
     * @param mode 是否对齐，默认不对齐。参看 AudioAlignmentMode{@link #AudioAlignmentMode}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 你必须在实时合唱场景下使用此功能。在加入房间时，所有人应设置 RoomProfileType{@link #RoomProfileType} 为 `kRoomProfileTypeChorus`。
     *        - 订阅的所有远端流必须通过 startAudioMixing{@link #IAudioMixingManager#startAudioMixing} 开启了背景音乐混音，并将 AudioMixingConfig{@link #AudioMixingConfig} 中的 `sync_progress_to_record_frame` 设置为 `true`。
     *        - 如果订阅的某个音频流延迟过大，可能无法实现精准对齐。
     *        - 合唱的参与者不应调用此 API，因为调用此 API 会增加延迟。如果希望从听众变为合唱参与者，应关闭对齐功能。
     * @list 在线 KTV
     */
    /**
     * @locale en
     * @type api
     * @hidden internal use only
     * @brief On the listener side, set all subscribed audio streams precisely timely aligned.
     * @param stream_key The remote audio stream used as the benchmark during time alignment. See RemoteStreamKey{@link #RemoteStreamKey}. <br>
     *                  You are recommended to use the audio stream from the lead singer. <br>
     *                  You must call this API after receiving onUserPublishStreamAudio{@link #IRTCRoomEventHandler#onUserPublishStreamAudio}.
     * @param mode Whether to enable the alignment. Disabled by default. See AudioAlignmentMode{@link #AudioAlignmentMode}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - You must use the function when all participants set RoomProfileType{@link #RoomProfileType} to `kRoomProfileTypeChorus` when joining the room.
     *        - All remote participants must call startAudioMixing{@link #IAudioMixingManager#startAudioMixing} to play background music and set `sync_progress_to_record_frame` of AudioMixingConfig{@link #AudioMixingConfig} to `true`.
     *        - If the subscribed audio stream is delayed too much, it may not be precisely aligned.
     *        - The chorus participants must not enable the alignment. If you wish to change the role from listener to participant, you should disable the alignment.
     * @list Online Karaoke
     */
    virtual int setAudioAlignmentProperty(const RemoteStreamKey& stream_key, AudioAlignmentMode mode) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Android,Linux)
     * @type api
     * @brief 设置 Extension 配置项
     * @param group_id App 和 Extension 应该归属于同一个 App Group，此处需要传入 Group Id
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 该函数必须在 sharedEngineWithAppId 函数之后立即调用。如果不调用或者调用时机比较晚，本地用户会收到 onMediaDeviceStateChanged{@link #IRTCEngineEventHandler#onMediaDeviceStateChanged} 的回调。 <br>
     *        参数 device_state 值为 ByteRTCMediaDeviceStateStopped，device_error 值为 `ByteRTCMediaDeviceErrorNotFindGroupId`
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Android,Linux)
     * @type api
     * @brief set extension configuration
     * @param group_id App and extension should belong to the same app group. Group ID needs to be passed in here
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note This function must be called immediately after the sharedenginewithappid function. If it is not called or the call time is late, local users will receive a callback to onMediaDeviceStateChanged{@link #IRTCEngineEventHandler#onMediaDeviceStateChanged}. <br>
     *        Parameter device_state value is ByteRTCMediaDeviceStateStopped, device_error value is `ByteRTCMediaDeviceErrorNotFindGroupId`.
     * @list Screen Sharing
     */
    virtual int setExtensionConfig(const char* group_id) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Android,Linux)
     * @type api
     * @brief 发送屏幕共享 Extension 程序消息
     * @param message :发送给 Extension 程序的消息内容
     * @param size :message 大小
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 该函数必须在 startScreenCapture 函数之后调用
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Android,Linux)
     * @type api
     * @brief send screen sharing extension program message
     * @param message:  Message content sent to extension program
     * @param size : size of message
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note This function must be called after the startScreenCapture function
     * @list Screen Sharing
     */
    virtual int sendScreenCaptureExtensionMessage(const char* message, size_t size) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Android,Linux)
     * @type api
     * @brief 开启本地屏幕共享数据采集
     * @param type 屏幕共享数据采集类型
     * @param bundle_id 传入 Broadcast Upload Extension 的 Bundle Id，用于在设置中优先展示 Extension
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 当从 ios 控制中心异常启动 Extension 时可以不用调用本函数 <br>
     *       本地用户会收到 onMediaDeviceStateChanged{@link #IRTCEngineEventHandler#onMediaDeviceStateChanged} 的回调。 <br>
     *       参数 device_state 值为 ByteRTCMediaDeviceStateStarted，device_error 值为 ByteRTCMediaDeviceErrorOK
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Android,Linux)
     * @type api
     * @brief  start local screen shared data collection
     * @param type screen shared data collection type
     * @param bundleId the bundle ID of the broadcast upload extension passed in, which is used to display the extension preferentially in settings
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note when the extension is started abnormally from the IOS control center, you don't need to call this function <br>
     *       local users will receive a callback to onMediaDeviceStateChanged{@link #IRTCEngineEventHandler#onMediaDeviceStateChanged}. <br>
     *       Parameter device_state value is kMediaDeviceStateStarted, device_error value is kMediaDeviceErrorOK
     */
    virtual int startScreenCapture(ScreenMediaType type, const char* bundle_id) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,iOS,Linux)
     * @type api
     * @brief 通过传入的 Type 和 Context 开启屏幕音视频内部采集。
     * @param type <br>
     *        指定的屏幕媒体采集类型，参看 ScreenMediaType{@link #ScreenMediaType}
     * @param context <br>
     *        Android 平台传入 Intent 对象，由业务方申请系统录屏权限后获得。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 采集后，你还需要调用 publishScreen {@link #IRtcRoom#publishScreen} 将采集到的屏幕音视频推送到远端。
     *        - 开启屏幕音视频内部采集，Android 专用接口。
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,iOS,Linux)
     * @type api
     * @brief  Start to capture screen audio/video through the incoming Type and Context.
     * @param type <br>
     *        specifying the media type of screen capture，see ScreenMediaType{@link #ScreenMediaType}
     * @param context <br>
     *        Android platform incoming Intent object, obtained by the business party after applying for system recording permission.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - After collection, you also need to call publishScreen {@link #IRtcRoom#publishScreen} to collect the screen audio/vieo Push to the far end.
     *        - Enable internal screen audio/video capture, Android-specific interface.
     * @list Screen Sharing
     */
    virtual int startScreenCapture(ScreenMediaType type, void* context) = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief 停止本地屏幕共享数据采集
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief  stop local screen shared data collection
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Screen Sharing
     */
    virtual int stopScreenCapture() = 0;
    /**
     * @locale zh
     * @hidden for internal use only on Windows
     * @type api
     * @region 转推直播
     * @brief 推送客户端合流外部视频帧。 <br>
     * @param uid <br>
     *        外部视频帧对应的合流布局中的uid
     * @param frame <br>
     *        用于合流的外部视频帧
     * @return
     *        + 0: 调用成功。
     *        + < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     */
    /**
     * @locale en
     * @hidden for internal use only on Windows
     * @type api
     * @brief  push client mixed stream external video frame<br>
     * @param uid <br>
     *        external Video Frame uid
     * @param frame The video frames to be included.
     * @return
     *        + 0: Success.
     *        + < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     */
    virtual int pushClientMixedStreamExternalVideoFrame(const char* uid, const VideoFrameData& frame) = 0;
    /**
     * @locale zh
     * @hidden for internal use only on Windows
     * @type api
     * @brief 设置转推直播观察者。
     * @param observer
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list CDN 推流
     */
    /**
     * @locale en
     * @hidden for internal use only on Windows
     * @type api
     * @brief  set observer for mixed stream
     * @param observer
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Pushing Streams to CDN
     */
    virtual int setClientMixedStreamObserver(IClientMixedStreamObserver* observer) = 0;
    /**
     * @locale zh
     * @hidden for internal use only on Windows
     * @type api
     * @brief 客户端合流转推 CDN。
     * @param task_id 任务 ID。
     * @param config 合流转推 CDN 任务配置。
     * @param extra_config
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list Pushing Streams to CDN
     */
    /**
     * @locale en
     * @hidden for internal use only on Windows
     * @type api
     * @brief
     * @param task_id
     * @param config
     * @param extra_config
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Pushing Streams to CDN
     */
    virtual int startClientMixedStream(const char* task_id, IMixedStreamConfig* config, ClientMixedStreamConfig extra_config) = 0;
    /**
     * @locale zh
     * @hidden for internal use only on Windows
     * @type api
     * @brief
     * @param task_id
     * @param config
     * @param extra_config
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list CDN 推流
     */
    /**
     * @locale en
     * @hidden for internal use only on Windows
     * @type api
     * @brief
     * @param task_id
     * @param config
     * @param extra_config
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Pushing Streams to CDN
     */
    virtual int updateClientMixedStream(const char* task_id, IMixedStreamConfig* config, ClientMixedStreamConfig extra_config) = 0;
    /**
     * @locale zh
     * @hidden for internal use only on Windows
     * @type api
     * @brief
     * @param task_id
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list CDN 推流
     */
    /**
     * @locale en
     * @hidden for internal use only on Windows
     * @type api
     * @brief
     * @param task_id
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details.
     * @list Pushing Streams to CDN
     */
    virtual int stopClientMixedStream(const char* task_id) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @valid since 3.60
     * @type api
     * @brief 指定房间中的媒体流，发布到 CDN 或发布一路 WTN 流。同时指定多路流时，SDK 会先将多路流合成一路流，然后再进行转推。
     * @param task_id 转推直播任务 ID，长度不超过 126 字节。 当 IMixedStreamConfig{@link #IMixedStreamConfig} 中的 `PushTargetType = 0` 时， 用于标识转推直播任务。你可以在同一房间内发起多个转推直播任务，并用不同的 ID 加以区分。当你需要发起多个转推直播任务时，应使用多个 ID；当你仅需发起一个转推直播任务时，建议使用空字符串。
     * 当 `PushTargetType = 1` 时，设置无效，传空即可。
     * @param push_target_config 推流目标配置参数，比如设置推流地址、WTN 流 ID。参看 MixedStreamPushTargetConfig{@link #MixedStreamPushTargetConfig}。
     * @param config 转推直播配置参数，比如设置合流的图片、视频视图布局和音频属性。参看 IMixedStreamConfig{@link #IMixedStreamConfig}。
     * @return
     *        - 0: 成功
     *        - !0: 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 调用该方法后，关于启动结果和推流过程中的错误，会收到 onMixedStreamEvent{@link #IRTCEngineEventHandler#onMixedStreamEvent} 回调。
     *       - 在[控制台](https://console.volcengine.com/rtc/cloudRTC?tab=callback)配置了转推直播和WTN 流的服务端回调后，调用本接口会收到相应回调。重复调用该接口时，第二次调用会同时触发 [TranscodeStarted](https://www.volcengine.com/docs/6348/75125#transcodestarted) 和 [TranscodeUpdated](https://www.volcengine.com/docs/6348/75125#transcodeupdated)。
     *       - 调用 stopPushMixedStream{@link #IRTCEngine#stopPushMixedStream} 停止转推直播。
     *       - 调用 updatePushMixedStream{@link #IRTCEngine#updatePushMixedStream} 可以更新部分任务参数。
     *       - 调用 startPushSingleStream{@link #IRTCEngine#startPushSingleStream} 可以转推单路流到 CDN。
     * @list CDN 推流
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @valid since 3.60
     * @type api
     * @brief Create a new task of pushing mixed media streams to CDN and sets the relevant configurations. <br>
     *        When pushing more than one live streams in the same task, SDK will first mix those streams into one single stream and then push it to CDN.
     * @param task_id Task ID. The length should not exceed 126 bytes. <br>
     *        When IMixedStreamConfig{@link #IMixedStreamConfig} is set to `PushTargetType = 0`, this ID is used to identify the task. <br>
     *        You may want to push more than one mixed stream to CDN from the same room. When you do that, use different ID for corresponding tasks; if you will start only one task, use an empty string.
     * @param push_target_config Push target config, such as the push url and WTN stream ID. See MixedStreamPushTargetConfig{@link #MixedStreamPushTargetConfig}.
     * @param config Configurations to be set when pushing streams to CDN. See IMixedStreamConfig{@link #IMixedStreamConfig}.
     * @return
     *        - 0: Success
     *        - !0: Fail. See ReturnStatus{@link #ReturnStatus} for more details.
     * @note
     *       - After calling this API, you will be informed of the result and errors during the pushing process via the onMixingEvent{@link #IRTCEngineEventHandler#onMixingEvent} callback.
     *       - Subscribe to the Push-to-CDN and the WTN stream notifications in the [console](https://console.byteplus.com/rtc/cloudRTC?tab=callback) to receive notifications about task status changes. When calling this API repeatedly, subsequent calls to this API will trigger both [TranscodeStarted](https://docs.byteplus.com/en/docs/byteplus-rtc/docs-75125#transcodestarted) and [TranscodeUpdated](https://docs.byteplus.com/en/docs/byteplus-rtc/docs-75125#transcodeupdated) callbacks.
     *       - Call stopPushMixedStream{@link #IRTCEngine#stopPushMixedStream} to stop pushing streams to CDN.
     *       - Call updatePushMixedStream{@link #IRTCEngine#updatePushMixedStream} to update part of the configurations of the task.
     *       - Call startPushSingleStream{@link #IRTCEngine#startPushSingleStream} to push a single stream to CDN.
     * @list Pushing Streams to CDN
     */
    virtual int startPushMixedStream(const char* task_id, MixedStreamPushTargetConfig push_target_config, IMixedStreamConfig* config) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @valid since 3.60
     * @type api
     * @brief 更新由 startPushMixedStream{@link #IRTCEngine#startPushMixedStream} 启动的任务参数，会收到 onMixedStreamEvent{@link #IRTCEngineEventHandler#onMixedStreamEvent} 回调。
     * @param task_id 转推直播任务 ID。指定想要更新参数设置的转推直播任务。仅当 IMixedStreamConfig{@link #IMixedStreamConfig} 中的 `PushTargetType = 0` 时有效。
     * @param push_target_config 推流目标配置参数，比如设置推流地址、WTN 流 ID。参看 MixedStreamPushTargetConfig{@link #MixedStreamPushTargetConfig}。
     * @param config 转推直播配置参数，参看 IMixedStreamConfig{@link #IMixedStreamConfig}。除特殊说明外，均支持过程中更新。 <br>
     *        调用时，结构体中没有传入值的属性，会被更新为默认值。
     * @return
     *        - 0: 成功
     *        - !0: 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明。
     * @list CDN 推流
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @valid since 3.60
     * @type api
     * @brief Update the task configuration during the task started via startPushMixedStream{@link #IRTCEngine#startPushMixedStream}.  You will be informed of the change via the onMixedStreamEvent{@link #IRTCEngineEventHandler#onMixedStreamEvent} callback.
     * @param task_id Task ID. Specifys of the task to be updated. <br>
     *                When IMixedStreamConfig{@link #IMixedStreamConfig} is set to `PushTargetType = 0`, this ID is used to identify the task.
     * @param push_target_config Push target config, such as the push url and WTN stream ID. See MixedStreamPushTargetConfig{@link #MixedStreamPushTargetConfig}.
     * @param config Configurations that you want to update. See IMixedStreamConfig{@link #IMixedStreamConfig} for specific indications. You can update any property for the task unless it is specified as unavailable for updates. <br>
     *        If you left some properties blank, you can expect these properties to be set to their default values.
     * @return
     *        - 0: Success
     *        - !0: Fail. See ReturnStatus{@link #ReturnStatus} for more details.
     * @list Pushing Streams to CDN
     */
    virtual int updatePushMixedStream(const char* task_id, MixedStreamPushTargetConfig push_target_config, IMixedStreamConfig* config) = 0;
    /**
     * @locale zh
     * @valid since 3.60
     * @type api
     * @brief 停止由 startPushMixedStream{@link #IRTCEngine#startPushMixedStream} 启动的任务。
     * @param task_id 转推直播任务 ID。指定想要更新参数设置的转推直播任务。
     * @param target_type 参看 MixedStreamPushTargetType{@link #MixedStreamPushTargetType}。
     * @return
     *        - 0: 成功
     *        - !0: 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明。
     * @list CDN 推流
     */
    /**
     * @locale en
     * @valid since 3.60
     * @type api
     * @brief Stops the task started via startPushMixedStream{@link #IRTCEngine#startPushMixedStream}.
     * @param task_id Task ID. Specifys of which pushing task you want to update the parameters.
     * @param target_type See MixedStreamPushTargetType{@link #MixedStreamPushTargetType}.
     * @return
     *        - 0: Success
     *        - !0: Fail. See ReturnStatus{@link #ReturnStatus} for more details.
     * @list Pushing Streams to CDN
     */
    virtual int stopPushMixedStream(const char* task_id, MixedStreamPushTargetType target_type) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @valid since 3.60
     * @type api
     * @brief 新增单流转推直播任务。
     * @param task_id 任务 ID。 <br>
     *               你可以发起多个转推直播任务，并用不同的任务 ID 加以区分。当你需要发起多个转推直播任务时，应使用多个 ID；当你仅需发起一个转推直播任务时，建议使用空字符串。
     * @param param 转推直播配置参数。详见 PushSingleStreamParam{@link #PushSingleStreamParam}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明。
     * @note
     *       - 调用该方法后，关于启动结果和推流过程中的错误，会收到 onSingleStreamEvent{@link #IRTCEngineEventHandler#onSingleStreamEvent} 回调。
     *       - 在[控制台](https://console.volcengine.com/rtc/cloudRTC?tab=callback)配置了转推直播服务端回调后，调用本接口会收到相应回调。重复调用该接口时，第二次调用会同时触发 [TranscodeStarted](https://www.volcengine.com/docs/6348/75125#transcodestarted) 和 [TranscodeUpdated](https://www.volcengine.com/docs/6348/75125#transcodeupdated)。
     *       - 调用 stopPushSingleStream{@link #IRTCEngine#stopPushSingleStream} 停止任务。
     *       - 由于本功能不进行编解码，所以推到 RTMP 的视频流会根据推流端的分辨率、编码方式、关闭摄像头等变化而变化。
     * @list CDN 推流
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @valid since 3.60
     * @type api
     * @brief Create a new task of pushing a single media stream to CDN.
     * @param task_id Task ID. <br>
     *        You may want to start more than one task to push streams to CDN. When you do that, use different IDs for corresponding tasks; if you will start only one task, use an empty string.
     * @param param Configurations for pushing a single stream to CDN. See PushSingleStreamParam{@link #PushSingleStreamParam}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details.
     * @note
     *       - After calling this API, you will be informed of the result and errors during the pushing process with onSingleStreamEvent{@link #IRTCEngineEventHandler#onSingleStreamEvent}.
     *       - Subscribe to the Push-to-CDN and the WTN stream notifications in the [console](https://console.byteplus.com/rtc/cloudRTC?tab=callback) to receive notifications about task status changes. When calling this API repeatedly, subsequent calls to this API will trigger both [TranscodeStarted](https://docs.byteplus.com/en/docs/byteplus-rtc/docs-75125#transcodestarted) and [TranscodeUpdated](https://docs.byteplus.com/en/docs/byteplus-rtc/docs-75125#transcodeupdated) callbacks.
     *       - Call stopPushSingleStream{@link #IRTCEngine#stopPushSingleStream} to stop the task.
     *       - Since this API does not perform encoding and decoding, the video stream pushed to RTMP will change according to the resolution, encoding method, and turning off the camera of the end of pushing streams.
     * @list Pushing Streams to CDN
     */
    virtual int startPushSingleStream(const char* task_id, PushSingleStreamParam& param) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @valid since 3.60
     * @type api
     * @brief 停止由 startPushSingleStream{@link #IRTCEngine#startPushSingleStream} 启动的单流转推直播任务。
     * @param task_id 任务 ID。可以指定想要停止的单流转推任务。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明。
     * @list CDN 推流
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @valid since 3.60
     * @type api
     * @brief Stops the task started via startPushSingleStream{@link #IRTCEngine#startPushSingleStream}. <br>
     *        This method stops pushing a single stream to CDN.
     * @param task_id Task ID. Specifys the task you want to stop.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details.
     * @list Pushing Streams to CDN
     */
    virtual int stopPushSingleStream(const char* task_id) = 0;
    /**
     * @locale zh
     * @hidden internal use only
     * @type api
     * @brief 开启缓存同步功能。开启后，会缓存收到的实时音视频数据，并对齐不同数据中的时间戳完成同步。此功能会影响音视频数据消费的实时性。
     * @param config 参看 ChorusCacheSyncConfig{@link #ChorusCacheSyncConfig}。
     * @param observer 事件和数据观察者，参看 IChorusCacheSyncObserver{@link #IChorusCacheSyncObserver}。
     * @return 查看 ReturnStatus{@link #ReturnStatus}。
     * @note 要关闭缓存同步功能，调用 stopChorusCacheSync{@link #IRTCEngine#stopChorusCacheSync}。
     * @list Pushing Streams to CDN
     */
     /**
      * @locale en
      * @hidden internal use only
      * @type api
      * @brief Start aligning RTC data by cache. Received RTC data from different sources will be cached, and aligned based on the included timestamps. This feature compromizes the real-time nature of RTC data consumption.
      * @param config See ChorusCacheSyncConfig{@link #ChorusCacheSyncConfig}.
      * @param observer Event and data observer. See IChorusCacheSyncObserver{@link #IChorusCacheSyncObserver}.
      * @return See ReturnStatus{@link #ReturnStatus}.
      * @note To disable the feature, call stopChorusCacheSync{@link #IRTCEngine#stopChorusCacheSync}.
      * @list Pushing Streams to CDN
      */
    virtual int startChorusCacheSync(ChorusCacheSyncConfig * config, IChorusCacheSyncObserver* observer) = 0;
    /**
     * @locale zh
     * @hidden internal use only
     * @type api
     * @brief 关闭缓存同步功能。
     * @return 查看 ReturnStatus{@link #ReturnStatus}。
     * @list Pushing Streams to CDN
     */
     /**
      * @locale en
      * @hidden internal use only
      * @type api
      * @brief Stop aligning RTC data by cache.
      * @return See ReturnStatus{@link #ReturnStatus}.
      * @list Pushing Streams to CDN
      */
    virtual int stopChorusCacheSync() = 0;
    /**
     * @locale zh
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief 更新屏幕采集数据类型
     * @param type 屏幕采集数据类型
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 该函数必须在 startScreenCapture 函数之后调用。本地用户会收到 onMediaDeviceStateChanged{@link #IRTCEngineEventHandler#onMediaDeviceStateChanged} 的回调。 <br>
     *        参数 device_state 值为 ByteRTCMediaDeviceStateStarted 或 ByteRTCMediaDeviceStateStopped，device_error 值为 ByteRTCMediaDeviceErrorOK。
     * @list 屏幕共享
     */
    /**
     * @locale en
     * @hidden(macOS,Windows,Linux)
     * @type api
     * @brief  update screen acquisition data type
     * @param type screen acquisition data type
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note This API must be called after the startScreenCapture function. Local users will receive a callback to onMediaDeviceStateChanged{@link #IRTCEngineEventHandler#onMediaDeviceStateChanged}. <br>
     *        Parameter device_state value is ByteRTCMediaDeviceStateStarted or ByteRTCMediaDeviceStateStopped, device_error value is ByteRTCMediaDeviceErrorOK.
     * @list Screen Sharing
     */
    virtual int updateScreenCapture(ScreenMediaType type) = 0;
     /**
      * @locale zh
      * @type api
      * @brief 启用音频信息提示。开启提示后，你会收到 onLocalAudioPropertiesReport{@link #IRTCEngineEventHandler#onLocalAudioPropertiesReport}，onRemoteAudioPropertiesReport{@link #IRTCEngineEventHandler#onRemoteAudioPropertiesReport} 和 onActiveSpeaker{@link #IRTCEngineEventHandler#onActiveSpeaker}。
      * @param config 详见 AudioPropertiesConfig{@link #AudioPropertiesConfig}
      * @return
      *        - 0: 调用成功。
      *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
      * @list 音频管理
      */
     /**
      * @locale en
      * @type api
      * @brief Enable audio information prompts. After that, you will receive onLocalAudioPropertiesReport{@link #IRTCEngineEventHandler#onLocalAudioPropertiesReport}, onRemoteAudioPropertiesReport{@link #IRTCEngineEventHandler#onRemoteAudioPropertiesReport}, and onActiveSpeaker{@link #IRTCEngineEventHandler#onActiveSpeaker}.
      * @param config See AudioPropertiesConfig{@link #AudioPropertiesConfig}
      * @return
      *        - 0: Success.
      *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
      * @list Audio Management
      */
    virtual int enableAudioPropertiesReport(const AudioPropertiesConfig& config) = 0;
    
    /**
      * @locale zh
      * @hidden for internal use only
      * @type api
      * @region 音频管理
      * @brief 启用音频人声识别能力。开启提示后，你会收到 onAudioVADStateUpdate{@link #IRTCVideoEventHandler#onAudioVADStateUpdate}。
      * @param interval 回调间隔，单位毫秒
      *       + `<= 0`: 关闭人声识别能力回调
      *       + `(0,100]`: 开启人声识别能力回调，不合法的 interval 值，SDK 自动设置为 100ms
      *       + `> 100`: 开启人声识别能力回调，并将信息提示间隔设置为此值
     * @return
     *        + 0: 调用成功。
     *        + < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
      * @list 音频管理
      */
    virtual int enableAudioVADReport(int interval) = 0;
    /**
      * @locale zh
      * @hidden for internal use only
      * @type api
      * @region 音频管理
      * @brief 启用音频音乐识别能力。开启提示后，你会收到 onAudioAEDStateUpdate{@link #IRTCVideoEventHandler#onAudioAEDStateUpdate}。
      * @param interval 回调间隔，单位毫秒。推荐设置为2000，可支持范围为[300,1000]，小于300设置为300，超出1000设置为1000. <= 0 则关闭回调.
     * @return
     *        + 0: 调用成功。
     *        + < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
      * @list 音频管理
      */
    virtual int enableAudioAEDReport(int interval) = 0;

    /**
     * @locale zh
     * @type api
     * @brief 调节本端播放收到的远端流时的音量。你必须在进房后进行设置。流的发布状态改变不影响设置生效。
     * @param stream_key 参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param volume 音量值和原始音量的比值，范围是 [0, 400]，单位为 %，自带溢出保护。 <br>
     *               为保证更好的通话质量，建议将 volume 值设为 [0,100]。
     * @return 方法调用结果： <br>
     *        - 0：成功；
     *        - <0：失败。具体失败原因参看 ReturnStatus{@link #ReturnStatus}。
     * @note 假设某远端用户 A 始终在被调节的目标用户范围内： <br>
     *        - 当该方法与 setRemoteRoomAudioPlaybackVolume{@link #IRTCRoom#setRemoteRoomAudioPlaybackVolume} 共同使用时，本地收听用户 A 的音量为后调用的方法设置的音量；
     *        - 当该方法与 setPlaybackVolume{@link #IRTCEngine#setPlaybackVolume} 方法共同使用时，本地收听用户 A 的音量将为两次设置的音量效果的叠加。
     *        - 当你调用该方法设置远端流音量后，如果远端退房，接口设置失效。
     * @list Audio Management
     */
    /**
     * @locale en
     * @type api
     * @brief Set the audio volume of playing the received remote stream. You must join the room before calling the API. The validity of the setting is not associated with the publishing status of the stream.
     * @param stream_key See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param volume The ratio between the playing voilume of the original volume. The range is `[0, 400]` with overflow protection. The unit is %. <br>
     *               For better audio quality, you are recommended to se the value to `[0, 100]`.
     * @return result <br>
     *        - 0: Success.
     *        - <0: Failure. See ReturnStatus{@link #ReturnStatus}.
     * @note Assume that a remote user A is always within the scope of the adjusted target users: <br>
     *        - When this API is used together with setRemoteRoomAudioPlaybackVolume{@link #IRTCRoom#setRemoteRoomAudioPlaybackVolume}, the volume of local listening user A is the volume set by the API called later;
     *        - When this API is used together with the setPlaybackVolume{@link #IRTCEngine#setPlaybackVolume}, the volume of local listening user A will be the superposition of the two set volume effects.
     *        - When you call this API to set the remote stream volume, if the remote user leaves the room, the setting will be invalid.
     * @list Audio Management
     */
    virtual int setRemoteAudioPlaybackVolume(const RemoteStreamKey& stream_key, int volume) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 开启/关闭音量均衡功能。 <br>
     *        开启音量均衡功能后，人声的响度会调整为 -16lufs。如果已调用 setAudioMixingLoudness{@link #IAudioMixingManager#setAudioMixingLoudness} 传入了混音音乐的原始响度，此音乐播放时，响度会调整为 -20lufs。（Linux 不支持）
     * @param enable 是否开启音量均衡功能： <br>
     *       - true: 是
     *       - false: 否
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 该接口须在调用 startAudioMixing{@link #IAudioMixingManager#startAudioMixing} 开始播放音频文件之前调用。
     * @list 音频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Enables/disables the loudness equalization function. <br>
     *        If you call this API with the parameter set to True, the loudness of user's voice will be adjusted to -16lufs. If then you also call setAudioMixingLoudness{@link #IAudioMixingManager#setAudioMixingLoudness} and import the original loudness of the audio data used in audio mixing, the loudness will be adjusted to -20lufs when the audio data starts to play.
     * @param enable Whether to enable loudness equalization function: <br>
     *        - true: Yes
     *        - false: No
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note You must call this API before starting to play the audio file with startAudioMixing{@link #IAudioMixingManager#startAudioMixing}.
     * @list Audio Management
     */
    virtual int enableVocalInstrumentBalance(bool enable) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 打开/关闭音量闪避功能，适用于在 RTC 通话过程中会同时播放短视频或音乐的场景，如“一起看”、“在线 KTV”等。 <br>
     *        开启该功能后，当检测到远端人声时，RTC 的本地的媒体播放音量会自动减弱，从而保证远端人声的清晰可辨；当远端人声消失时，RTC 的本地媒体音量会恢复到闪避前的音量水平。
     * @param enable 是否开启音量闪避： <br>
     *        - true: 是
     *        - false: 否
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list 音频管理
     */
    /**
     * @locale en
     * @type api
     * @brief Enables/disables the playback ducking function. This function is usually used in scenarios where short videos or music will be played simultaneously during RTC calls. <br>
     *        With the function on, if remote voice is detected, the local media volume of RTC will be lowered to ensure the clarity of the remote voice. If remote voice disappears, the local media volume of RTC restores.
     * @param enable Whether to enable playback ducking: <br>
     *        - true: Yes
     *        - false: No
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Audio Management
     */
    virtual int enablePlaybackDucking(bool enable) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 注册本地视频帧监测器。 <br>
     *        无论使用内部采集还是自定义采集，调用该方法后，SDK 每监测到一帧本地视频帧时，都会将视频帧信息通过 onLocalEncodedVideoFrame{@link #ILocalEncodedVideoFrameObserver#onLocalEncodedVideoFrame} 回调给用户
     * @param observer 本地视频帧监测器，参看 ILocalEncodedVideoFrameObserver{@link #ILocalEncodedVideoFrameObserver}。将参数设置为 nullptr 则取消注册。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 该方法可在进房前后的任意时间调用，在进房前调用可保证尽可能早地监测视频帧并触发回调
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Register a local video frame observer. <br>
     *        This method applys to both internal capturing and custom capturing. <br>
     *        After calling this API, SDK triggers onLocalEncodedVideoFrame{@link #ILocalEncodedVideoFrameObserver#onLocalEncodedVideoFrame} whenever a video frame is captured.
     * @param observer Local video frame observer. See ILocalEncodedVideoFrameObserver{@link #ILocalEncodedVideoFrameObserver}. You can cancel the registration by setting it to `nullptr`.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note You can call this API before or after entering the RTC room. Calling this API before entering the room ensures that video frames are monitored and callbacks are triggered as early as possible.
     * @list Custom Stream Processing
     */
    virtual int registerLocalEncodedVideoFrameObserver(ILocalEncodedVideoFrameObserver* observer) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 注册远端编码后视频数据回调。 <br>
     *        完成注册后，当 SDK 监测到远端编码后视频帧时，会触发 onRemoteEncodedVideoFrame{@link #IRemoteEncodedVideoFrameObserver#onRemoteEncodedVideoFrame} 回调
     * @param observer 远端编码后视频数据监测器，参看 IRemoteEncodedVideoFrameObserver{@link #IRemoteEncodedVideoFrameObserver}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 更多自定义解码功能说明参看 [自定义视频编解码](https://www.volcengine.com/docs/6348/82921#%E8%87%AA%E5%AE%9A%E4%B9%89%E8%A7%86%E9%A2%91%E8%A7%A3%E7%A0%81)。
     *       - 该方法适用于手动订阅，并且进房前后均可调用，建议在进房前调用。
     *       - 引擎销毁前需取消注册，调用该方法将参数设置为 nullptr 即可。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Video data callback after registering remote encoding. <br>
     *         After registration, when the SDK detects a remote encoded video frame, it will trigger the onRemoteEncodedVideoFrame{@link #IRemoteEncodedVideoFrameObserver#onRemoteEncodedVideoFrame} callback
     * @param observer Remote encoded video data monitor. See IRemoteEncodedVideoFrameObserver{@link #IRemoteEncodedVideoFrameObserver}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - See [Custom Video Encoding and Decoding](https://docs.byteplus.com/byteplus-rtc/docs/82921#custom-video-decoding) for more details about custom video decoding.
     *        - This method applys to manual subscription mode and can be called either before or after entering the Room. It is recommended to call it before entering the room.
     *        - The engine needs to be unregistered before it is destroyed. Call this method to set the parameter to nullptr.
     * @list Custom Stream Processing
     */
    virtual int registerRemoteEncodedVideoFrameObserver(IRemoteEncodedVideoFrameObserver* observer) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 注册自定义编码帧推送事件回调
     * @param encoder_handler 自定义编码帧回调类，参看 IExternalVideoEncoderEventHandler{@link #IExternalVideoEncoderEventHandler}
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *       - 该方法需在进房前调用。
     *       - 引擎销毁前需取消注册，调用该方法将参数设置为 nullptr 即可。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Register custom encoded frame push event callback
     * @param encoder_handler Custom encoded frame callback class. See IExternalVideoEncoderEventHandler{@link #IExternalVideoEncoderEventHandler}
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - This method needs to be called before entering the room.
     *        - The engine needs to be unregistered before it is destroyed. Call this method to set the parameter to nullptr.
     * @list Custom Stream Processing
     */
    virtual int setExternalVideoEncoderEventHandler(IExternalVideoEncoderEventHandler* encoder_handler) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 推送自定义编码后的视频流
     * @param index 需要推送的编码流的属性，参看 StreamIndex{@link #StreamIndex}
     * @param video_index 对应的编码流下标，从 0 开始，如果调用 setVideoEncoderConfig{@link #IRTCEngine#setVideoEncoderConfig} 设置了多路流，此处数量须与之保持一致
     * @param video_stream 编码流视频帧信息，参看 IEncodedVideoFrame{@link #IEncodedVideoFrame}。
     * @return 方法调用结果： <br>
     *        - 0：成功；
     *        - <0：失败。具体失败原因参看 ReturnStatus{@link #ReturnStatus}。
     * @note
     *        - 目前仅支持推送 H264 和 ByteVC1 格式的视频帧，且视频流协议格式须为 Annex B 格式。
     *        - 该函数运行在用户调用线程内
     *        - 推送自定义编码视频帧前，必须调用 setVideoSourceType{@link #IRTCEngine#setVideoSourceType} 将视频输入源切换至自定义编码视频源。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Push custom encoded video stream
     * @param index The attributes of the encoded stream that need to be pushed. See StreamIndex{@link #StreamIndex}
     * @param video_index The corresponding encoded stream subscript, starting from 0, if you call setVideoEncoderConfig{@link #IRTCEngine#setVideoEncoderConfig} to set multiple streams, the number here must be consistent with it
     * @param video_stream Encoded stream video frame information. See IEncodedVideoFrame{@link #IEncodedVideoFrame}.
     * @return API call result: <br>
     *        - 0: Success.
     *        - <0: Failure. See ReturnStatus{@link #ReturnStatus} for specific reasons.
     * @note
     *         - Currently, only video frames in H264 and ByteVC1 formats are supported, and the video stream protocol must be in an Annex B format.
     *         - This function runs within the user calling thread
     *         - Before pushing a custom encoded video frame, you must call setVideoSourceType{@link #IRTCEngine#setVideoSourceType} to switch the video input source to the custom encoded video source.
     * @list Custom Stream Processing
     */
    virtual int pushExternalEncodedVideoFrame(StreamIndex index, int video_index, IEncodedVideoFrame* video_stream) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 在订阅远端视频流之前，设置远端视频数据解码方式
     * @param key 远端流信息，即对哪一路视频流进行解码方式设置，参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param config 视频解码方式，参看 VideoDecoderConfig{@link #VideoDecoderConfig}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 当你想要对远端流进行自定义解码时，你需要先调用 registerRemoteEncodedVideoFrameObserver{@link #IRTCEngine#registerRemoteEncodedVideoFrameObserver} 注册远端视频流监测器，然后再调用该接口将解码方式设置为自定义解码。监测到的视频数据会通过 onRemoteEncodedVideoFrame{@link #IRemoteEncodedVideoFrameObserver#onRemoteEncodedVideoFrame} 回调出来。
     *        - 自 3.56 起，要用于自动订阅场景下，你可以设置 `key` 中的 `RoomId` 和 `UserId` 为 `nullptr`，此时，通过此接口设置的解码方式根据 `key` 中的 `StreamIndex` 值，适用于所有的远端主流或屏幕流的解码方式。
     * @list 自定义流处理
     */
    /**
     * @locale en
     * @type api
     * @brief Before subscribing to the remote video stream, set the remote video data decoding method
     * @param key The remote stream information, that is, which video stream is decoded. See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param config Video decoding method. See VideoDecoderConfig{@link #VideoDecoderConfig}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - When you want to custom decode a remote stream, you need to call registerRemoteEncodedVideoFrameObserver{@link #IRTCEngine#registerRemoteEncodedVideoFrameObserver} to register the remote video stream monitor, and then call the interface to set the decoding method to custom decoding. The monitored video data will be called back through onRemoteEncodedVideoFrame{@link #IRemoteEncodedVideoFrameObserver#onRemoteEncodedVideoFrame}.
     *         - Since version 3.56, for automatic subscription, you can set the `RoomId` and `UserId` of `key` as `nullptr`. In this case, the decoding settings set by calling the API applies to all remote main streams or screen sharing streams based on the `StreamIndex` value of `key`.
     * @list Custom Stream Processing
     */
    virtual int setVideoDecoderConfig(RemoteStreamKey key, VideoDecoderConfig config) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 在订阅远端视频流之后，向远端请求关键帧
     * @param stream_info 远端流信息，参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 该方法仅适用于手动订阅模式，并且在成功订阅远端流之后使用。
     *        - 该方法适用于调用 setVideoDecoderConfig{@link #IRTCEngine#setVideoDecoderConfig} 开启自定义解码功能后，并且自定义解码失败的情况下使用
     * @list 音视频传输
     */
    /**
     * @locale en
     * @type api
     * @brief After subscribing to the remote video stream, request the key frame to the remote
     * @param stream_info The remote stream information. See RemoteStreamKey{@link #RemoteStreamKey}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - This method is only suitable for manual subscription mode and is used after successful subscription to the remote flow.
     *         - This method is suitable for calling setVideoDecoderConfig{@link #IRTCEngine#setVideoDecoderConfig} to turn on the custom decoding function, and the custom decoding fails
     * @list Audio & Video Transport
     */
    virtual int requestRemoteVideoKeyFrame(const RemoteStreamKey& stream_info) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 发送音频流同步信息。将消息通过音频流发送到远端，并实现与音频流同步，该接口调用成功后，远端用户会收到 onStreamSyncInfoReceived{@link #IRTCEngineEventHandler#onStreamSyncInfoReceived} 回调。
     * @param data 消息内容。
     * @param length 消息长度。取值范围是 [1,255] 字节，建议小于 16 字节，否则可能占用较大带宽。
     * @param config 媒体流信息同步的相关配置，详见 StreamSycnInfoConfig{@link #StreamSycnInfoConfig} 。
     * @return
     *        - >=0: 消息发送成功。返回成功发送的次数。
     *        - -1: 消息发送失败。消息长度大于 255 字节。
     *        - -2: 消息发送失败。传入的消息内容为空。
     *        - -3: 消息发送失败。通过屏幕流进行消息同步时，此屏幕流还未发布。
     *        - -4: 消息发送失败。通过用麦克风或自定义设备采集到的音频流进行消息同步时，此音频流还未发布，详见错误码 ErrorCode{@link #ErrorCode}。
     * @note
     * - 调用本接口的频率建议不超过 50 次每秒。
     * - 在 `kRoomProfileTypeInteractivePodcast` 房间模式下，此消息一定会送达。在其他房间模式下，如果本地用户未说话，此消息不一定会送达。
     * @list 消息
     */
    /**
     * @locale en
     * @type api
     * @brief Send audio stream synchronization information. The message is sent to the remote end through the audio stream and synchronized with the audio stream. After the interface is successfully called, the remote user will receive a onStreamSyncInfoReceived{@link #IRTCEngineEventHandler#onStreamSyncInfoReceived} callback.
     * @param data Message content.
     * @param length Message length. Message length must be [1,16] bytes.
     * @param config For configuration of media stream information synchronization. See StreamSycnInfoConfig{@link #StreamSycnInfoConfig}.
     * @return
     *         - > = 0: Message sent successfully. Returns the number of successful sends.
     *         - -1: Message sending failed. Message length greater than 16 bytes.
     *         - -2: Message sending failed. The content of the incoming message is empty.
     *         - -3: Message sending failed. This screen stream was not published when the message was synchronized through the screen stream.
     *         - -4: Message sending failed. This audio stream is not yet published when you synchronize messages with an audio stream captured by a microphone or custom device, as described in ErrorCode{@link #ErrorCode}.
     * @note
     * - Regarding the frequency, we recommend no more than 50 calls per second.
     * - When using `kRoomProfileTypeInteractivePodcast` as room profile, the data will be delivered. For other coom profiles, the data may be lost when the local user is muted.
     * @list Messaging
     */
    virtual int sendStreamSyncInfo(const uint8_t* data, int32_t length, const StreamSycnInfoConfig& config) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 开启本地语音变调功能，多用于 K 歌场景。 <br>
     *        使用该方法，你可以对本地语音的音调进行升调或降调等调整。
     * @param pitch 相对于语音原始音调的升高/降低值，取值范围[-12，12]，默认值为 0，即不做调整。 <br>
     *        取值范围内每相邻两个值的音高距离相差半音，正值表示升调，负值表示降调，设置的绝对值越大表示音调升高或降低越多。 <br>
     *        超出取值范围则设置失败，并且会触发 onWarning{@link #IRTCEngineEventHandler#onWarning} 回调，提示 WarningCode{@link #WarningCode} 错误码为 `WARNING_CODE_SET_SCREEN_STREAM_INVALID_VOICE_PITCH` 设置语音音调不合法
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list 音频处理
     */
    /**
     * @locale en
     * @type api
     * @brief Change local voice to a different key, mostly used in Karaoke scenarios. <br>
     *        You can adjust the pitch of local voice such as ascending or descending with this method.
     * @param pitch The value that is higher or lower than the original local voice within a range from -12 to 12. The default value is 0, i.e. No adjustment is made. <br>
     *        The difference in pitch between two adjacent values within the value range is a semitone, with positive values indicating an ascending tone and negative values indicating a descending tone, and the larger the absolute value set, the more the pitch is raised or lowered. <br>
     *        Out of the value range, the setting fails and triggers onWarning{@link #IRTCEngineEventHandler#onWarning} callback, indicating `WARNING_CODE_SET_SCREEN_STREAM_INVALID_VOICE_PITCH` for invalid value setting with WarningCode{@link #WarningCode}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Audio Processing
     */
    virtual int setLocalVoicePitch(int pitch) = 0;
    
    /**
     * @locale zh
     * @type api
     * @brief 在指定视频流上添加水印。
     * @param stream_index 需要添加水印的视频流属性，参看 StreamIndex{@link #StreamIndex}。
     * @param image_path 水印图片路径，仅支持本地文件绝对路径，长度限制为 512 字节。 <br>
     *          水印图片为 PNG 或 JPG 格式。
     * @param config 水印参数，参看 RTCWatermarkConfig{@link #RTCWatermarkConfig}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 调用 clearVideoWatermark{@link #IRTCEngine#clearVideoWatermark} 移除指定视频流的水印。
     *        - 同一路流只能设置一个水印，新设置的水印会代替上一次的设置。你可以多次调用本方法来设置不同流的水印。
     *        - 若开启本地预览镜像，或开启本地预览和编码传输镜像，则远端水印均不镜像；在开启本地预览水印时，本端水印会镜像。
     *        - 开启大小流后，水印对大小流均生效，且针对小流进行等比例缩小。
     * @list 视频处理
     */
    /**
     * @locale en
     * @type api
     * @brief Adds watermark to designated video stream.
     * @param stream_index The index of the target stream. See StreamIndex{@link #StreamIndex}.
     * @param image_path The absolute path of the watermark image. The path should be less than 512 bytes. <br>
     *        The watermark image should be in PNG or JPG format.
     * @param config Watermark configurations. See RTCWatermarkConfig{@link #RTCWatermarkConfig}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *        - Call clearVideoWatermark{@link #IRTCEngine#clearVideoWatermark} to remove the watermark from the designated video stream.
     *        - You can only add one watermark to one video stream. The newly added watermark replaces the previous one. You can call this API multiple times to add watermarks to different streams.
     *        - If you mirror the preview, or the preview and the published stream, the watermark will also be mirrored locally, but the published watermark will not be mirrored.
     *        - When you enable simulcast mode, the watermark will be added to all video streams, and it will be scaled down to smaller encoding configurations accordingly.
     * @list Video Processing
     */
    virtual int setVideoWatermark(StreamIndex stream_index, const char* image_path, RTCWatermarkConfig config) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 移除指定视频流的水印。
     * @param stream_index 需要移除水印的视频流属性，参看 StreamIndex{@link #StreamIndex}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @list 视频处理
     */
    /**
     * @locale en
     * @type api
     * @brief  Removes video watermark from designated video stream.
     * @param stream_index The index of the target stream. See StreamIndex{@link #StreamIndex}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @list Video Processing
     */
    virtual int clearVideoWatermark(StreamIndex stream_index) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 截取本地视频画面
     * @param stream_index 截图的视频流的属性，参看 StreamIndex{@link #StreamIndex}。
     * @param callback 本地截图的回调。参看 ISnapshotResultCallback{@link #ISnapshotResultCallback}。
     * @return 本地截图任务的编号，从 `1` 开始递增。
     * @note
     *        - 对截取的画面，包含本地视频处理的全部效果，包含旋转，镜像，美颜等。
     *        - 不管采用 SDK 内部采集，还是自定义采集，都可以进行截图。
     * @list 高级功能
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Take a snapshot of the local video.
     * @param stream_index See StreamIndex{@link #StreamIndex}.
     * @param callback See ISnapshotResultCallback{@link #ISnapshotResultCallback}.
     * @return The index of the local snapshot task, starting from `1`.
     * @note
     *        - The snapshot is taken with all video effects on, like rotation, and mirroring.
     *        - You can take the snapshot either using SDK internal video capture or customized capture.
     * @list Advanced Features
     */
    virtual long takeLocalSnapshot(const StreamIndex stream_index, ISnapshotResultCallback* callback) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 截取远端视频画面
     * @param stream_key 截图的视频流，参看 RemoteStreamKey{@link #RemoteStreamKey}。
     * @param callback 参看 ISnapshotResultCallback{@link #ISnapshotResultCallback}。
     * @return 远端截图任务的编号，从 `1` 开始递增。
     * @list 高级功能
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Take a snapshot of the remote video.
     * @param stream_key See RemoteStreamKey{@link #RemoteStreamKey}.
     * @param callback See ISnapshotResultCallback{@link #ISnapshotResultCallback}.
     * @return The index of the remote snapshot task, starting from `1`.
     * @list Advanced Features
     */
    virtual long takeRemoteSnapshot(const RemoteStreamKey stream_key, ISnapshotResultCallback* callback) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 摄像头处于关闭状态时，使用静态图片填充本地推送的视频流。 <br>
     *        调用 `stopVideoCapture` 接口时，会开始推静态图片。若要停止发送图片，可传入空字符串或启用内部摄像头采集。 <br>
     *        可重复调用该接口来更新图片。
     * @param file_path 设置静态图片的路径。 <br>
     *        支持本地文件绝对路径，不支持网络链接，长度限制为 512 字节。 <br>
     *        静态图片支持类型为 JPEG/JPG、PNG、BMP。 <br>
     *        若图片宽高比与设置的编码宽高比不一致，图片会按编码高宽等比缩放，并裁剪长边。推流帧率与码率与设置的编码参数一致。
     * @return
     *        - 0: 成功。
     *        - -1: 失败。
     * @note
     *        - 该接口只适用于 SDK 内部摄像头采集，不适用于自定义视频采集。
     *        - 本地预览无法看到静态图片。
     *        - 进入房间前后均可调用此方法。在多房间场景中，静态图片仅在发布的房间中生效。
     *        - 针对该静态图片，滤镜和镜像效果不生效，水印效果生效。
     *        - 只有主流能设置静态图片，屏幕流不支持设置。
     *        - 开启大小流后，静态图片对大小流均生效，且针对小流进行等比例缩小。
     * @list 音视频传输
     */
    /**
     * @locale en
     * @type api
     * @brief Set an alternative image when the local internal video capture is not enabled. <br>
     *        When you call `stopVideoCapture`, an alternative image will be pushed. You can set the path to null or open the camera to stop publishing the image. <br>
     *        You can repeatedly call this API to update the image.
     * @param file_path Set the path of the static image. <br>
     *        You can use the absolute path (file://xxx). The maximum size for the path is 512 bytes. <br>
     *        You can upload a .JPG, .JPEG, .PNG, or .BMP file. <br>
     *        When the aspect ratio of the image is inconsistent with the video encoder configuration, keep the aspect-ratio to scale the image and the crop the longer sides. The framerate and the bitrate are consistent with the video encoder configuration.
     * @return
     *        - 0: Success.
     *        - -1: Failure.
     * @note
     *        - The API is only effective when publishing an internally captured video.
     *        - You cannot locally preview the image.
     *        - You can call this API before and after joining an RTC room. In the multi-room mode, the image can be only displayed in the room you publish the stream.
     *        - You cannot apply effects like filters and mirroring to the image, while you can watermark the image.
     *        - The image is not effective for a screen-sharing stream.
     *        - When you enable the simulcast mode, the image will be added to all video streams, and it will be proportionally scaled down to smaller encoding configurations.
     * @list Audio & Video Transport
     */
    virtual int setDummyCaptureImagePath(const char* file_path) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 开启云代理
     * @param configuration 云代理服务器信息列表。参看 CloudProxyConfiguration{@link #CloudProxyConfiguration}。
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note
     *        - 在加入房间前调用此接口
     *        - 在开启云代理后，进行通话前网络探测
     *        - 开启云代理后，并成功链接云代理服务器后，会收到 onCloudProxyConnected{@link #IRTCEngineEventHandler#onCloudProxyConnected}。
     *        - 要关闭云代理，调用 stopCloudProxy{@link #IRTCEngine#stopCloudProxy}。
     * @list 通话加密
     */
    /**
     * @locale en
     * @type api
     * @brief  Start cloud proxy
     * @param configuration cloud proxy informarion list. See CloudProxyConfiguration{@link #CloudProxyConfiguration}.
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note
     *         - Call this API before joining the room.
     *         - Start pre-call network detection after starting cloud proxy.
     *         - After starting cloud proxy and connects the cloud proxy server successfully, receives onCloudProxyConnected{@link #IRTCEngineEventHandler#onCloudProxyConnected}.
     *         - To stop cloud proxy, call stopCloudProxy{@link #IRTCEngine#stopCloudProxy}.
     * @list Encryption
     */
    virtual int startCloudProxy(const CloudProxyConfiguration& configuration) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 关闭云代理
     * @return
     *        - 0: 调用成功。
     *        - < 0 : 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明
     * @note 要开启云代理，调用 startCloudProxy{@link #IRTCEngine#startCloudProxy}。
     * @list 通话加密
     */
    /**
     * @locale en
     * @type api
     * @brief  Stop cloud proxy
     * @return
     *        - 0: Success.
     *        - < 0 : Fail. See ReturnStatus{@link #ReturnStatus} for more details
     * @note To start cloud proxy, call startCloudProxy{@link #IRTCEngine#startCloudProxy}.
     * @list Encryption
     */
    virtual int stopCloudProxy() = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 开启音视频回路测试。 <br>
     *        在进房前，用户可调用该接口对音视频通话全链路进行检测，包括对音视频设备以及用户上下行网络的检测，从而帮助用户判断是否可以正常发布和接收音视频流。 <br>
     *        开始检测后，SDK 会录制你声音或视频。如果你在设置的延时范围内收到了回放，则视为音视频回路测试正常。
     * @param echo_test_config 回路测试参数设置，参看 EchoTestConfig{@link #EchoTestConfig}。
     * @param play_delay_time 音视频延迟播放的时间间隔，用于指定在开始检测多长时间后期望收到回放。取值范围为 [2,10]，单位为秒，默认为 2 秒。
     * @return 方法调用结果： <br>
     *        - 0：成功
     *        - -2：失败，参数异常
     *        - -4：失败，用户已进房
     *        - -6：失败，当前用户已经在检测中
     *        - -7：失败，音视频均不检查
     *        - -8：失败，已经存在相同 roomId 的房间
     * @note
     *        - 调用该方法开始音视频回路检测后，你可以调用 stopEchoTest{@link #IRTCEngine#stopEchoTest} 立即结束测试，也可等待测试 60s 后自动结束，以更换设备进行下一次测试，或进房。
     *        - 在该方法之前调用的所有跟设备控制、流控制相关的方法均在开始检测时失效，在结束检测后恢复生效。
     *        - 在调用 startEchoTest{@link #IRTCEngine#startEchoTest} 和 stopEchoTest{@link #IRTCEngine#stopEchoTest} 之间调用的所有跟设备采集、流控制、进房相关的方法均不生效，并会收到 onWarning{@link #IRTCEngineEventHandler#onWarning} 回调，提示警告码为 `kWarningCodeInEchoTestMode`。
     *        - 音视频回路检测的结果会通过 onEchoTestResult{@link #IRTCEngineEventHandler#onEchoTestResult} 回调通知。
     * @list 网络管理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Starts a call test. <br>
     *        Before entering the room, you can call this API to test whether your local audio/video equipment as well as the upstream and downstream networks are working correctly. <br>
     *        Once the test starts, SDK will record your sound or video. If you receive the playback within the delay range you set, the test is considered normal.
     * @param echo_test_config Test configurations, see EchoTestConfig{@link #EchoTestConfig}.
     * @param play_delay_time Delayed audio/video playback time specifying how long you expect to receive the playback after starting the. The range of the value is [2,10] in seconds and the default value is 2.
     * @return API call result: <br>
     * - 0: Success.
     * - -2: Failure due to parameter exception.
     * - -4: Failure because the user has joined a room.
     * - -6: Failure for the testing is in progress.
     * - -7: Failure for the request included neither video nor audio.
     * - -8: Failure for the roomID is already used.
     * @note
     *        - Once you start the test, you can either call stopEchoTest{@link #IRTCEngine#stopEchoTest} or wait until the test stops automatically after 60s, to start the next test or enter the room.
     *        - All APIs related to device control and stream control called before this API are invalidated during the test and are restored after the test.
     *        - All APIs related to device control, stream control, and room entry called during the test do not take effect, and you will receive onWarning{@link #IRTCEngineEventHandler#onWarning} with the warning code `kWarningCodeInEchoTestMode`.
     *        - You will receive the test result from onEchoTestResult{@link #IRTCEngineEventHandler#onEchoTestResult}.
     * @list Network Processing
     */
    virtual int startEchoTest(EchoTestConfig echo_test_config, unsigned int play_delay_time) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 停止音视频回路测试。 <br>
     *        调用 startEchoTest{@link #IRTCEngine#startEchoTest} 开启音视频回路检测后，你必须调用该方法停止检测。
     * @return 方法调用结果： <br>
     *        - 0：成功
     *        - -1：失败，未开启回路检测
     * @note 音视频回路检测结束后，所有对系统设备及音视频流的控制均会恢复到开始检测前的状态。
     * @list 网络管理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Stop the current call test. <br>
     *        After calling startEchoTest{@link #IRTCEngine#startEchoTest}, you must call this API to stop the test.
     * @return API call result: <br>
     *        - 0: Success
     *        - -1: Failure, no test is in progress
     * @note After stopping the test with this API, all the system devices and streams are restored to the state they were in before the test.
     * @list Network Processing
     */
    virtual int stopEchoTest() = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 创建 K 歌评分管理接口。
     * @return K 歌评分管理接口,详见 ISingScoringManager{@link #ISingScoringManager}。
     * @note 如需使用 K 歌评分功能，即调用该方法以及 `ISingScoringManager` 类下全部方法，需集成 SAMI 动态库，详情参看[按需集成插件](1108726)文档。
     * @list 在线 KTV
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Create a karaoke scoring management interface.
     * @return Karaoke scoring management interface, see ISingScoringManager{@link #ISingScoringManager}.
     * @note To use the karaoke scoring feature, i.e., to call this method and all methods in the `ISingScoringManager` class, you need to intergrate SAMI dynamic library. For details, see [Integrate Plugins on Demand](1108726).
     */
    virtual ISingScoringManager* getSingScoringManager() = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 通过 NTP 协议，获取网络时间。
     * @return 网络时间。参看 NetworkTimeInfo{@link #NetworkTimeInfo}。
     * @note
     *        - 第一次调用此接口会启动网络时间同步功能，并返回 `0`。同步完成后，会收到 onNetworkTimeSynchronized{@link #IRTCEngineEventHandler#onNetworkTimeSynchronized}，此后，再次调用此 API，即可获取准确的网络时间。
     *        - 在合唱场景下，合唱参与者应在相同的网络时间播放背景音乐。
     * @list 网络管理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Obtain the synchronization network time information.
     * @return See NetworkTimeInfo{@link #NetworkTimeInfo}.
     * @note
     *        - When you call this API for the first time, you starts synchornizing the network time information and receive the return value `0`. After the synchonization finishes, you will receive onNetworkTimeSynchronized{@link #IRTCEngineEventHandler#onNetworkTimeSynchronized}. After that, calling this API will get you the correct network time.
     *        - Under chorus scenario, participants shall start audio mixing at the same network time.
     * @list Network Processing
     */
    virtual NetworkTimeInfo getNetworkTimeInfo() = 0;
    /**
     * @locale zh
     * @hidden(Linux,macOS)
     * @type api
     * @brief 创建 KTV 管理接口。
     * @return KTV 管理接口，参看 IKTVManager{@link #IKTVManager}。
     * @list 在线 KTV
     */
    /**
     * @locale en
     * @hidden currently not available
     * @type api
     * @brief Creates the KTV manager interfaces.
     * @return KTV manager interfaces. See IKTVManager{@link #IKTVManager}.
     * @list Online Karaoke
     */
    virtual IKTVManager* getKTVManager() = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 开启通话前回声检测
     * @param test_audio_file_path 用于回声检测的音频文件的绝对路径，路径字符串使用 UTF-8 编码格式，支持以下音频格式: mp3，aac，m4a，3gp，wav。 <br>
     *         音频文件不为静音文件，推荐时长为 10 ～ 20 秒。
     * @return 方法调用结果： <br>
     *        - 0: 成功。
     *        - -1：失败。上一次检测未结束，请先调用 stopHardwareEchoDetection{@link #IRTCEngine#stopHardwareEchoDetection} 停止检测 后重新调用本接口。
     *        - -2：失败。路径不合法或音频文件格式不支持。
     * @note
     *        - 只有当 RoomProfileType{@link #RoomProfileType} 为 `kRoomProfileTypeMeeting` 和 `kRoomProfileTypeMeetingRoom` 时支持开启本功能。
     *        - 开启检测前，你需要向用户获取音频设备的使用权限。
     *        - 开启检测前，请确保音频设备没有被静音，采集和播放音量正常。
     *        - 调用本接口后监听 onHardwareEchoDetectionResult{@link #IRTCEngineEventHandler#onHardwareEchoDetectionResult} 获取检测结果。
     *        - 检测期间，进程将独占音频设备，无法使用其他音频设备测试接口： startEchoTest{@link #IRTCEngine#startEchoTest}、startAudioDeviceRecordTest{@link #IAudioDeviceManager#startAudioDeviceRecordTest} 或 startAudioPlaybackDeviceTest{@link #IAudioDeviceManager#startAudioPlaybackDeviceTest}。
     *        - 调用 stopHardwareEchoDetection{@link #IRTCEngine#stopHardwareEchoDetection} 停止检测，释放对音频设备的占用。
     * @list 音频管理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Start echo detection before joining a room.
     * @param test_audio_file_path Absolute path of the music file for the detection. It is expected to encode with UTF-8. The following files are supported: mp3, aac, m4a, 3gp, wav. <br>
     *          We recommend to assign a music file whose duration is between 10 to 20 seconds. <br>
     *        Do not pass a Silent file.
     * @return Method call result: <br>
     *        - 0: Success.
     *        - -1: Failure due to the onging process of the previous detection. Call stopHardwareEchoDetection{@link #IRTCEngine#stopHardwareEchoDetection} to stop it before calling this API again.
     *        - -2: Failure due to an invalid file path or file format.
     * @note
     *        - You can use this feature only when RoomProfileType{@link #RoomProfileType} is set to `kRoomProfileTypeMeeting` or `kRoomProfileTypeMeetingRoom`.
     *        - Before calling this API, ask the user for the permissions to access the local audio devices.
     *        - Before calling this api, make sure the audio devices are activate and keep the capture volume and the playback volume within a reasonable range.
     *        - The detection result is passed as the argument of onHardwareEchoDetectionResult{@link #IRTCEngineEventHandler#onHardwareEchoDetectionResult}.
     *        - During the detection, the SDK is not able to response to the other testing APIs, such as startEchoTest{@link #IRTCEngine#startEchoTest}、startAudioDeviceRecordTest{@link #IAudioDeviceManager#startAudioDeviceRecordTest} or startAudioPlaybackDeviceTest{@link #IAudioDeviceManager#startAudioPlaybackDeviceTest}.
     *        - Call stopHardwareEchoDetection{@link #IRTCEngine#stopHardwareEchoDetection} to stop the detection and release the audio devices.
     * @list Audio Management
     */
    virtual int startHardwareEchoDetection(const char* test_audio_file_path) = 0;
    /**
     * @locale zh
     * @hidden(Linux)
     * @type api
     * @brief 停止通话前回声检测
     * @return 方法调用结果： <br>
     *        - 0: 成功。
     *        - -1：失败。
     * @note
     *       - 关于开启通话前回声检测，参看 startHardwareEchoDetection{@link #IRTCEngine#startHardwareEchoDetection} 。
     *       - 建议在收到 onHardwareEchoDetectionResult{@link #IRTCEngineEventHandler#onHardwareEchoDetectionResult} 通知的检测结果后，调用本接口停止检测。
     *       - 在用户进入房间前结束回声检测，释放对音频设备的占用，以免影响正常通话。
     * @list 音频管理
     */
    /**
     * @locale en
     * @hidden(Linux)
     * @type api
     * @brief Stop the echo detection before joining a room.
     * @return   Method call result: <br>
     *        - 0: Success.
     *        - -1: Failure.
     * @note
     *        - Refer to startHardwareEchoDetection{@link #IRTCEngine#startHardwareEchoDetection} for information on how to start a echo detection.
     *        - We recommend calling this API to stop the detection once getting the detection result from onHardwareEchoDetectionResult{@link #IRTCEngineEventHandler#onHardwareEchoDetectionResult}.
     *        - You must stop the echo detection to release the audio devices before the user joins a room. Otherwise, the detection may interfere with the call.
     * @list Audio Management
     */
    virtual int stopHardwareEchoDetection() = 0;
    /**
     * @locale zh
     * @hidden(macOS, Windows, Linux)
     * @type api
     * @brief 启用蜂窝网络辅助增强，改善通话质量。
     * @param config 参看 MediaTypeEnhancementConfig{@link #MediaTypeEnhancementConfig}。
     * @return 方法调用结果： <br>
     *        - 0: 成功。
     *        - -1：失败，内部错误。
     *        - -2: 失败，输入参数错误。
     * @note 此功能默认不开启。
     * @list Audio Management
     */
    /**
     * @locale en
     * @hidden(macOS, Windows, Linux)
     * @type api
     * @brief Enable cellular network assisted communication to improve call quality.
     * @param config See MediaTypeEnhancementConfig{@link #MediaTypeEnhancementConfig}.
     * @return Method call result: <br>
     *        - 0: Success.
     *        - -1: Failure, internal error.
     *        - -2: Failure, invalid parameters.
     * @note The function is off by default.
     * @list Audio Management
     */
    virtual int setCellularEnhancement(const MediaTypeEnhancementConfig& config) = 0;
    /**
     * @locale zh
     * @type api
     * @brief 设置本地代理。
     * @param configurations 本地代理配置参数。参看 LocalProxyConfiguration{@link #LocalProxyConfiguration}。 <br>
     *        你可以根据自己的需要选择同时设置 Http 隧道 和 Socks5 两类代理，或者单独设置其中一类代理。如果你同时设置了 Http 隧道 和 Socks5 两类代理，此时，媒体和信令采用 Socks5 代理， Http 请求采用 Http 隧道代理；如果只设置 Http 隧道 或 Socks5 一类代理，媒体、信令和 Http 请求均采用已设置的代理。 <br>
     *        调用此接口设置本地代理后，若想清空当前已有的代理设置，可再次调用此接口，选择不设置任何代理即可清空。
     * @param configuration_num 本地代理配置参数的数量。
     * @note
     *       - 该方法需要在进房前调用。
     *       - 调用该方法设置本地代理后，SDK 会触发 onLocalProxyStateChanged{@link #IRTCEngineEventHandler#onLocalProxyStateChanged} ，返回代理连接的状态。
     * @list 通话加密
     */
    /**
     * @locale en
     * @type api
     * @brief Sets local proxy.
     * @param configurations Local proxy configurations. Refer to LocalProxyConfiguration{@link #LocalProxyConfiguration}. <br>
     *         You can set both Http tunnel and Socks5 as your local proxies, or only set one of them based on your needs. If you set both Http tunnel and Socks5 as your local proxies, then media traffic and signaling are routed through Socks5 proxy and Http requests through Http tunnel proxy. If you set either Http tunnel or Socks5 as your local proxy, then media traffic, signaling and Http requests are all routed through the proxy you chose. <br>
     *         If you want to remove the existing local proxy configurations, you can call this API with the parameter set to null.
     * @param configuration_num The number of local proxy configurations.
     * @note
     *       - You must call this API before joining the room.
     *       - After calling this API, you will receive onLocalProxyStateChanged{@link #IRTCEngineEventHandler#onLocalProxyStateChanged} callback that informs you of the states of local proxy connection.
     * @list Encryption
     */
    virtual int setLocalProxy(const LocalProxyConfiguration* configurations, int configuration_num) = 0;
    /**
     * @locale zh
     * @hidden(Android,iOS)
     * @valid since 3.57
     * @type api
     * @brief 设置视频暗光增强模式。 <br>
     *        对于光线不足、照明不均匀或背光等场景下推荐开启，可有效改善画面质量。
     * @param mode 默认不开启。参看 VideoEnhancementMode{@link #VideoEnhancementMode}。
     * @return
     *        - 0: API 调用成功。会立即生效，但需要等待下载和检测完成后才能看到增强后的效果。
     *        - < 0: API 调用失败。查看 ReturnStatus{@link #ReturnStatus} 获得更多错误说明。
     * @note
     *        - 开启后会影响设备性能，应根据实际需求和设备性能决定是否开启。
     *        - 对 RTC SDK 内部采集的视频和自定义采集的视频都生效。
     * @list 视频处理
     */
    /**
     * @locale en
     * @hidden(Android,iOS)
     * @valid since 3.57
     * @type api
     * @brief Sets the video lowlight enhancement mode. <br>
     *        It can significantly improve image quality in scenarios with insufficient light, contrast lighting, or backlit situations.
     * @param mode It defaults to Disable. Refer to VideoEnhancementMode{@link #VideoEnhancementMode} for more details.
     * @return
     *        - 0: Success. After you call this method, it will take action immediately. But it may require some time for downloads and detection processes before you can see the enhancement.
     *        - < 0: Failure. See ReturnStatus{@link #ReturnStatus} for more details.
     * @note
     *        - Turning on this mode will impact device performance. This feature should be activated only when required and the device performance is adequate.
     *        - Functionality applies to videos captured by the internal module as well as those from custom collections.
     * @list Video Processing
     */
    virtual int setLowLightAdjusted(VideoEnhancementMode mode) = 0;

    virtual IWTNStream* getWTNStream() = 0;

    /**
     * @locale zh
     * @type api
     * @brief 创建 IRTCEngine 实例。 <br>
     *        如果当前进程中未创建引擎实例，那么你必须先使用此方法，以使用 RTC 提供的各种音视频能力。 <br>
     *        如果当前进程中已创建了引擎实例，再次调用此方法时，会返回已创建的引擎实例。
     * @param config 创建引擎参数配置，详见 EngineConfig{@link #EngineConfig}
     * @param handler SDK 回调给应用层的 Handler，详见 IRTCEngineEventHandler{@link #IRTCEngineEventHandler}
     * @return
     *        - IRTCEngine：创建成功。返回一个可用的 IRTCEngine{@link #IRTCEngine} 实例
     *        - Null：app_id 或者 event_handler 为空, event_handler 为空。
     * @list 引擎管理
     */
     /**
     * @locale en
     * @type api
     * @region Engine Management
     * @brief Creates an engine instance. <br>
     *        This is the very first API that you must call if you want to use all the RTC capabilities. <br>
     *        If there is no engine instance in current process, calling this API will create one. If an engine instance has been created, calling this API again will have the created engine instance returned.
     * @param config EngineConfig{@link #EngineConfig}
     * @param handler Handler sent from SDK to App. See IRTCEngineEventHandler{@link #IRTCEngineEventHandler}
     * @return
     *        - IRTCEngine: A successfully created engine instance.
     *        - Null:  app_id is null or empty. event_handler is null.
     * @list Engine Management
     * @order 1
     */
    static BYTERTC_STATIC_API IRTCEngine* createRTCEngine(const EngineConfig& config, IRTCEngineEventHandler* event_handler);
    /**
     * @locale zh
     * @hidden internal use only
     * @type api
     * @brief 创建多实例引擎对象 <br>
     *        如果当前进程中未创建引擎实例，那么你必须先使用此方法，以使用 RTC 提供的各种音视频能力。 <br>
     *        如果当前进程中已创建了引擎实例，再次调用此方法时，会返回新创建的引擎实例。
     * @param config 创建引擎参数配置，详见 EngineConfig{@link #EngineConfig}
     * @param handler SDK 回调给应用层的 Handler，详见 IRTCEngineEventHandler{@link #IRTCEngineEventHandler}
     * @return
     *        - RTCEngine：创建成功。返回一个可用的 IRTCEngine{@link #IRTCEngine} 实例
     *        - Null：.so 文件加载失败，创建失败。
     * @note 你应注意保持 handler 的生命周期必须大于 IRTCEngine{@link #IRTCEngine} 的生命周期，即 handler 必须在调用 destroyRTCEngine{@link #IRTCEngine#destroyRTCEngine} 之后销毁。
     * @list 引擎管理
     */
    /**
     * @locale en
     * @hidden internal use only
     * @type api
     * @brief Creates an engine instance for multi-engine senario. <br>
     *        This is the very first API that you must call if you want to use all the RTC capabilities. <br>
     *        If there is no engine instance in current process, calling this API will create one. If an engine instance has been created, calling this API again will have a new created engine instance returned.
     * @param config EngineConfig{@link #EngineConfig}
     * @param handler Handler sent from SDK to App. See IRTCEngineEventHandler{@link #IRTCEngineEventHandler}
     * @return
     *         - RTCEngine: A successfully created engine instance.
     *         - Null: Failed to load the .so file. No instance is returned.
     * @note The lifecycle of the handler must be longer than that of the RTCEngine, i.e. the handler must be created before calling createRTCEngine{@link #IRTCEngine#createRTCEngine} and destroyed after calling destroyRTCEngine{@link #IRTCEngine#destroyRTCEngine}.
     * @list Engine Management
     */
    static BYTERTC_STATIC_API IRTCEngine* createRTCEngineMulti(const EngineConfig& config, IRTCEngineEventHandler* event_handler);
    /**
     * @locale zh
     * @type api
     * @brief 销毁由 createRTCEngine{@link #createRTCEngine} 所创建的引擎实例，并释放所有相关资源。
     * @note
     *        - 请确保和需要销毁的 IRTCEngine{@link #IRTCEngine} 实例相关的业务场景全部结束后，才调用此方法。如果在多线程场景下，调用此接口后，又调用了其他 IRTCEngine{@link #IRTCEngine} 相关接口，可能导致 SDK 崩溃。该方法在调用之后，会销毁所有和此 IRTCEngine{@link #IRTCEngine} 实例相关的内存，并且停止与媒体服务器的任何交互。
     *        - 调用本方法会启动 SDK 退出逻辑。引擎线程会保留，直到退出逻辑完成。因此，不要在回调线程中直接调用此 API，会导致死锁。同时此方法是耗时操作，不建议在主线程调用本方法，避免主线程阻塞。
     * @list 引擎管理
     */
    /**
     * @locale en
     * @type api
     * @brief Destroy the engine instance created by createRTCEngine{@link #createRTCEngine}, and release all related resources.
     * @note
     *         - Call this API after all business scenarios related to the engine instance are destroyed. In a multi-thread scenario, you must not call IRTCEngine{@link #IRTCEngine} related APIs after calling this interface, or the SDK may crash. When the API is called, RTC SDK destroys all memory associated with the engine instance and stops any interaction with the media server.
     *         - Calling this API will start the SDK exit logic. The engine thread is held until the exit logic is complete. The engine thread is retained until the exit logic is complete. Therefore, do not call this API directly in the callback thread, or it will cause a deadlock. This function takes a long time to execute, so it's not recommended to call this API in the main thread, or the main thread may be blocked.
     * @list Engine Management
     */
    static BYTERTC_STATIC_API void destroyRTCEngine();
    /**
     * @locale zh
     * @hidden internal use only
     * @type api
     * @brief 销毁由 createRTCEngineMulti{@link #IRTCEngine#createRTCEngineMulti} 所创建的引擎实例，并释放所有相关资源。
     * @note
     *      - 请确保和需要销毁的 IRTCEngine{@link #IRTCEngine} 实例相关的业务场景全部结束后，才调用此方法
     *      - 该方法在调用之后，会销毁所有和此 IRTCEngine{@link #IRTCEngine} 实例相关的内存，并且停止与媒体服务器的任何交互
     *      - 调用本方法会启动 SDK 退出逻辑。引擎线程会保留，直到退出逻辑完成。因此，不要在回调线程中直接调用此 API，会导致死锁。同时此方法是耗时操作，不建议在主线程调用本方法，避免主线程阻塞。
     * @list 引擎管理
     */
    /**
     * @locale en
     * @hidden internal use only
     * @type api
     * @brief Destroy the engine instance created by createRTCEngineMulti{@link #IRTCEngine#createRTCEngineMulti}, and release all related resources.
     * @note
     *       - Call this API after all business scenarios related to the engine instance are destroyed.
     *       - When the API is called, RTC SDK destroys all memory associated with the engine instance and stops any interaction with the media server.
     *       - Calling this API will start the SDK exit logic. The engine thread is held until the exit logic is complete. The engine thread is retained until the exit logic is complete. Therefore, do not call this API directly in the callback thread, or it will cause a deadlock. This function takes a long time to execute, so it's not recommended to call this API in the main thread, or the main thread may be blocked.
     * @list Engine Management
     */
    static BYTERTC_STATIC_API void destroyRTCEngineMulti(IRTCEngine* instance_multi);
    /**
     * @locale zh
     * @type api
     * @brief 获取当前 SDK 版本信息。
     * @return 当前 SDK 版本信息。
     * @list 引擎管理
     */
    /**
     * @locale en
     * @type api
     * @brief  Get the current SDK version information.
     * @return Current SDK version information.
     * @list Engine Management
     */
    static BYTERTC_STATIC_API const char* getSDKVersion();
    /**
     * @locale zh
     * @type api
     * @brief 配置 SDK 本地日志参数，包括日志级别、存储路径、日志文件最大占用的总空间、日志文件名前缀。
     * @param log_config 本地日志参数，参看 LogConfig{@link #LogConfig}。
     * @return
     *        - 0：成功。
     *        - –1：失败，本方法必须在创建引擎前调用。
     *        - –2：失败，参数填写错误。
     * @note 本方法必须在调用 createRTCEngine{@link #createRTCEngine} 之前调用。
     * @list 引擎管理
     */
    /**
     * @locale en
     * @type api
     * @brief Configures the local log parameters of RTC SDK, including the logging level, directory, the limits for total log file size and the prefix of log.
     * @param log_config Local log parameters. See LogConfig{@link #LogConfig}.
     * @return
     *        - 0: Success.
     *        - –1: Failure. This API must be called before creating engine.
     *        - –2: Failure. Invalid parameters.
     * @note This API must be called before createRTCEngine{@link #createRTCEngine}.
     * @list Engine Management
     */
    static BYTERTC_STATIC_API int setLogConfig(const LogConfig& log_config);
};

}  // namespace bytertc
