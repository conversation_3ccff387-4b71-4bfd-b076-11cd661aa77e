# 音频工具测试

此目录包含对音频处理工具的测试代码。

## 测试内容

1. 解析 WAV 和 MP3 文件
2. 输出文件格式信息到文件
3. 将文件转换为 PCM 格式（16kHz 采样率、单声道、16 位深）

## 准备测试

在运行测试前，需要准备以下测试文件：

1. `samples/sample.wav` - WAV 格式音频样本
2. `samples/sample.mp3` - MP3 格式音频样本

您可以使用真实的音频文件，或者使用以下命令生成测试用的 WAV 文件（需要安装 sox 工具）：

```bash
# 生成WAV文件 - 3秒钟的440Hz正弦波
sox -n samples/sample.wav synth 3 sine 440

# 生成MP3文件 - 3秒钟的440Hz正弦波
sox -n samples/sample.mp3 synth 3 sine 440
```

## 运行测试

使用以下命令运行测试：

```bash
cd aigc_server
go test -v test/audio/...
```

测试结果将输出到控制台，转换后的 PCM 文件将保存在`test/audio/output`目录下。
