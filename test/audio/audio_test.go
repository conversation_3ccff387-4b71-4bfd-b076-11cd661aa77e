package audio

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"aigc_server/pkg/utils"
)

const (
	// 测试文件夹
	testDir       = "samples"
	outputDir     = "output"
	targetPCMPath = "output/resampled_16k_mono.pcm"
)

// TestAudioProcessor 测试音频处理器的解析和转换功能
func TestAudioProcessor(t *testing.T) {
	// 确保输出目录存在
	os.MkdirAll(outputDir, 0755)

	// 测试文件列表
	testFiles := []struct {
		path         string
		expectedType string
		description  string
	}{
		{"sample.wav", utils.WAVFormat, "WAV文件测试"},
		{"sample.mp3", utils.MP3Format, "MP3文件测试"},
	}

	for _, tf := range testFiles {
		t.Run(tf.description, func(t *testing.T) {
			// 读取测试文件
			filePath := filepath.Join(testDir, tf.path)
			data, err := os.ReadFile(filePath)
			if err != nil {
				t.Logf("跳过测试 %s: 文件未找到或无法读取: %v", filePath, err)
				t.Skip<PERSON>ow()
				return
			}

			// 解析文件
			processor, err := utils.NewAudioProcessor(data)
			if err != nil {
				t.Fatalf("无法创建音频处理器: %v", err)
			}

			// 获取音频信息
			info := processor.GetInfo()

			// 检查文件类型
			if info.Format != tf.expectedType {
				t.Errorf("文件类型错误: 期望 %s, 得到 %s", tf.expectedType, info.Format)
			}

			// 保存音频信息到文件
			infoFilePath := filepath.Join(outputDir, fmt.Sprintf("%s_info.txt", tf.path))
			infoContent := fmt.Sprintf(
				"文件: %s\n格式: %s\n采样率: %d Hz\n声道数: %d\n位深度: %d 位\n时长: %.2f 秒\n文件大小: %d 字节\n",
				tf.path, info.Format, info.SampleRate, info.Channels, info.BitDepth,
				info.Duration, info.FileSize,
			)

			err = os.WriteFile(infoFilePath, []byte(infoContent), 0644)
			if err != nil {
				t.Errorf("无法写入信息文件: %v", err)
			}

			// 解码为PCM
			pcmData, audioPCMFormat, err := processor.DecodeToPCM()
			if err != nil {
				t.Fatalf("解码为PCM失败: %v", err)
			}

			// 重采样为16kHz, 单声道
			srcFormat := audioPCMFormat

			dstFormat := utils.AudioPCMFormat{
				SampleRate: 16000, // 16kHz
				Channels:   1,     // 单声道
				BitDepth:   16,    // 16位
			}

			// 执行重采样
			resampledPCM, err := utils.ResamplePCM(pcmData, srcFormat, dstFormat)
			if err != nil {
				t.Fatalf("重采样失败: %v", err)
			}

			// 输出重采样后的PCM到文件
			outputPath := filepath.Join(outputDir, fmt.Sprintf("%s_16k_mono.pcm", tf.path))
			err = os.WriteFile(outputPath, resampledPCM, 0644)
			if err != nil {
				t.Errorf("保存PCM文件失败: %v", err)
			}

			t.Logf("成功处理: %s", tf.path)
			t.Logf("  - 格式: %s", info.Format)
			t.Logf("  - 采样率: %d Hz", info.SampleRate)
			t.Logf("  - 声道数: %d", info.Channels)
			t.Logf("  - 已保存PCM到: %s", outputPath)
		})
	}
}

// TestAudioProcessor_ResamplePCM 单独测试PCM重采样功能
func TestAudioProcessor_ResamplePCM(t *testing.T) {
	// 确保输出目录存在
	os.MkdirAll(outputDir, 0755)

	// 生成测试用PCM数据（1000Hz正弦波，44.1kHz，立体声，16位，1秒）
	originalPCM := generateTestTone(1000, 44100, 2, 16, 1.0)

	// 原始PCM格式
	srcFormat := utils.AudioPCMFormat{
		SampleRate: 44100,
		Channels:   2,
		BitDepth:   16,
	}

	// 目标PCM格式（16kHz，单声道，16位）
	dstFormat := utils.AudioPCMFormat{
		SampleRate: 16000,
		Channels:   1,
		BitDepth:   16,
	}

	// 执行重采样
	resampledPCM, err := utils.ResamplePCM(originalPCM, srcFormat, dstFormat)
	if err != nil {
		t.Fatalf("重采样失败: %v", err)
	}

	// 保存重采样后的PCM到文件
	err = os.WriteFile(targetPCMPath, resampledPCM, 0644)
	if err != nil {
		t.Fatalf("无法保存重采样PCM文件: %v", err)
	}

	// 获取重采样前后的大小，验证结果
	originalSize := len(originalPCM)
	resampledSize := len(resampledPCM)
	// 计算理论大小比例: 新采样率/旧采样率 * 新声道数/旧声道数 * 新位深度/旧位深度
	expectedRatio := float64(dstFormat.SampleRate) / float64(srcFormat.SampleRate) *
		float64(dstFormat.Channels) / float64(srcFormat.Channels) *
		float64(dstFormat.BitDepth) / float64(srcFormat.BitDepth)

	actualRatio := float64(resampledSize) / float64(originalSize)
	t.Logf("重采样完成:")
	t.Logf("  - 原始大小: %d 字节", originalSize)
	t.Logf("  - 重采样后大小: %d 字节", resampledSize)
	t.Logf("  - 理论比例: %.4f, 实际比例: %.4f", expectedRatio, actualRatio)
	t.Logf("  - 已保存到: %s", targetPCMPath)

	// 简单验证：重采样后的大小应该接近理论值（考虑到对齐和舍入误差）
	if actualRatio < expectedRatio*0.9 || actualRatio > expectedRatio*1.1 {
		t.Errorf("重采样后大小与理论值偏差过大: 理论比例 %.4f, 实际比例 %.4f", expectedRatio, actualRatio)
	}
}

// 生成测试音频数据（正弦波）
func generateTestTone(frequency, sampleRate, channels, bitDepth int, duration float64) []byte {
	samplesPerChannel := int(float64(sampleRate) * duration)
	bytesPerSample := bitDepth / 8
	totalBytes := samplesPerChannel * channels * bytesPerSample

	result := make([]byte, totalBytes)

	// 生成16位正弦波
	if bitDepth == 16 {
		for i := 0; i < samplesPerChannel; i++ {
			// 计算正弦波值（-1.0 到 1.0）
			t := float64(i) / float64(sampleRate)
			value := sin(2.0 * 3.14159265359 * float64(frequency) * t)

			// 转换为16位整数（-32768 到 32767）
			sample := int16(value * 32767.0)

			// 写入每个声道
			for ch := 0; ch < channels; ch++ {
				offset := (i*channels + ch) * bytesPerSample
				result[offset] = byte(sample)
				result[offset+1] = byte(sample >> 8)
			}
		}
	}

	return result
}

// 简单的正弦函数实现
func sin(x float64) float64 {
	// 使用泰勒级数近似计算
	const (
		B = 4.0 / 3.14159265359
		C = -4.0 / (3.14159265359 * 3.14159265359)
	)

	// 将x映射到[-π,π]范围
	for x > 3.14159265359 {
		x -= 2 * 3.14159265359
	}
	for x < -3.14159265359 {
		x += 2 * 3.14159265359
	}

	// 近似计算
	return B*x + C*x*abs(x)
}

// 绝对值函数
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}
