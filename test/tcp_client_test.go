package main

import (
	"encoding/binary"
	"encoding/json"
	"fmt"
	"net"
	"time"
)

// 消息类型定义
type DollEnterRoomRequest struct {
	DollId string `json:"dollId"`
}

type DollEnterRoomResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		DollId    string `json:"dollId"`
		RoomId    string `json:"roomId"`
		RoomToken string `json:"roomToken"`
	} `json:"data"`
}

// Pack 打包数据
func Pack(body []byte) []byte {
	header := make([]byte, 8)
	header[0] = 0xAB
	header[1] = 0xAB
	header[2] = 0xAB
	
	// 计算异或校验
	var xor byte
	for _, b := range body {
		xor ^= b
	}
	header[3] = xor
	
	// 写入长度，小端
	bodyLen := len(body)
	header[4] = byte(bodyLen & 0xFF)
	header[5] = byte((bodyLen >> 8) & 0xFF)
	header[6] = byte((bodyLen >> 16) & 0xFF)
	header[7] = byte((bodyLen >> 24) & 0xFF)
	
	return append(header, body...)
}

// ReadPacket 读取数据包
func ReadPacket(conn net.Conn) ([]byte, error) {
	// 读取头部
	header := make([]byte, 8)
	_, err := conn.Read(header)
	if err != nil {
		return nil, err
	}
	
	// 检查头部
	if header[0] != 0xAB || header[1] != 0xAB || header[2] != 0xAB {
		return nil, fmt.Errorf("包头错误")
	}
	
	// 读取长度
	bodyLen := int(binary.LittleEndian.Uint32(header[4:8]))
	
	// 读取body
	body := make([]byte, bodyLen)
	_, err = conn.Read(body)
	if err != nil {
		return nil, err
	}
	
	// 校验异或
	var xor byte
	for _, b := range body {
		xor ^= b
	}
	if xor != header[3] {
		return nil, fmt.Errorf("异或校验失败")
	}
	
	return body, nil
}

// SendJSON 发送JSON数据
func SendJSON(conn net.Conn, v interface{}) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}
	packet := Pack(b)
	_, err = conn.Write(packet)
	return err
}

func main() {
	// 连接到TCP服务器
	conn, err := net.Dial("tcp", "localhost:8970")
	if err != nil {
		fmt.Printf("连接失败: %v\n", err)
		return
	}
	defer conn.Close()
	
	fmt.Println("已连接到TCP服务器")
	
	// 发送进入房间请求
	req := DollEnterRoomRequest{
		DollId: "test_doll_001",
	}
	
	fmt.Printf("发送进入房间请求: %+v\n", req)
	err = SendJSON(conn, req)
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		return
	}
	
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(10 * time.Second))
	
	// 读取响应
	respData, err := ReadPacket(conn)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}
	
	// 解析响应
	var resp DollEnterRoomResponse
	err = json.Unmarshal(respData, &resp)
	if err != nil {
		fmt.Printf("解析响应失败: %v\n", err)
		return
	}
	
	fmt.Printf("收到响应: %+v\n", resp)
	
	if resp.Code == 0 {
		fmt.Println("✅ TCP服务器测试成功！")
		fmt.Printf("房间ID: %s\n", resp.Data.RoomId)
		fmt.Printf("房间Token: %s\n", resp.Data.RoomToken)
	} else {
		fmt.Printf("❌ 服务器返回错误: %s\n", resp.Message)
	}
}
