package tcpserver

import (
	"biz_server/config"
	"biz_server/internal/service"
	"biz_server/pkg/log"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"os"
	"path/filepath"
	"strconv"
	"sync/atomic"
	"time"
)

const (
	maxConnections = 1000
	readTimeout    = 30 * time.Second
	writeTimeout   = 30 * time.Second
)

var (
	activeConnections int32
	remainingData     []byte
)

type DollEnterRoomRequest struct {
	DollId string `json:"dollId"`
}

type DollSettingsRequest struct {
	DollId  string `json:"dollId"`
	Battery string `json:"battery"`
	Volume  string `json:"volume"`
}

type DollEnterRoomResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		DollId    string `json:"dollId"`
		RoomId    string `json:"roomId"`
		RoomToken string `json:"roomToken"`
	} `json:"data"`
}

type DollSettingsResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Volume string `json:"volume"`
	} `json:"data"`
}

type DollInterruptRequest struct {
	DollId    string `json:"dollId"`
	Interrupt bool   `json:"interrupt"`
}

type DollInterruptResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Interrupt bool `json:"interrupt"`
	} `json:"data"`
}

// 写入标准 WAV 文件头
func writeWav(pcm []byte, filename string) {
	// 确保目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		log.Infof("创建目录失败: %v\n", err)
		return
	}

	var header [44]byte
	totalLen := 36 + len(pcm)

	// ChunkID "RIFF"
	copy(header[0:], []byte("RIFF"))
	binary.LittleEndian.PutUint32(header[4:], uint32(totalLen))
	copy(header[8:], []byte("WAVE"))

	// Subchunk1ID "fmt "
	copy(header[12:], []byte("fmt "))
	binary.LittleEndian.PutUint32(header[16:], 16)    // Subchunk1Size
	binary.LittleEndian.PutUint16(header[20:], 1)     // AudioFormat PCM
	binary.LittleEndian.PutUint16(header[22:], 1)     // NumChannels
	binary.LittleEndian.PutUint32(header[24:], 8000)  // SampleRate
	binary.LittleEndian.PutUint32(header[28:], 16000) // ByteRate
	binary.LittleEndian.PutUint16(header[32:], 2)     // BlockAlign
	binary.LittleEndian.PutUint16(header[34:], 16)    // BitsPerSample

	// Subchunk2ID "data"
	copy(header[36:], []byte("data"))
	binary.LittleEndian.PutUint32(header[40:], uint32(len(pcm))) // Subchunk2Size

	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Infof("保存音频失败:", err)
		return
	}
	defer file.Close()
	file.Write(header[:])
	file.Write(pcm)
}

func handleDollSettings(conn net.Conn, req DollSettingsRequest) {
	resp := DollSettingsResponse{
		Code:    0,
		Message: "success",
	}
	// var doll model.Doll
	// plugin.GetDB().Model(&model.Doll{}).Where("doll_id = ?", req.DollId).First(&doll)
	// resp.Data.Volume = strconv.Itoa(doll.Volume)
	volume, _ := strconv.Atoi(req.Volume)
	resp.Data.Volume = strconv.Itoa((volume + 10) % 100)
	log.Infof("[tcpserver] 设置娃娃音量 dollId: %s, %d", req.DollId, volume)
	sendJSON(conn, resp)
}

func handleDollInterrupt(conn net.Conn, req DollInterruptRequest) {
	log.Infof("[tcpserver] 打断 dollId: %s, %t", req.DollId, req.Interrupt)
	resp := DollInterruptResponse{
		Code:    0,
		Message: "success",
	}
	resp.Data.Interrupt = req.Interrupt
	sendJSON(conn, resp)
}

func handleDollEnterRoom(conn net.Conn, req DollEnterRoomRequest) {
	// 调用 aigc 服务获取房间信息
	aigcResp, err := service.GetAIGCService().GetRoom(req.DollId)
	if err != nil {
		log.Errorf("调用 AIGC 服务失败: %v", err)
		sendJSON(conn, map[string]interface{}{"code": 1, "message": "获取房间信息失败"})
		return
	}

	log.Infof("[tcpserver] AIGC 服务返回房间信息 dollId: %s, %+v", req.DollId, aigcResp)
	resp := DollEnterRoomResponse{
		Code:    0,
		Message: "success",
	}
	resp.Data.DollId = req.DollId
	resp.Data.RoomId = aigcResp.RoomID
	resp.Data.RoomToken = aigcResp.RoomToken
	sendJSON(conn, resp)
}

var audio_bytes []byte
var audio_recv_time time.Time
var audio_count int
var is_audio_saved bool = true
var SAVE_AUDIO = false

func handleConn(conn net.Conn) {
	// 限制最大连接数
	if atomic.LoadInt32(&activeConnections) >= maxConnections {
		log.Warnf("达到最大连接数限制，拒绝新连接")
		conn.Close()
		return
	}
	atomic.AddInt32(&activeConnections, 1)
	defer atomic.AddInt32(&activeConnections, -1)

	go func() {
		for {
			if !SAVE_AUDIO {
				return
			}
			time.Sleep(1 * time.Second)
			if !is_audio_saved && time.Since(audio_recv_time) > 300*time.Millisecond {
				// 保存为wav
				filename := fmt.Sprintf("./audio/audio-%d.wav", time.Now().Unix())
				// 使用 8kHz 采样率，16bit，单声道
				log.Infof("总接收数据长度: %d 字节\n", len(audio_bytes))
				log.Infof("理论音频长度: %.2f 秒\n", float64(len(audio_bytes))/(8000*2))
				writeWav(audio_bytes, filename)
				log.Infof("音频已保存: %s", filename)
				audio_bytes = []byte{}
				is_audio_saved = true
			}
		}
	}()

	// 设置读写超时
	// conn.SetReadDeadline(time.Now().Add(readTimeout))
	// conn.SetWriteDeadline(time.Now().Add(writeTimeout))

	defer conn.Close()
	remoteAddr := conn.RemoteAddr().String()
	log.Infof("新的连接建立: %s, 当前活跃连接数: %d", remoteAddr, atomic.LoadInt32(&activeConnections))

	for {
		packet, err, is_audio := ReadPacket(conn)

		if err != nil {
			if err == io.EOF {
				log.Infof("客户端 %s 主动断开连接", remoteAddr)
				return
			} else if nerr, ok := err.(net.Error); ok && nerr.Timeout() {
				log.Warnf("客户端 %s 连接超时", remoteAddr)
			} else {
				log.Errorf("客户端 %s 读取错误: %v", remoteAddr, err)
			}
			// return
		}

		// 更新写入超时
		// conn.SetWriteDeadline(time.Now().Add(writeTimeout))

		if is_audio {
			audio_recv_time = time.Now()
			audio_bytes = append(audio_bytes, packet...)
			audio_count++
			log.Infof("***** 收到音频数据长度: %d, 总长度: %d, 次数: %d", len(packet), len(audio_bytes), audio_count)
			is_audio_saved = false
			continue
		}

		// 先尝试解析为 DollSetRequest
		var setReq DollSettingsRequest
		if err := json.Unmarshal(packet, &setReq); err == nil && setReq.DollId != "" && setReq.Battery != "" && setReq.Volume != "" {
			handleDollSettings(conn, setReq)
			continue
		}

		// 语音打断
		var interruptReq DollInterruptRequest
		if err := json.Unmarshal(packet, &interruptReq); err == nil && interruptReq.DollId != "" && interruptReq.Interrupt != false {
			handleDollInterrupt(conn, interruptReq)
			continue
		}

		// DollEnterRoomRequest
		var enterRoomReq DollEnterRoomRequest
		if err := json.Unmarshal(packet, &enterRoomReq); err == nil && enterRoomReq.DollId != "" {
			handleDollEnterRoom(conn, enterRoomReq)
			continue
		}

		// 解析失败
		sendJSON(conn, map[string]interface{}{"code": 1, "message": "invalid request"})
	}
}

func sendJSON(conn net.Conn, v interface{}) {
	b, _ := json.Marshal(v)
	packet := Pack(b)
	conn.Write(packet)
}

func Pack(body []byte) []byte {
	header := make([]byte, 8)
	header[0] = 0xAB
	header[1] = 0xAB
	header[2] = 0xAB
	// 计算异或校验
	var xor byte
	for _, b := range body {
		xor ^= b
	}
	header[3] = xor
	// 写入长度，小端
	bodyLen := len(body)
	header[4] = byte(bodyLen & 0xFF)
	header[5] = byte((bodyLen >> 8) & 0xFF)
	header[6] = byte((bodyLen >> 16) & 0xFF)
	header[7] = byte((bodyLen >> 24) & 0xFF)
	return append(header, body...)
}

func ReadPacket(conn net.Conn) ([]byte, error, bool) {
	buf := make([]byte, 0, 4096)
	tmp := make([]byte, 4096)

	for {
		// 如果有剩余数据，先使用剩余数据
		if len(remainingData) > 0 {
			buf = append(buf, remainingData...)
			remainingData = []byte{}
		}

		// 先保证有8字节头
		for len(buf) < 8 {
			n, err := conn.Read(tmp)
			if err != nil {
				return nil, err, false
			}
			buf = append(buf, tmp[:n]...)
		}

		// 如果前面 8 个是 0xBB 就是音频数据
		is_audio := false
		if buf[0] == 0xBB && buf[1] == 0xBB && buf[2] == 0xBB && buf[3] == 0xBB {
			is_audio = true
		}

		// 检查头部
		if (buf[0] != 0xAB || buf[1] != 0xAB || buf[2] != 0xAB) && !is_audio {
			return nil, errors.New("包头错误"), false
		}

		xorVal := buf[3]
		bodyLen := int(binary.LittleEndian.Uint32(buf[4:8]))

		// 继续读body
		for len(buf) < 8+bodyLen {
			n, err := conn.Read(tmp)
			if err != nil {
				return nil, err, false
			}
			buf = append(buf, tmp[:n]...)
		}

		body := buf[8 : 8+bodyLen]
		// 校验异或
		var xor byte
		for _, b := range body {
			xor ^= b
		}
		if xor != xorVal && !is_audio {
			return nil, errors.New("异或校验失败"), false
		}

		// 拆出本包
		packet := make([]byte, bodyLen)
		copy(packet, body)

		// 保存剩余数据
		if len(buf) > 8+bodyLen {
			remainingData = make([]byte, len(buf)-8-bodyLen)
			copy(remainingData, buf[8+bodyLen:])
		}

		return packet, nil, is_audio
	}
}

func StartTCPServer(addr string, cfg *config.ExternalServiceConfig) {
	ln, err := net.Listen("tcp", addr)
	if err != nil {
		panic(err)
	}
	log.Infof("TCP服务已启动，监听：%s", addr)

	// 启动监控协程
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		for range ticker.C {
			active := atomic.LoadInt32(&activeConnections)
			log.Infof("TCP服务状态 - 当前活跃连接数: %d", active)
		}
	}()

	for {
		conn, err := ln.Accept()
		if err != nil {
			log.Errorf("接受连接失败: %v", err)
			continue
		}
		go handleConn(conn)
	}
}
